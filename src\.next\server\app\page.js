(()=>{var e={};e.id=974,e.ids=[974],e.modules={597:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\github\\\\alltomarkdown_frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5600:(e,t,s)=>{Promise.resolve().then(s.bind(s,6527))},6055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(1658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6527:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(687),l=s(5814),a=s.n(l),n=s(6457);function i(){let{t:e}=(0,n.Bd)(["common","home"]);return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsx)("div",{className:"bg-white",children:(0,r.jsxs)("div",{className:"relative isolate px-6 pt-6 lg:px-8",children:[(0,r.jsx)("div",{className:"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80",children:(0,r.jsx)("div",{className:"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-200 to-blue-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"})}),(0,r.jsx)("div",{className:"mx-auto max-w-2xl py-32 sm:py-48 lg:py-56",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl",children:e("home:title")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:e("home:description")}),(0,r.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-2",children:[(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"PDF"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Word (DOC/DOCX)"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Excel (XLS/XLSX)"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"HTML"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Images"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Websites"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"URLs"}),(0,r.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Text Files"})]}),(0,r.jsxs)("div",{className:"mt-10 flex flex-col items-center justify-center",children:[(0,r.jsx)(a(),{href:"/convert",className:"rounded-lg bg-blue-600 px-10 py-5 text-xl font-bold text-white shadow-md hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-all hover:shadow-lg hover:translate-y-[-2px] active:translate-y-[1px] w-80 text-center",children:e("home:startConverting")}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(a(),{href:"/pricing",className:"text-xs text-gray-400 hover:text-blue-500 transition-colors",children:[e("home:viewPricing")," ",(0,r.jsx)("span",{"aria-hidden":"true",children:"→"})]})})]})]})}),(0,r.jsx)("div",{className:"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]",children:(0,r.jsx)("div",{className:"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-200 to-blue-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"})})]})}),(0,r.jsx)("div",{className:"bg-blue-50 py-16 sm:py-24",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[(0,r.jsx)("h2",{className:"text-base font-semibold leading-7 text-blue-600",children:"AI-Friendly Format"}),(0,r.jsx)("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:e("home:aiTitle")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:e("home:aiDescription")})]}),(0,r.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl",children:(0,r.jsxs)("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-3 lg:gap-y-16",children:[(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"})})}),e("home:clearStructure")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:clearStructureDesc")})]}),(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z"})})}),e("home:enhancedResponses")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:enhancedResponsesDesc")})]}),(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"})})}),e("home:noFormatting")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:noFormattingDesc")})]})]})})]})}),(0,r.jsx)("div",{className:"bg-white py-24 sm:py-32",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl lg:text-center",children:[(0,r.jsx)("h2",{className:"text-base font-semibold leading-7 text-blue-600",children:"Convert Any Format"}),(0,r.jsx)("p",{className:"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",children:e("home:convertAnyFormat")}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-gray-600",children:e("home:convertDesc")})]}),(0,r.jsxs)("div",{className:"mx-auto mt-10 max-w-2xl",children:[(0,r.jsx)("h3",{className:"text-center text-lg font-semibold leading-8 text-gray-900",children:e("home:supportedFormats")}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-red-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"PDF"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-blue-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"Word"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"DOC, DOCX"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-green-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0112 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H11.25a9.06 9.06 0 00-1.5.124m7.5 10.376h3.375c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M15 12a3 3 0 11-6 0 3 3 0 016 0z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"Excel"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"XLS, XLSX"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-purple-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"HTML"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-yellow-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"Images"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"JPG, PNG, etc."})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-indigo-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"Websites"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"URLs"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-gray-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"Text Files"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"TXT, RTF"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("svg",{className:"h-8 w-8 text-orange-500",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.429 9.75L2.25 12l4.179 2.25m0-4.5l5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0l4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0l-5.571 3-5.571-3"})}),(0,r.jsx)("span",{className:"mt-2 text-sm font-medium",children:"And More"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Many formats"})]})]})]}),(0,r.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl",children:(0,r.jsxs)("dl",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16",children:[(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z"})})}),e("home:easyUpload")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:easyUploadDesc")})]}),(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"})})}),e("home:advancedOptions")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:advancedOptionsDesc")})]}),(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z"})})}),e("home:batchProcessing")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:batchProcessingDesc")})]}),(0,r.jsxs)("div",{className:"relative pl-16",children:[(0,r.jsxs)("dt",{className:"text-base font-semibold leading-7 text-gray-900",children:[(0,r.jsx)("div",{className:"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"})})}),e("home:fastConversion")]}),(0,r.jsx)("dd",{className:"mt-2 text-base leading-7 text-gray-600",children:e("home:fastConversionDesc")})]})]})})]})})]})}},7910:e=>{"use strict";e.exports=require("stream")},8752:(e,t,s)=>{Promise.resolve().then(s.bind(s,597))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9209:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(5239),l=s(8088),a=s(8170),n=s.n(a),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,597)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,373,658,522],()=>s(9209));module.exports=r})();