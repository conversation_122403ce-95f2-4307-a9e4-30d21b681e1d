import { createClient, SupabaseClient } from '@supabase/supabase-js';

let supabaseInstance: SupabaseClient | null = null;

export const getSupabaseClient = (): SupabaseClient => {
  if (supabaseInstance) {
    return supabaseInstance;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration.");
    throw new Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");
  }
  if (!supabaseAnonKey) {
    console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration.");
    throw new Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");
  }

  // 创建 Supabase 客户端并启用 debug 模式
  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      debug: true, // 启用认证模块的调试日志
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    global: {
      fetch: async (url, options) => {
        // 记录所有请求的详细信息
        console.log(`[Supabase Debug] Fetch request to: ${url}`);
        console.log(`[Supabase Debug] Fetch options:`, options);

        try {
          // 使用原生 fetch 发送请求
          const response = await fetch(url, options);
          console.log(`[Supabase Debug] Response status: ${response.status}`);
          return response;
        } catch (error) {
          console.error(`[Supabase Debug] Fetch error:`, error);
          throw error;
        }
      }
    }
  });

  // 添加额外的调试日志
  console.log('[Supabase Debug] Client initialized with URL:', supabaseUrl);

  return supabaseInstance;
};

// Optional: For components that might still expect a direct export,
// you could provide a version that tries to initialize immediately,
// but this might re-introduce the problem in some contexts.
// It's generally better to update consuming code to use the function.

// export const supabase = getSupabaseClient(); // Avoid this if it causes issues during build
