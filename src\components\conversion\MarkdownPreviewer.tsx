import React, { useState, useEffect } from 'react';
import DOMPurify from 'dompurify';

interface MarkdownPreviewerProps {
  markdownContent: string;
  fileName: string;
  onClose?: () => void;
}

const MarkdownPreviewer: React.FC<MarkdownPreviewerProps> = ({
  markdownContent,
  fileName,
  onClose,
}) => {
  const [htmlContent, setHtmlContent] = useState<string>('');

  useEffect(() => {
    // In a real implementation, we would use a Markdown parser library
    // like marked or markdown-it to convert Markdown to HTML
    // For this example, we'll simulate it with a simple conversion
    const simulateMarkdownToHtml = (markdown: string): string => {
      // This is a very simplified example
      // In a real app, use a proper Markdown parser
      const html = markdown
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/\*\*(.*)\*\*/gm, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gm, '<em>$1</em>')
        .replace(/\n/gm, '<br>');
      
      return html;
    };

    // Convert markdown to HTML
    const rawHtml = simulateMarkdownToHtml(markdownContent);
    
    // Sanitize HTML to prevent XSS attacks
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      USE_PROFILES: { html: true },
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'hr',
        'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
        'em', 'strong', 'del', 'a', 'img', 'table',
        'thead', 'tbody', 'tr', 'th', 'td'
      ],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'title']
    });

    setHtmlContent(sanitizedHtml);
  }, [markdownContent]);

  if (!markdownContent) {
    return null;
  }

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg fixed inset-0 z-50 overflow-auto flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white shadow-xl sm:rounded-lg w-full max-w-3xl max-h-[90vh] flex flex-col">
        <div className="px-4 py-5 sm:p-6 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium leading-6 text-gray-900">
            Preview: {fileName}
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Close preview"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        <div className="p-4 sm:p-6 overflow-y-auto">
          <div className="prose prose-sm max-w-none">
            <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownPreviewer;
