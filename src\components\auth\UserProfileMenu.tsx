'use client';

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Cog6ToothIcon } from '@heroicons/react/20/solid'; // Removed UserIcon
import { getSupabaseClient } from '../../lib/supabaseClient';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link'; // Import Link
import { getLocalAuthToken } from '../../hooks/useAuth'; // Import the function to get local JWT

// Define a CSS keyframes animation for the avatar loading pulse
const avatarPulseAnimation = `
@keyframes avatarPulse {
  0% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
  50% {
    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */
  }
  100% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
}

.avatar-pulse {
  animation: avatarPulse 1.5s ease-in-out infinite;
}

.dark .avatar-pulse {
  animation-name: avatarPulseDark;
}

@keyframes avatarPulseDark {
  0% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
  50% {
    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */
  }
  100% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
}
`;

interface SupabaseUser { // Renamed to avoid conflict with localUser
  email?: string | null;
  user_metadata?: {
    name?: string;
    full_name?: string; // From some OAuth providers
    avatar_url?: string;
    picture?: string; // Common field from OAuth providers for avatar
  };
}

// Interface for the user data fetched from your backend /api/v1/users/me
interface LocalUser {
  id: string;
  email: string;
  username: string;
  full_name: string | null;
  is_active: boolean;
  is_superuser: boolean;
  tier: string; // e.g., "free", "basic_paid"
  monthly_conversion_limit: number;
  max_file_size_bytes: number;
  conversions_this_month: number;
  last_conversion_reset: string | null;
  avatar_url?: string; // Added avatar_url to LocalUser
}

interface UserProfileMenuProps {
  user: SupabaseUser | null; // Prop can now be SupabaseUser or null
}

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({ user: supabaseUser }) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const supabase = getSupabaseClient();

  const [localUser, setLocalUser] = useState<LocalUser | null>(null);
  const [isLoadingLocalUser, setIsLoadingLocalUser] = useState(false);
  const [localUserError, setLocalUserError] = useState<string | null>(null);
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark' | 'system'>('system');

  // Add the avatar pulse animation styles to the document
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = avatarPulseAnimation;

    // Add it to the document head
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Initialize theme based on localStorage or system preference
  useEffect(() => {
    // Check if theme is stored in localStorage
    const storedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system' | null;

    // Update current theme state
    if (storedTheme) {
      setCurrentTheme(storedTheme as 'light' | 'dark' | 'system');
    }

    const applyTheme = () => {
      if (storedTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else if (storedTheme === 'light') {
        document.documentElement.classList.remove('dark');
      } else if (storedTheme === 'system' || !storedTheme) {
        // Use system preference if set to 'system' or not set
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }

        // If not set, default to 'system'
        if (!storedTheme) {
          localStorage.setItem('theme', 'system');
          setCurrentTheme('system');
        }
      }
    };

    // Apply theme initially
    applyTheme();

    // Listen for system theme changes if theme is set to 'system'
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      if (localStorage.getItem('theme') === 'system') {
        if (e.matches) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
    };

    // Add listener for system theme changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  // Fetch user data from backend
  useEffect(() => {
    const fetchLocalUserData = async () => {
      const token = getLocalAuthToken();
      if (!token) {
        // No local token, likely means user is not fully authenticated with backend yet or session expired
        // console.log('No local JWT found, skipping fetch of /users/me');
        setIsLoadingLocalUser(false); // Make sure to set loading to false if we're not fetching
        return;
      }

      setIsLoadingLocalUser(true); // Start loading
      setLocalUserError(null);

      try {
        // Simulate a minimum loading time of 1.5 seconds for better UX and to ensure animation is visible
        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 1500));

        const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL
          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/me`
          : '/api/v1/users/me';

        const fetchPromise = fetch(apiUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        // Wait for both the minimum loading time and the fetch to complete
        const [response] = await Promise.all([fetchPromise, minLoadingTime]);

        if (response.ok) {
          const data: LocalUser = await response.json();
          setLocalUser(data);
        } else {
          const errorData = await response.json();
          console.error('Failed to fetch local user data:', response.status, errorData);
          setLocalUserError(errorData.detail || `Error ${response.status}`);
          // If unauthorized, perhaps clear local token?
          if (response.status === 401 && typeof window !== 'undefined') {
            localStorage.removeItem('local_jwt_token'); // Assuming 'local_jwt_token' is the key
            // Optionally, trigger a re-evaluation of auth state if you have a global context
          }
        }
      } catch (error) {
        console.error('Error fetching local user data:', error);
        setLocalUserError(t('errorFetchingUserData', 'Failed to load user details.'));
      } finally {
        setIsLoadingLocalUser(false); // End loading
      }
    };

    if (supabaseUser?.email) { // Fetch only if Supabase user exists
      fetchLocalUserData();
    } else {
      setIsLoadingLocalUser(false); // Make sure loading is false if we don't have a Supabase user
    }
  }, [supabaseUser, t]);

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      if (typeof window !== 'undefined') {
        localStorage.removeItem('local_jwt_token'); // Ensure local JWT is cleared on sign out
      }
      router.push('/');
      router.refresh(); // Or use a state management solution to update UI
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Determine display values based on available data
  // console.log('UserProfileMenu: supabaseUser prop:', supabaseUser); // For debugging
  // console.log('UserProfileMenu: localUser state:', localUser); // For debugging

  let displayName = t('user');
  let displayEmail: string | null | undefined = null;
  let avatarUrl: string | null | undefined = null;

  if (supabaseUser) {
    displayName = supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name || supabaseUser.email || t('user');
    displayEmail = supabaseUser.email;
    avatarUrl = supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture;
  }

  if (localUserError) {
    displayName = t('error', 'Error');
    // Potentially clear email/avatar if error state means we don't trust any user data
    displayEmail = null;
    avatarUrl = null;
  } else if (localUser) {
    // Prioritize localUser data if available
    displayName = localUser.full_name || localUser.username || (supabaseUser ? (supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name) : '') || localUser.email;
    displayEmail = localUser.email;
    // If localUser has avatar_url, use it. Otherwise, fallback to supabaseUser's avatar if localUser doesn't override it.
    avatarUrl = localUser.avatar_url || (supabaseUser ? (supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture) : null);
  }
  // console.log('UserProfileMenu: final displayName:', displayName, 'final avatarUrl:', avatarUrl); // For debugging

  return (
    <div className="relative inline-block text-left group">
      {/* Avatar button with loading animation - completely replaced when loading */}
      {isLoadingLocalUser ? (
        <div className="flex items-center justify-center rounded-full focus:outline-none h-10 w-10 cursor-pointer">
          <span className="sr-only">{t('loadingUserMenu', 'Loading user menu')}</span>
          <div className="h-10 w-10 rounded-full avatar-pulse"></div>
        </div>
      ) : !supabaseUser && !localUser ? (
        <div className="flex items-center space-x-2">
          <Link
            href="/login"
            className="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-blue-400"
          >
            {t('login', 'Login')}
          </Link>
          <button
            onClick={() => router.push('/register')}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {t('register', 'Register')}
          </button>
        </div>
      ) : (
        <div className="flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none transition-colors h-10 w-10 cursor-pointer dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
          <span className="sr-only">{t('openUserMenu', 'Open user menu')}</span>
          {avatarUrl ? (
            <div className="relative h-10 w-10 rounded-full overflow-hidden">
              <Image
                src={avatarUrl}
                alt={displayName}
                fill
                sizes="40px"
                style={{ objectFit: 'cover' }}
                priority
              />
            </div>
          ) : (
            <div className="h-10 w-10 rounded-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-600 dark:text-gray-300">
                <path d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z"
                  stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20"
                  stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </div>
          )}
        </div>
      )}

      {/* Dropdown menu - shown on hover with modern styling, only if user is logged in */}
      {!isLoadingLocalUser && (supabaseUser || localUser) && (
        <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-150"
             style={{ marginTop: '0.5rem' }}>
        {/* Invisible bridge to extend hover area */}
        <div className="absolute -top-2 right-0 w-16 h-2 bg-transparent"></div>

        {/* User info section */}
        <div className="px-4 py-3 border-b border-gray-100">
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-3">
              {isLoadingLocalUser ? (
                <div className="h-8 w-8 rounded-full avatar-pulse"></div>
              ) : avatarUrl ? (
                <div className="relative h-8 w-8 rounded-full overflow-hidden">
                  <Image
                    src={avatarUrl}
                    alt={displayName}
                    fill
                    sizes="32px"
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              ) : (
                <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-600 dark:text-gray-300">
                    <path d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z"
                      stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20"
                      stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate" title={displayName}>{displayName}</p>
              {displayEmail && <p className="text-xs text-gray-500 truncate" title={displayEmail}>{displayEmail}</p>}
            </div>
          </div>
        </div>

        {/* Menu items */}
        <div className="py-1">
          <a
            href="/account"
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <svg className="mr-3 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            {t('accountPreferences', 'Account Preferences')}
          </a>

          <a
            href="/settings"
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Cog6ToothIcon
              className="mr-3 h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
            {t('settings', 'Settings')}
          </a>
        </div>

        {/* Logout button */}
        <div className="py-1 border-t border-gray-100">
          <button
            onClick={handleSignOut}
            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <svg className="mr-3 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16 17 21 12 16 7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            {t('logout', 'Sign out')}
          </button>
        </div>

        {/* Theme selector - moved to bottom */}
        <div className="py-1 border-t border-gray-100">
          <div className="px-4 py-2">
            <p className="text-xs font-medium text-gray-500 mb-2">{t('theme', 'Theme')}</p>
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  document.documentElement.classList.remove('dark');
                  localStorage.setItem('theme', 'light');
                  setCurrentTheme('light');
                }}
                className={`p-2 rounded-md bg-white border ${currentTheme === 'light' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'} hover:bg-gray-50`}
                aria-label={t('lightTheme', 'Light theme')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${currentTheme === 'light' ? 'text-blue-600' : 'text-gray-700'}`} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="5"></circle>
                  <line x1="12" y1="1" x2="12" y2="3"></line>
                  <line x1="12" y1="21" x2="12" y2="23"></line>
                  <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                  <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                  <line x1="1" y1="12" x2="3" y2="12"></line>
                  <line x1="21" y1="12" x2="23" y2="12"></line>
                  <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                  <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
              </button>
              <button
                onClick={() => {
                  document.documentElement.classList.add('dark');
                  localStorage.setItem('theme', 'dark');
                  setCurrentTheme('dark');
                }}
                className={`p-2 rounded-md bg-gray-900 border ${currentTheme === 'dark' ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-50' : 'border-gray-700'} hover:bg-gray-800`}
                aria-label={t('darkTheme', 'Dark theme')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${currentTheme === 'dark' ? 'text-blue-400' : 'text-gray-200'}`} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
              </button>
              <button
                onClick={() => {
                  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }
                  localStorage.setItem('theme', 'system');
                  setCurrentTheme('system');
                }}
                className={`p-2 rounded-md bg-gray-100 border ${currentTheme === 'system' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'} hover:bg-gray-200`}
                aria-label={t('systemTheme', 'System theme')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${currentTheme === 'system' ? 'text-blue-600' : 'text-gray-700'}`} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  <line x1="8" y1="21" x2="16" y2="21"></line>
                  <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      )}
    </div>
  );
};

export default UserProfileMenu;
