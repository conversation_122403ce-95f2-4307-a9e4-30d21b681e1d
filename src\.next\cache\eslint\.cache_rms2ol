[{"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\convert\\page.tsx": "1", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx": "2", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\page.tsx": "3", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\pricing\\page.tsx": "4", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\register\\page.tsx": "5", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ConversionOptions.tsx": "6", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ConversionProgress.tsx": "7", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\FileListManager.tsx": "8", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\FileUploader.tsx": "9", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\MarkdownPreviewer.tsx": "10", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ResultDisplay.tsx": "11", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\i18n\\I18nProvider.tsx": "12", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\i18n\\LanguageSwitcher.tsx": "13", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\layout\\ClientLayout.tsx": "14", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\layout\\MainLayout.tsx": "15", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\lib\\supabaseClient.ts": "16", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\login\\page.tsx": "17", "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\auth\\UserProfileMenu.tsx": "18"}, {"size": 21174, "mtime": 1747903780208, "results": "19", "hashOfConfig": "20"}, {"size": 1494, "mtime": 1746592539788, "results": "21", "hashOfConfig": "20"}, {"size": 18586, "mtime": 1747376605205, "results": "22", "hashOfConfig": "20"}, {"size": 5590, "mtime": 1747818158750, "results": "23", "hashOfConfig": "20"}, {"size": 8002, "mtime": 1747818284614, "results": "24", "hashOfConfig": "20"}, {"size": 29741, "mtime": 1748063410163, "results": "25", "hashOfConfig": "20"}, {"size": 1946, "mtime": 1747223901146, "results": "26", "hashOfConfig": "20"}, {"size": 6376, "mtime": 1747204750175, "results": "27", "hashOfConfig": "20"}, {"size": 4725, "mtime": 1747204772139, "results": "28", "hashOfConfig": "20"}, {"size": 2971, "mtime": 1747204861426, "results": "29", "hashOfConfig": "20"}, {"size": 6060, "mtime": 1747204840926, "results": "30", "hashOfConfig": "20"}, {"size": 960, "mtime": 1746622342671, "results": "31", "hashOfConfig": "20"}, {"size": 3343, "mtime": 1747375823182, "results": "32", "hashOfConfig": "20"}, {"size": 5293, "mtime": 1747817854153, "results": "33", "hashOfConfig": "20"}, {"size": 1948, "mtime": 1746589933700, "results": "34", "hashOfConfig": "20"}, {"size": 2247, "mtime": 1747362677509, "results": "35", "hashOfConfig": "20"}, {"size": 9604, "mtime": 1747818228835, "results": "36", "hashOfConfig": "20"}, {"size": 20034, "mtime": 1747818001433, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "vstork", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\convert\\page.tsx", ["92"], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\page.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\pricing\\page.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\register\\page.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ConversionOptions.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ConversionProgress.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\FileListManager.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\FileUploader.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\MarkdownPreviewer.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\conversion\\ResultDisplay.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\i18n\\I18nProvider.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\i18n\\LanguageSwitcher.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\layout\\ClientLayout.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\layout\\MainLayout.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\lib\\supabaseClient.ts", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\login\\page.tsx", [], [], "D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\auth\\UserProfileMenu.tsx", [], [], {"ruleId": "93", "severity": 1, "message": "94", "line": 237, "column": 6, "nodeType": "95", "endLine": 237, "endColumn": 63, "suggestions": "96"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'conversionOptions' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["97"], {"desc": "98", "fix": "99"}, "Update the dependencies array to be: [files, isConverting, updateFileState, user, conversionOptions, pollTaskStatus, t]", {"range": "100", "text": "101"}, [11264, 11321], "[files, isConverting, updateFileState, user, conversionOptions, pollTaskStatus, t]"]