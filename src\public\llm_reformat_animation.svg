<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Document with Poor Formatting -->
  <g id="originalDoc" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Poor Formatting</text>

    <!-- Poorly formatted text lines -->
    <line x1="20" y1="35" x2="80" y2="35" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="40" x2="30" y2="40" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="50" x2="75" y2="50" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="55" x2="25" y2="55" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="65" x2="70" y2="65" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="70" x2="35" y2="70" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="80" x2="65" y2="80" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="85" x2="40" y2="85" stroke="#757575" stroke-width="1.5"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="1.5s" fill="freeze" />
  </g>

  <!-- Well-Formatted Document -->
  <g id="transformedDoc" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Well Formatted</text>

    <!-- Title -->
    <rect x="20" y="35" width="60" height="6" fill="#4CAF50"/>
    <text x="50" y="41" font-size="6" fill="#FFFFFF" text-anchor="middle" font-weight="bold"># Title</text>

    <!-- Paragraph -->
    <line x1="20" y1="50" x2="75" y2="50" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="55" x2="70" y2="55" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="60" x2="65" y2="60" stroke="#4CAF50" stroke-width="2"/>

    <!-- Bullet points -->
    <circle cx="25" cy="70" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="70" x2="70" y2="70" stroke="#4CAF50" stroke-width="2"/>

    <circle cx="25" cy="80" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="80" x2="65" y2="80" stroke="#4CAF50" stroke-width="2"/>

    <circle cx="25" cy="90" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="90" x2="75" y2="90" stroke="#4CAF50" stroke-width="2"/>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="1.5s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="1.5s" dur="1.5s" fill="freeze" />
  </g>
</svg>
