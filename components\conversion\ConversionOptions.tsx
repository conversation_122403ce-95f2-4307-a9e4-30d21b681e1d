import React, { useState } from 'react';
import { Disclosure, Popover } from '@headlessui/react';

export interface ConversionOptionsType {
  enable_summarize?: boolean;
  enable_grammar_correction?: boolean;
  enable_llm_reformat?: boolean;
  image_mode_preference?: 'embedded' | 'referenced';
  processing_mode?: string;
  enable_image_recognition?: boolean;
  enable_image_description?: boolean;
  image_description_style?: 'concise' | 'detailed';
}

interface ConversionOptionsProps {
  options: ConversionOptionsType;
  onChange: (options: ConversionOptionsType) => void;
}

// Helper component for displaying SVG animation in a Popover
const SVGAnimationIcon: React.FC<{ svgSrc: string; altText: string }> = ({ svgSrc, altText }) => {
  return (
    <Popover className="relative inline-block ml-2">
      {({ open }) => (
        <>
          <Popover.Button className="focus:outline-none">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
          </Popover.Button>
          <Popover.Panel className="absolute z-20 w-32 transform -translate-x-1/2 left-1/2 sm:left-auto sm:right-0 sm:-translate-x-0 mt-2">
            {/* Adjusted w-32 to roughly fit 100px + padding. SVG is 100x120 */}
            <div className="overflow-hidden rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
              <div className="bg-white p-2">
                <img src={svgSrc} alt={altText} width="100" height="120" />
              </div>
            </div>
          </Popover.Panel>
        </>
      )}
    </Popover>
  );
};


const ConversionOptions: React.FC<ConversionOptionsProps> = ({ options, onChange }) => {
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...options,
      [name]: checked,
    });
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...options,
      [name]: value,
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...options,
      [name]: value,
    });
  };

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium leading-6 text-gray-900">Conversion Options</h3>
        <div className="mt-4 space-y-6">
          {/* General Options */}
          <fieldset>
            <legend className="text-base font-medium text-gray-900">General</legend>
            <div className="mt-4 space-y-4">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable_summarize"
                    name="enable_summarize"
                    type="checkbox"
                    checked={options.enable_summarize || false}
                    onChange={handleCheckboxChange}
                    className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable_summarize" className="font-medium text-gray-700 align-middle">
                    Enable Summarize
                    <SVGAnimationIcon svgSrc="/summarize_animation.svg" altText="Summarize Animation" />
                  </label>
                  <p className="text-gray-500">Automatically generate a summary (user-tier based).</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable_grammar_correction"
                    name="enable_grammar_correction"
                    type="checkbox"
                    checked={options.enable_grammar_correction || false}
                    onChange={handleCheckboxChange}
                    className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable_grammar_correction" className="font-medium text-gray-700 align-middle">
                    Enable Grammar Correction
                    <SVGAnimationIcon svgSrc="/grammar_correction_animation.svg" altText="Grammar Correction Animation" />
                  </label>
                  <p className="text-gray-500">Correct grammar mistakes (user-tier based).</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable_llm_reformat"
                    name="enable_llm_reformat"
                    type="checkbox"
                    checked={options.enable_llm_reformat || false}
                    onChange={handleCheckboxChange}
                    className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable_llm_reformat" className="font-medium text-gray-700 align-middle">
                    Enable LLM Reformat
                    <SVGAnimationIcon svgSrc="/llm_reformat_animation.svg" altText="LLM Reformat Animation" />
                  </label>
                  <p className="text-gray-500">Reformat text using LLM.</p>
                </div>
              </div>
            </div>
          </fieldset>

          {/* Image Options */}
          <fieldset>
            <legend className="text-base font-medium text-gray-900">Images</legend>
            <div className="mt-4 space-y-4">
              <div>
                <label htmlFor="image_mode_preference" className="block text-sm font-medium text-gray-700">
                  Image Export Mode
                </label>
                <select
                  id="image_mode_preference"
                  name="image_mode_preference"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  value={options.image_mode_preference || 'embedded'}
                  onChange={handleSelectChange}
                >
                  <option value="embedded">Embedded (Base64)</option>
                  <option value="referenced">Referenced (Links)</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  Choose how images are exported in the Markdown.
                </p>
              </div>
            </div>
          </fieldset>

          {/* Processing Mode */}
          <fieldset>
            <legend className="text-base font-medium text-gray-900">Processing</legend>
            <div className="mt-4 space-y-4">
              <div>
                <label htmlFor="processing_mode" className="block text-sm font-medium text-gray-700">
                  Processing Mode
                </label>
                <input
                  type="text"
                  name="processing_mode"
                  id="processing_mode"
                  className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={options.processing_mode || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., zip_images_to_markdown"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Specify a special processing mode if needed.
                </p>
              </div>
            </div>
          </fieldset>
          
          {/* ZIP Bundle Image Enhancement Options - Basic Implementation */}
          <Disclosure as="div" className="mt-6">
            {({ open }) => (
              <>
                <Disclosure.Button className="flex justify-between w-full px-4 py-2 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75">
                  <span>ZIP Bundle Image Enhancements</span>
                  <svg
                    className={`${
                      open ? 'transform rotate-180' : ''
                    } w-5 h-5 text-gray-500`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Disclosure.Button>
                <Disclosure.Panel className="px-4 pt-4 pb-2 text-sm text-gray-500">
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="enable_image_recognition"
                          name="enable_image_recognition"
                          type="checkbox"
                          checked={options.enable_image_recognition || false}
                          onChange={handleCheckboxChange}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="enable_image_recognition" className="font-medium text-gray-700 align-middle">
                          Enable Image Recognition
                          <SVGAnimationIcon svgSrc="/image_recognition_animation.svg" altText="Image Recognition Animation" />
                        </label>
                        <p className="text-gray-500">Recognize content within images (for ZIP bundles).</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="enable_image_description"
                          name="enable_image_description"
                          type="checkbox"
                          checked={options.enable_image_description || false}
                          onChange={handleCheckboxChange}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="enable_image_description" className="font-medium text-gray-700 align-middle">
                          Enable Image Description
                          <SVGAnimationIcon svgSrc="/image_description_animation.svg" altText="Image Description Animation" />
                        </label>
                        <p className="text-gray-500">Generate textual descriptions for images (for ZIP bundles).</p>
                      </div>
                    </div>
                    
                    {options.enable_image_description && (
                       <div>
                         <label htmlFor="image_description_style" className="block text-sm font-medium text-gray-700">
                           Image Description Style
                         </label>
                         <select
                           id="image_description_style"
                           name="image_description_style"
                           className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                           value={options.image_description_style || 'concise'}
                           onChange={handleSelectChange}
                         >
                           <option value="concise">Concise</option>
                           <option value="detailed">Detailed</option>
                         </select>
                         <p className="mt-1 text-xs text-gray-500">
                           Style for generated image descriptions.
                         </p>
                       </div>
                    )}
                  </div>
                </Disclosure.Panel>
              </>
            )}
          </Disclosure>
        </div>
      </div>
    </div>
  );
};

export default ConversionOptions;
