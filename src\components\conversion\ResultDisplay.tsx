import React from 'react';
import { FileStatus } from './FileListManager';
import { useTranslation } from 'react-i18next';

interface ResultItem {
  id: string;
  fileName: string;
  status: FileStatus;
  resultUrl?: string;
  error?: string;
}

interface ResultDisplayProps {
  results: ResultItem[];
  onDownloadFile: (id: string) => void;
  onDownloadAll: () => void;
  onRetryFile?: (id: string) => void;
}

const ResultDisplay: React.FC<ResultDisplayProps> = ({
  results,
  onDownloadFile,
  onDownloadAll,
  onRetryFile,
}) => {
  const { t } = useTranslation(['common', 'convert']);
  const finalResults = results.filter(result => result.status === 'completed' || result.status === 'failed');
  const completedResultsCount = results.filter(result => result.status === 'completed').length;


  if (finalResults.length === 0) {
    return null;
  }

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium leading-6 text-gray-900">
            {t('convert:conversionResultsTitle', 'Conversion Results')}
          </h3>
          {completedResultsCount > 1 && (
            <button
              type="button"
              onClick={onDownloadAll}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('convert:downloadAll')}
            </button>
          )}
        </div>
        <div className="mt-4">
          {completedResultsCount > 0 && (
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-green-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    {completedResultsCount === 1
                      ? t('convert:successMessage')
                      : t('convert:successMessagePlural', { count: completedResultsCount })}
                  </p>
                  {/* <p className="text-xs text-green-600 mt-1">{t('convert:aiSuccessTip')}</p> */}
                </div>
              </div>
            </div>
          )}

          <ul className="divide-y divide-gray-200">
            {finalResults.map((result) => (
              <li key={result.id} className="py-4 flex justify-between items-center">
                <div className="flex items-center min-w-0">
                  {result.status === 'completed' ? (
                    <svg
                      className="h-6 w-6 text-green-500 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  ) : ( // Failed
                    <svg
                      className="h-6 w-6 text-red-500 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  )}
                  <div className="ml-2 min-w-0">
                    <p className="text-sm text-gray-900 truncate" title={result.fileName}>{result.fileName}</p>
                    {result.status === 'failed' && result.error && (
                        <p className="text-xs text-red-600 truncate" title={result.error}>{result.error}</p>
                    )}
                  </div>
                </div>
                <div className="ml-2 flex-shrink-0 flex space-x-2">
                  {result.status === 'completed' && (
                    <button
                      type="button"
                      onClick={() => onDownloadFile(result.id)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      {t('convert:download')}
                    </button>
                  )}
                  {result.status === 'failed' && onRetryFile && (
                     <button
                        type="button"
                        onClick={() => onRetryFile(result.id)}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                    >
                        {t('convert:retry', 'Retry')}
                    </button>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ResultDisplay;
