<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Image with Text -->
  <g id="originalImage" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Image with Text</text>

    <!-- Image content with text -->
    <rect x="20" y="35" width="60" height="8" fill="#BDBDBD" rx="1"/>
    <text x="50" y="41" font-size="6" fill="#FFFFFF" text-anchor="middle">Hello World</text>

    <rect x="20" y="50" width="50" height="8" fill="#BDBDBD" rx="1"/>
    <text x="45" y="56" font-size="6" fill="#FFFFFF" text-anchor="middle">Sample Text</text>

    <rect x="20" y="65" width="55" height="8" fill="#BDBDBD" rx="1"/>
    <text x="47.5" y="71" font-size="6" fill="#FFFFFF" text-anchor="middle">Document</text>

    <rect x="20" y="80" width="45" height="8" fill="#BDBDBD" rx="1"/>
    <text x="42.5" y="86" font-size="6" fill="#FFFFFF" text-anchor="middle">Content</text>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="2s" fill="freeze" />
  </g>

  <!-- Recognized Text -->
  <g id="recognizedText" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Extracted Text</text>

    <!-- Extracted text lines -->
    <line x1="20" y1="40" x2="70" y2="40" stroke="#4CAF50" stroke-width="2"/>
    <text x="20" y="37" font-size="5" fill="#4CAF50">Hello World</text>

    <line x1="20" y1="55" x2="65" y2="55" stroke="#4CAF50" stroke-width="2"/>
    <text x="20" y="52" font-size="5" fill="#4CAF50">Sample Text</text>

    <line x1="20" y1="70" x2="68" y2="70" stroke="#4CAF50" stroke-width="2"/>
    <text x="20" y="67" font-size="5" fill="#4CAF50">Document</text>

    <line x1="20" y1="85" x2="60" y2="85" stroke="#4CAF50" stroke-width="2"/>
    <text x="20" y="82" font-size="5" fill="#4CAF50">Content</text>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="0s" dur="2s" fill="freeze" />
  </g>
</svg>
