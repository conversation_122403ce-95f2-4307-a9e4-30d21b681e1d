(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/ws/browser.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = function() {
    throw new Error('ws does not work in the browser. Browser clients must use the native ' + 'WebSocket object');
};
}}),
}]);

//# sourceMappingURL=node_modules_ws_browser_bdb30e1f.js.map