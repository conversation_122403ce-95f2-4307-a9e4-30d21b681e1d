(()=>{var e={};e.id=907,e.ids=[907],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2646:(e,t,r)=>{Promise.resolve().then(r.bind(r,9500))},2984:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(687),i=r(3210),n=r.n(i);let a=i.forwardRef(function({title:e,titleId:t,...r},s){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z",clipRule:"evenodd"}))});var o=r(6457),l=r(8262);function c(...e){return e.filter(Boolean).join(" ")}function d(){let{t:e,i18n:t}=(0,o.Bd)(["pricing","common"]),r=(0,l.A)(t.language,["pricing","common"]),[i]=n().useState("monthly"),d=[{name:r.t("pricing:free"),id:"tier-free",href:"/convert",price:{monthly:"$0",annually:"$0"},description:"Perfect for occasional use and small documents.",features:["Convert up to 5 files per day","Max file size: 5MB","Basic Markdown conversion","Standard support"],mostPopular:!1},{name:r.t("pricing:pro"),id:"tier-pro",href:"#",price:{monthly:"$15",annually:"$144"},description:"Ideal for regular users and content creators.",features:["Unlimited conversions","Max file size: 20MB","Advanced Markdown options","Batch processing","Priority support","No ads"],mostPopular:!0},{name:r.t("pricing:enterprise"),id:"tier-enterprise",href:"#",price:{monthly:"$39",annually:"$384"},description:"For teams and professional content creators.",features:["Everything in Pro","Max file size: 50MB","API access","Custom conversion options","Team management","Dedicated support","Advanced analytics"],mostPopular:!1}];return(0,s.jsx)("div",{className:"bg-white py-24 sm:py-32",children:(0,s.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mx-auto max-w-4xl text-center",children:[(0,s.jsx)("h1",{className:"text-base font-semibold leading-7 text-blue-600",children:e("pricing:title")}),(0,s.jsx)("p",{className:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:e("pricing:subtitle")})]}),(0,s.jsx)("p",{className:"mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600",children:e("pricing:description")}),(0,s.jsx)("div",{className:"mt-16 flex justify-center",children:(0,s.jsx)("div",{className:"grid grid-cols-1 gap-y-8 sm:grid-cols-2 sm:gap-x-8 lg:grid-cols-3",children:d.map(t=>(0,s.jsxs)("div",{className:c(t.mostPopular?"ring-2 ring-blue-600":"ring-1 ring-gray-200","rounded-3xl p-8 xl:p-10"),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between gap-x-4",children:[(0,s.jsx)("h2",{id:t.id,className:"text-lg font-semibold leading-8 text-gray-900",children:t.name}),t.mostPopular?(0,s.jsx)("p",{className:"rounded-full bg-blue-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-blue-600",children:e("pricing:mostPopular")}):null]}),(0,s.jsx)("p",{className:"mt-4 text-sm leading-6 text-gray-600",children:t.description}),(0,s.jsxs)("p",{className:"mt-6 flex items-baseline gap-x-1",children:[(0,s.jsx)("span",{className:"text-4xl font-bold tracking-tight text-gray-900",children:"annually"===i?t.price.annually:t.price.monthly}),(0,s.jsx)("span",{className:"text-sm font-semibold leading-6 text-gray-600",children:"annually"===i?e("pricing:yearly"):e("pricing:monthly")})]}),(0,s.jsx)("a",{href:t.href,"aria-describedby":t.id,className:c(t.mostPopular?"bg-blue-600 text-white shadow-sm hover:bg-blue-500":"text-blue-600 ring-1 ring-inset ring-blue-200 hover:ring-blue-300","mt-6 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"),children:t.name===r.t("pricing:free")?e("pricing:getStarted"):e("pricing:subscribe")}),(0,s.jsx)("ul",{role:"list",className:"mt-8 space-y-3 text-sm leading-6 text-gray-600",children:t.features.map(e=>(0,s.jsxs)("li",{className:"flex gap-x-3",children:[(0,s.jsx)(a,{className:"h-6 w-5 flex-none text-blue-600","aria-hidden":"true"}),e]},e))})]},t.id))})})]})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3326:(e,t,r)=>{Promise.resolve().then(r.bind(r,2984))},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7679:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(5239),i=r(8088),n=r(8170),a=r.n(n),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9500)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\pricing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\pricing\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/pricing/page",pathname:"/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9500:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\github\\\\alltomarkdown_frontend\\\\src\\\\app\\\\pricing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\pricing\\page.tsx","default")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,373,658,522],()=>r(7679));module.exports=s})();