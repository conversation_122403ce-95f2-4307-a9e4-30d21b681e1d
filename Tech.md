# **技术设计文档：在线文档转 Markdown 网站 (前端)**

**版本：** 1.0
**日期：** 2025年5月7日
**基于 PRD 版本：** 1.0 (2025年5月6日)

## **1\. 引言 (Introduction)**

### **1.1. 文档目的 (Purpose of this Document)**

本文档旨在为“在线文档转 Markdown 网站”的前端开发提供详细的技术指导和规范。它基于产品需求文档 (PRD) V1.0，并对其进行技术层面的细化和补充，确保开发团队能够高效、高质量地完成前端应用的架构设计、模块开发、技术选型验证和最终实现。本文档将作为前端开发、测试以及后续维护工作的重要参考依据。

### **1.2. 项目概述 (Project Overview)**

本项目旨在构建一个现代、简洁、支持多语言的在线服务网站，专注于将各种常见文档格式（如 HTML、Word 文档等）高效、准确地转换为 Markdown 格式。该网站主要面向个人开发者、内容创作者、技术写手等需要频繁进行文档格式转换的个体用户。前端将采用 React 和 Next.js 构建，利用 Headless UI 和 Tailwind CSS 实现用户界面。

### **1.3. 技术栈概览 (Technology Stack Overview)**

*   **核心框架：** [`React`](https://react.dev/) with [`Next.js`](https://nextjs.org/)
*   **UI 组件库：** [`Headless UI`](https://headlessui.com/)
*   **CSS 框架：** [`Tailwind CSS`](https://tailwindcss.com/)
*   **状态管理：** [`React Context API`](https://react.dev/learn/passing-data-deeply-with-context) (优先), [`Zustand`](https://zustand-demo.pmnd.rs/) (备选，用于复杂状态)
*   **国际化 (i18n)：** [`react-i18next`](https://react.i18next.com/) / [`next-i18next`](https://github.com/i18next/next-i18next)
*   **API 通信：** [`Axios`](https://axios-http.com/)
*   **HTML 清理：** [`DOMPurify`](https://github.com/cure53/DOMPurify)
*   **构建工具：** Next.js 内置 (Webpack/SWC)
*   **代码规范：** ESLint, Prettier
*   **版本控制：** Git

## **2\. 系统架构 (System Architecture)**

### **2.1. 前端架构概述 (Frontend Architecture Overview)**

前端应用将基于 Next.js 框架构建，充分利用其提供的各项特性以实现高性能、可维护和可扩展的单页应用 (SPA) 或服务端渲染 (SSR) / 静态站点生成 (SSG) 的混合模式。

*   **页面路由 (Routing)：** 利用 Next.js 基于文件系统的路由机制 (`pages` 目录)。动态路由将用于如用户配置、特定转换任务等场景。
*   **组件化 (Componentization)：** 遵循 React 的组件化思想，将 UI 拆分为可复用、高内聚、低耦合的组件。核心业务逻辑和 UI 展示将分离。
*   **服务端渲染/静态生成 (SSR/SSG)：**
    *   **SSG：** 适用于内容不经常变动的页面，如首页、关于我们、定价页（如果内容固定）。
    *   **SSR：** 适用于需要根据请求动态生成内容的页面，或需要 SEO 优化的用户特定内容页面。
    *   **CSR (Client-Side Rendering)：** 主要的转换工具页面、用户仪表盘等交互密集的页面将主要依赖客户端渲染，以保证交互的流畅性。Next.js 依然可以作为这些页面的入口和壳。
*   **API 路由 (API Routes)：** Next.js 的 API 路由 (`pages/api`) 可用于处理一些轻量级的后端逻辑，例如代理第三方 API 请求、处理表单提交（在没有专属后端服务初期），或与 Firebase/Auth0 等 BaaS 服务交互。但核心的文档转换逻辑预期由独立的后端服务处理。
*   **图片优化 (Image Optimization)：** 使用 Next.js 内置的 `<Image>` 组件进行图片自动优化。
*   **代码分割 (Code Splitting)：** Next.js 自动进行代码分割，确保每个页面只加载其必需的 JavaScript。

### **2.2. 核心模块划分 (Core Module Breakdown)**

根据 PRD 中的功能需求，前端可以划分为以下核心模块：

1.  **文档转换模块 (Document Conversion Module)：**
    *   文件上传组件 (FileUploader)
    *   文件列表与管理组件 (FileListManager)
    *   转换选项配置组件 (ConversionOptions)
    *   转换进度与状态显示组件 (ConversionProgress)
    *   结果展示与下载组件 (ResultDisplay)
    *   Markdown 预览组件 (MarkdownPreviewer)
2.  **用户认证模块 (User Authentication Module)：**
    *   注册表单组件 (RegisterForm)
    *   登录表单组件 (LoginForm)
    *   密码重置组件 (PasswordReset)
    *   社交登录按钮 (SocialLoginButtons)
    *   用户状态管理 (AuthContext/Zustand Store)
3.  **国际化模块 (Internationalization Module)：**
    *   语言切换组件 (LanguageSwitcher)
    *   i18n 配置与实例 (i18nSetup)
4.  **支付模块 (Payment Module) (可选)：**
    *   定价方案展示组件 (PricingPlans)
    *   Stripe 支付表单集成组件 (StripeForm)
5.  **广告模块 (Advertising Module) (可选)：**
    *   广告单元组件 (AdUnit)
6.  **通用 UI 组件模块 (Common UI Components Module)：**
    *   按钮 (Button)
    *   模态框 (Modal)
    *   下拉菜单 (Dropdown)
    *   输入框 (Input)
    *   通知/提示 (Notification/Toast)
    *   布局组件 (Layouts: MainLayout, AuthLayout)
7.  **核心服务模块 (Core Services Module)：**
    *   API 服务 (ApiService - Axios 封装)
    *   本地存储服务 (LocalStorageService)
    *   错误处理服务 (ErrorHandlingService)

### **2.3. 主要技术选型与理由 (Key Technology Choices & Rationale)**

*   **React with Next.js:**
    *   **理由：** React 提供了强大的组件化能力和庞大的生态。Next.js 在此基础上提供了优秀的开发体验、性能优化机制 (SSR, SSG, Image Optimization, Code Splitting)、内置路由、API 路由等，非常适合构建现代 Web 应用。其对 SEO 友好，也易于部署。
*   **Headless UI + Tailwind CSS:**
    *   **理由：** Headless UI 提供了无样式、功能完整且注重可访问性的组件逻辑，开发者可以完全掌控视觉表现。Tailwind CSS 是一个 utility-first 的 CSS 框架，能够快速构建高度自定义的界面，与 Headless UI 完美结合，避免了传统 UI 库样式覆盖的麻烦，同时保持了代码的整洁和可维护性（通过 `@apply` 或组件封装管理复杂样式）。
*   **React Context API / Zustand (State Management):**
    *   **理由：** 对于全局状态如用户认证信息、主题偏好、语言偏好等，React Context API 是轻量级且内置的选择。当应用状态变得更复杂，或需要更细致的性能优化时，Zustand 提供了简洁的 API、良好的性能和易于集成的特点，相比 Redux 更轻量。
*   **react-i18next / next-i18next (i18n):**
    *   **理由：** `i18next` 是一个功能强大且成熟的国际化框架。`react-i18next` 提供了 React 的绑定，而 `next-i18next` 则简化了其与 Next.js (特别是 SSR/SSG 场景下) 的集成，支持命名空间、复数、插值等高级功能。
*   **Axios (API Communication):**
    *   **理由：** Axios 是一个流行的基于 Promise 的 HTTP客户端，支持请求/响应拦截器、取消请求、超时设置、自动转换 JSON 数据等，使得 API 调用和管理更为便捷和强大。
*   **DOMPurify (HTML Sanitization):**
    *   **理由：** 在前端预览 Markdown 转换后的 HTML 时，防止 XSS 攻击至关重要。DOMPurify 是一个经过充分测试、高效且配置灵活的 HTML 清理库。

## **3\. 核心功能实现详述 (Core Feature Implementation Details)**

(对应 PRD 第 3.1 节)

### **3.1. 文档到 Markdown 转换流程 (Document to Markdown Conversion Flow)**

**前端主要职责：** 文件选择、上传管理、选项配置、调用后端转换 API、展示进度、处理结果。

#### **3.1.1. 文件上传模块 (File Upload Module)**

*   **组件设计 (`FileUploader.tsx`, `FileListManager.tsx`):**
    *   `FileUploader`: 包含点击上传按钮和拖放区域。
        *   使用 `<input type="file" multiple>` 隐藏元素，通过按钮点击触发。
        *   拖放区域监听 `dragenter`, `dragover`, `dragleave`, `drop` 事件。
        *   视觉反馈：拖放时高亮区域。
    *   `FileListManager`: 展示待上传/转换的文件列表。
        *   显示文件名、大小、上传进度（百分比或进度条）、状态（等待、上传中、转换中、成功、失败）。
        *   提供移除单个文件的按钮。
        *   提供“全部清除”按钮。
*   **状态管理 (React Context 或 Zustand):**
    *   `filesToUpload`: `File[]` - 用户选择的文件对象数组。
    *   `fileProgress`: `Map<string, number>` - 文件名到上传进度的映射。
    *   `fileStatus`: `Map<string, 'pending' | 'uploading' | 'processing' | 'completed' | 'failed'>` - 文件名到状态的映射。
*   **文件校验 (FR-006):**
    *   在文件选择后立即进行客户端校验（文件类型、大小）。
    *   支持的文件类型 (例如: `['.html', '.htm', '.doc', '.docx', '.txt']`) 和最大文件大小 (例如: `10 * 1024 * 1024` for 10MB) 应可配置。
    *   不符合要求的文件给出明确提示，并阻止其进入上传列表。
*   **上传逻辑 (FR-004, FR-005):**
    *   使用 `Axios` 发送 `multipart/form-data` 请求到后端转换 API。
    *   为每个文件单独发起上传请求，或根据后端 API 设计进行批量上传（若支持）。
    *   利用 `Axios` 的 `onUploadProgress` 事件更新 `fileProgress` 状态，驱动 UI 进度条。
    *   并发上传数量可以进行限制（例如，一次最多上传 3-5 个文件），以避免浏览器或网络瓶颈。

#### **3.1.2. 格式选择与转换选项 (Format Selection & Conversion Options)**

*   **组件设计 (`ConversionOptions.tsx`):**
    *   **输入格式 (FR-007):** 通常由后端自动检测。如果前端需要提供选项（例如，当一个文件扩展名可能对应多种内部格式时），可以使用下拉菜单 (Headless UI `Listbox`)。
    *   **输出格式 (FR-008):** 固定为 Markdown，界面明确标示。
    *   **(可选) Markdown 风格 (FR-009):** 使用下拉菜单选择 GFM, CommonMark 等。默认选中 GFM。
    *   **(可选) 其他转换设置 (FR-010):**
        *   图片处理方式 (例如：上传到图床并替换链接 / Base64 嵌入 / 保留原始相对路径 - 取决于后端能力)。
        *   表格转换方式。
        *   这些高级选项默认折叠 (Headless UI `Disclosure`) 或在独立设置区域展示。
*   **状态管理:**
    *   `selectedMarkdownFlavor`: `string`
    *   `advancedOptions`: `object`
    *   这些状态将作为参数传递给后端转换 API。

#### **3.1.3. 转换过程与进度反馈 (Conversion Process & Progress Feedback)**

*   **“开始转换”按钮 (FR-011):**
    *   位于突出位置，视觉上吸引用户点击。
    *   点击后，遍历 `filesToUpload` 列表，对每个文件调用后端转换 API（如果之前未在选择后立即上传并转换）。
    *   按钮在转换过程中应变为禁用状态，并显示加载指示。
*   **后端转换进度 (FR-012):**
    *   **推荐方案：Server-Sent Events (SSE)。**
        *   前端在发起转换请求后，与后端建立一个 SSE 连接，监听特定事件以接收进度更新。
        *   每个文件转换可能对应一个独立的 SSE 流或在同一流中通过 ID 区分。
        *   更新 `fileStatus` 和 `fileProgress` (如果后端能提供更细致的转换内部进度)。
    *   **备选方案：轮询 (Polling)。**
        *   如果 SSE 实现困难，可以定期轮询后端 API 获取转换状态。此方案实时性较差，且增加服务器负载。
*   **状态显示 (FR-013):**
    *   `FileListManager` 组件根据 `fileStatus` 清晰显示每个文件的当前状态。

#### **3.1.4. 结果展示与下载 (Result Display & Download)**

*   **成功提示 (FR-014):**
    *   文件转换成功后，在文件列表中更新状态，并可显示全局成功通知 (Toast)。
*   **下载按钮 (FR-015):**
    *   每个成功转换的文件旁显示“下载 .md”按钮。
    *   点击按钮后，前端可以：
        *   如果 API 直接返回文件内容：创建一个 `Blob` 对象，然后使用 `URL.createObjectURL` 和一个 `<a>` 标签的 `download` 属性来触发下载。文件名应为 `original_filename.md`。
        *   如果 API 返回一个下载链接：直接 `window.open(downloadLink)` 或修改 `<a>` 标签的 `href`。
*   **全部下载 (FR-016):**
    *   如果转换了多个文件，提供“全部下载 (.zip)”按钮。
    *   此功能通常需要后端支持：前端请求一个打包接口，后端将所有已转换的 Markdown 文件打包成 ZIP 并返回。
*   **失败处理 (FR-017):**
    *   在文件列表中更新状态为“失败”。
    *   显示后端返回的错误信息（如果可用且用户友好）。
    *   提供“重试”按钮，允许用户重新发起该文件的转换请求。
*   **(可选) 临时存储与查看 (FR-018):**
    *   登录用户转换的文件信息（文件名、转换时间、下载链接 - 若后端提供）可以存储在用户特定的数据库记录中。
    *   前端可以请求一个“转换历史”API 来获取这些信息并在专门页面展示。

#### **3.1.5. Markdown 内容预览 (Markdown Content Preview) (Optional, FR-019, FR-020)**

*   **组件设计 (`MarkdownPreviewer.tsx`):**
    *   一个区域用于展示渲染后的 HTML。
    *   需要一个 Markdown 到 HTML 的转换库在前端执行（如 `marked.js` 或 `markdown-it`），或者如果后端 API 可以直接返回预览用的 HTML。
*   **XSS 清理 (FR-020):**
    *   **必须使用 `DOMPurify.sanitize(htmlString)` 清理 Markdown 库生成的 HTML 字符串，然后再通过 `dangerouslySetInnerHTML` (React) 或类似方式渲染。**
    *   `DOMPurify` 配置应严格，仅允许 Markdown 常用且安全的标签和属性。

## **4\. 用户系统实现 (User System Implementation)**

(对应 PRD 第 3.2 节)
推荐使用第三方认证服务如 **Firebase Authentication** 或 **Auth0** 来简化实现和提高安全性。以下以 Firebase 为例。

### **4.1. 认证流程 (Authentication Flow)**

1.  **初始化 Firebase:** 在 Next.js 应用的入口 (`_app.tsx`) 初始化 Firebase SDK。
2.  **状态监听:** 使用 `onAuthStateChanged` 监听用户登录状态变化，并将用户信息存储在 React Context 或 Zustand store 中。
3.  **登录/注册:** 用户通过表单或第三方提供商（Google, GitHub）进行操作。
4.  **令牌管理:** Firebase SDK 会自动处理 ID Token 的获取和刷新。前端主要使用此 ID Token 与后端 API（如果后端需要验证用户身份）进行通信。

### **4.2. 用户注册与登录模块 (User Registration & Login Module)**

*   **组件设计 (`RegisterForm.tsx`, `LoginForm.tsx`):**
    *   使用 Headless UI 和 Tailwind CSS 构建表单。
    *   包含邮箱、密码输入框。注册表单可能包含确认密码。
    *   使用 HTML5 表单校验 + JavaScript 增强校验（如密码强度）。
*   **Firebase API 调用:**
    *   注册: `createUserWithEmailAndPassword(auth, email, password)`
    *   登录: `signInWithEmailAndPassword(auth, email, password)`
    *   Google 登录: `signInWithPopup(auth, new GoogleAuthProvider())`
    *   GitHub 登录: `signInWithPopup(auth, new GithubAuthProvider())`
*   **错误处理:** 捕获 Firebase 返回的错误，并向用户显示友好的提示信息。
*   **密码重置 (FR-025):** `sendPasswordResetEmail(auth, email)`

### **4.3. 会话管理 (User Session Management)**

*   **JWT (ID Token) (FR-027, FR-028):**
    *   Firebase SDK 会在用户登录后自动获取 ID Token，并存储在浏览器的 IndexedDB 中进行持久化。SDK 也会自动处理 Token 的刷新。
    *   前端在向需要认证的后端 API 发送请求时，通过 `auth.currentUser.getIdToken()` 获取当前的 ID Token，并将其放在 `Authorization` HTTP Header 中 (e.g., `Bearer <ID_TOKEN>`)。
*   **用户登出 (FR-029):**
    *   调用 `signOut(auth)`。
    *   清除 React Context/Zustand 中的用户状态。
    *   重定向到首页或登录页。
*   **保持登录状态 (FR-030):** Firebase 默认通过持久化会话来实现此功能。

### **4.4. 路由保护 (Route Protection)**

*   **实现方式 (FR-031, FR-032, FR-033):**
    1.  **React Context / Zustand:** 维护全局用户认证状态 (`user`, `isLoading`)。
    2.  **高阶组件 (HOC) `withAuth`:**
        ```typescript
        // hoc/withAuth.tsx
        import { useRouter } from 'next/router';
        import { useAuth } // 从 Context 或 Zustand 获取认证状态
        
        const withAuth = (WrappedComponent) => {
          return (props) => {
            const { user, isLoading } = useAuth();
            const router = useRouter();
        
            if (isLoading) {
              return <p>Loading...</p>; // 或者一个骨架屏
            }
        
            if (!user && typeof window !== 'undefined') {
              router.replace(`/login?redirectedFrom=${router.pathname}`);
              return null;
            }
        
            return <WrappedComponent {...props} />;
          };
        };
        export default withAuth;
        ```
    3.  **在 `_app.tsx` 中或页面级应用:**
        ```typescript
        // pages/dashboard.tsx
        import withAuth from '../hoc/withAuth';
        
        function DashboardPage() {
          // ...
        }
        export default withAuth(DashboardPage);
        ```
    4.  **登录后重定向:** 在登录页面，成功登录后检查 `router.query.redirectedFrom`，如果存在则重定向到该路径，否则到默认页面（如用户仪表盘）。
    *   **Next.js 中间件 (Middleware):** 另一种更现代的方式是使用 Next.js Middleware (`middleware.ts`) 来处理路由保护。中间件可以在请求到达页面之前检查认证状态，并执行重定向。这种方式对于 SSR/SSG 页面也更友好。

## **5\. 多语言支持实现 (Multilingual Support Implementation)**

(对应 PRD 第 3.3 节)

### **5.1. `react-i18next` / `next-i18next` 集成与配置**

*   **安装:** `npm install react-i18next i18next next-i18next`
*   **配置文件 (`next-i18next.config.js`):**
    ```javascript
    // next-i18next.config.js
    module.exports = {
      i18n: {
        defaultLocale: 'en',
        locales: ['en', 'zh'], // 支持的语言列表
      },
      localePath: typeof window === 'undefined' ? require('path').resolve('./public/locales') : '/locales',
      reloadOnPrerender: process.env.NODE_ENV === 'development',
    };
    ```
*   **在 `_app.tsx` 中集成:**
    ```typescript
    // pages/_app.tsx
    import { appWithTranslation } from 'next-i18next';
    // ...
    export default appWithTranslation(MyApp);
    ```
*   **在页面组件中使用 `serverSideTranslations` (用于 SSG/SSR):**
    ```typescript
    // pages/index.tsx
    import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
    import { useTranslation } from 'next-i18next';
    
    export async function getStaticProps({ locale }) {
      return {
        props: {
          ...(await serverSideTranslations(locale, ['common', 'home'])), // 'common', 'home' 是命名空间
        },
      };
    }
    
    export default function HomePage() {
      const { t } = useTranslation('home'); // 使用 'home' 命名空间
      return <h1>{t('welcomeMessage')}</h1>;
    }
    ```

### **5.2. 语言文件结构与管理 (FR-038)**

*   **路径:** `public/locales/{lng}/{namespace}.json`
    *   `public/locales/en/common.json`
    *   `public/locales/en/home.json`
    *   `public/locales/zh/common.json`
    *   `public/locales/zh/home.json`
*   **内容示例 (`public/locales/en/common.json`):**
    ```json
    {
      "appName": "Doc to Markdown",
      "upload": "Upload",
      "convert": "Convert"
    }
    ```
*   **命名空间 (Namespaces):** 按模块或页面组织翻译，避免单个文件过大，也方便按需加载。

### **5.3. 语言切换组件实现 (`LanguageSwitcher.tsx`) (FR-034)**

*   **组件设计:** 使用 Headless UI `Listbox` 或简单按钮组。
*   **功能:**
    *   获取当前语言: `router.locale`
    *   获取所有支持的语言: `router.locales`
    *   切换语言: `router.push(router.pathname, router.asPath, { locale: newLocale })`
*   **持久化 (FR-035):** `next-i18next` 默认会通过 cookie (`next-i18next`) 或 localStorage (可配置) 持久化用户选择的语言。
*   **自动检测 (FR-036):** `next-i18next` 支持基于浏览器 `navigator.language` 的语言检测，可以在 `next-i18next.config.js` 中配置。

### **5.4. 动态内容与复数处理 (FR-039, FR-040)**

*   **插值 (Interpolation):**
    *   JSON: `"welcome": "Welcome, {{name}}!"`
    *   Usage: `t('welcome', { name: userName })`
*   **复数 (Pluralization):** `i18next` 支持强大的复数规则。
    *   JSON:
        ```json
        {
          "item_one": "{{count}} item",
          "item_other": "{{count}} items"
        }
        ```
    *   Usage: `t('item', { count: numItems })`
*   **回退语言 (FR-041):** `i18next` 默认配置了回退机制，当某个键在当前语言中缺失时，会尝试从默认语言 (如 `en`) 中查找。

## **6\. 支付系统集成 (Payment System Integration)**

(对应 PRD 第 3.4 节 - 若实现)
推荐使用 **Stripe**。

### **6.1. Stripe 集成方案 (Stripe Elements)**

*   **前端库:** `@stripe/react-stripe-js`, `@stripe/stripe-js`
*   **流程:**
    1.  **加载 Stripe.js:** 在 `_app.tsx` 或特定支付页面使用 `loadStripe`。
    2.  **Elements Provider:** 用 `<Elements>` 组件包裹支付表单。
    3.  **支付表单组件 (`StripePaymentForm.tsx`):**
        *   使用 Stripe Elements (e.g., `CardElement`, `PaymentElement`) 构建安全的输入字段。支付信息不会直接触达项目服务器 (FR-045)。
        *   收集用户账单信息。
    4.  **创建 PaymentIntent (后端):** 用户点击支付前，前端请求后端 API 创建一个 `PaymentIntent`。后端返回 `client_secret`。
    5.  **确认支付 (前端):** 使用 `stripe.confirmCardPayment(clientSecret, { payment_method: { card: elements.getElement(CardElement) } })` 或 `stripe.confirmPayment()` (for PaymentElement)。
    6.  **处理结果:** 根据 Stripe 返回的结果（成功、失败、需额外操作）更新 UI，并通知后端更新用户订阅状态。

### **6.2. 付费方案展示页面组件 (`PricingPage.tsx`) (FR-042, FR-043)**

*   清晰展示不同方案、特性、价格、计费周期。
*   CTA 按钮引导用户选择方案并进入支付流程。

## **7\. 广告系统集成 (Advertising System Integration)**

(对应 PRD 第 3.5 节 - 若实现)
推荐使用 **Google AdSense**。

### **7.1. 广告 SDK 集成**

*   **AdSense Auto Ads:** 在 `<head>` 中添加 AdSense 提供的脚本。
*   **手动放置广告单元:**
    *   创建广告单元组件 (`AdUnit.tsx`)。
    *   该组件负责加载 AdSense 广告脚本 (如果尚未加载) 并根据广告单元 ID 展示广告。
    *   **SPA 刷新 (FR-050):** 对于 Next.js (SPA 导航)，确保在路由切换后广告能够正确加载和刷新。可能需要在 `useEffect` 中处理广告库的 API 调用，或者使用专门为 React/Next.js 设计的 AdSense 组件库。
        ```javascript
        // AdUnit.tsx
        useEffect(() => {
          try {
            (window.adsbygoogle = window.adsbygoogle || []).push({});
          } catch (e) {
            console.error("AdSense error:", e);
          }
        }, [router.asPath]); // 依赖路由路径变化来尝试刷新
        
        return (
          <ins className="adsbygoogle"
               style={{ display: 'block' }}
               data-ad-client="ca-pub-YOUR_CLIENT_ID"
               data-ad-slot="YOUR_SLOT_ID"
               data-ad-format="auto"
               data-full-width-responsive="true"></ins>
        );
        ```

### **7.2. `ads.txt` 配置 (FR-051)**

*   在 `public` 目录下放置 `ads.txt` 文件，并确保 Next.js 构建后能从网站根目录访问到。

### **7.3. 广告拦截器检测 (FR-052) (可选)**

*   可以使用第三方库或自定义脚本检测广告是否被成功加载，若未加载则判断为可能被拦截。
*   友好提示用户。

## **8\. 非功能性需求实现策略 (Non-Functional Requirements Implementation)**

(对应 PRD 第 4 节)

### **8.1. 性能 (Performance)**

*   **页面加载速度 (NFR-001):**
    *   **Next.js 优化:** 利用 SSG/SSR 减少 FCP/LCP。
    *   **代码分割:** Next.js 自动处理。
    *   **图片优化:** 使用 `next/image`。
    *   **懒加载:** 对非首屏组件、图片使用懒加载 (`next/dynamic` for components)。
*   **交互响应 (NFR-002):**
    *   优化 React 组件渲染性能 (`React.memo`, `useCallback`, `useMemo`)。
    *   避免不必要的重渲染。
    *   使用 Web Workers 处理 CPU 密集型任务（如果前端有此类任务，如大型文件解析前的预处理）。
*   **资源优化 (NFR-003):**
    *   Next.js 内置压缩 (Gzip/Brotli)。
    *   Tailwind CSS JIT 模式生成最小化的 CSS。
    *   定期分析打包结果 (`@next/bundle-analyzer`)。

### **8.2. 用户体验 (Usability/User Experience)**

*   **易学性 (NFR-004) & 高效性 (NFR-005):**
    *   遵循 PRD 第 6 节 UI/UX 设计指南。
    *   核心流程（文件上传到转换）保持简洁直观。
*   **容错性 (NFR-006):**
    *   清晰的错误提示（见 11. 错误处理机制）。
    *   表单校验友好提示。
*   **UI/UX 实现要点 (PRD 第 6 节):**
    *   **Tailwind CSS:** 严格遵循 utility-first，对于可复用样式组合，使用 `@apply` 在全局 CSS 文件中定义组件类，或直接在 React 组件中封装样式逻辑。
    *   **Headless UI:** 充分利用其无样式特性，结合 Tailwind CSS 实现完全自定义的、符合设计稿的组件。
    *   **响应式设计 (NFR-009):** 使用 Tailwind CSS 的响应式断点 (`sm:`, `md:`, `lg:`)。
    *   **微交互:** 使用 Tailwind CSS 的过渡 (`transition`, `duration`) 和动画工具类。

### **8.3. 兼容性 (Compatibility)**

*   **浏览器兼容性 (NFR-008):**
    *   Next.js 默认支持现代浏览器。
    *   针对特定 CSS/JS特性，检查 `caniuse.com`。
    *   使用 PostCSS (Next.js 内置) 自动添加浏览器前缀。
    *   测试：在主流浏览器 (Chrome, Firefox, Safari, Edge) 的最新两个版本上进行测试。
*   **设备兼容性 (NFR-009):** 通过响应式设计确保。

### **8.4. 安全性 (Security)**

*   **XSS 防护 (NFR-010):**
    *   **DOMPurify:** 任何用户输入或第三方来源的 HTML 在渲染前必须使用 `DOMPurify.sanitize()`。
    *   **React:** 默认会对 JSX 中的字符串进行转义，避免直接使用 `dangerouslySetInnerHTML`，除非配合 DOMPurify。
*   **JWT 安全 (NFR-011):**
    *   Firebase SDK 负责安全存储和刷新 ID Token。
    *   所有 API 通信使用 HTTPS (NFR-012)。
*   **API 请求安全 (NFR-012):** 确保所有对后端 API 的请求都通过 HTTPS。
*   **CSRF 防护 (NFR-013):**
    *   如果使用 Next.js API Routes 作为后端且涉及 Cookie 认证（非 Firebase ID Token 模式），则需要实现 CSRF 保护机制（如双重提交 Cookie，或使用 `csurf` 等库）。对于 Firebase ID Token 认证，CSRF 风险较低，因为 Token 通常通过 Header 传递。

### **8.5. 可维护性 (Maintainability)**

*   **代码规范 (NFR-014):**
    *   ESLint + Prettier 强制执行统一的代码风格和质量检查。
    *   TypeScript (推荐) 提供静态类型检查，提高代码健壮性和可维护性。
*   **组件化 (NFR-015):** 遵循 SOLID 原则设计 React 组件。
*   **文档化 (NFR-016):**
    *   复杂组件和逻辑添加 JSDoc/TSDoc 注释。
    *   维护 Storybook (可选) 展示和测试 UI 组件。
*   **Tailwind CSS 维护 (NFR-017):**
    *   对于常用的样式组合，封装为 React 组件的 props 或使用 `@apply` 创建语义化的 CSS 类，避免在 JSX 中出现过长的类名列表。
    *   配置文件 (`tailwind.config.js`) 清晰定义主题、颜色、字体等。

### **8.6. 可访问性 (Accessibility) (WCAG 2.1 AA)**

*   **语义化 HTML (NFR-020):** 使用正确的 HTML5 标签。
*   **Headless UI (NFR-021):** 其组件设计本身注重可访问性 (ARIA 属性, 键盘导航)。
*   **键盘导航 (NFR-022):** 确保所有交互元素都可通过键盘访问和操作。焦点状态清晰可见。
*   **ARIA 属性:** 根据需要为动态内容和自定义组件添加 ARIA 属性。
*   **图片替代文本 (alt text):** 所有图片提供有意义的 `alt` 文本。
*   **色彩对比度:** 确保文本和背景有足够的对比度。
*   **测试工具:** 使用 aXe, Lighthouse 等工具进行可访问性审计。

## **9\. 状态管理策略 (State Management Strategy)**

(详细阐述 PRD 第 5.3 节)

### **9.1. 全局状态与局部状态划分**

*   **全局状态:**
    *   用户认证信息 (user object, loading state, error state)
    *   应用主题 (light/dark mode)
    *   用户语言偏好
    *   全局通知/Toast 状态
    *   (可选) 购物车/订阅方案选择状态 (如果支付流程复杂)
*   **局部状态 (组件级状态):**
    *   表单输入值、校验状态
    *   UI 元素显隐、激活状态 (e.g., Modal, Dropdown)
    *   单个文件上传进度、状态
    *   API 请求的加载/错误状态 (通常由数据获取库如 SWR/React Query 处理，或在组件内手动管理)

### **9.2. React Context API 使用场景**

*   **首选方案** 用于管理上述大部分全局状态，特别是当状态更新不频繁或跨组件层级不深时。
*   **实现:**
    *   创建 Context (`AuthContext`, `ThemeContext`, `LocaleContext`)。
    *   创建 Provider 组件，在其内部使用 `useState` 或 `useReducer` 管理状态，并将状态和更新函数通过 Context value 传递下去。
    *   在 `_app.tsx` 中用 Provider 包裹整个应用。
    *   在需要访问状态的组件中使用 `useContext` Hook。

### **9.3. Zustand/Jotai (或选定的库) 使用场景与集成**

*   **备选方案** 当全局状态逻辑变得复杂，或者 Context 引起的性能问题（不必要的重渲染）难以优化时，考虑引入 Zustand。
*   **Zustand 特点:**
    *   API 简洁，基于 Hooks。
    *   无需 Provider 包裹。
    *   选择性订阅状态，减少重渲染。
*   **集成示例 (Zustand for Auth):**
    ```typescript
    // store/authStore.ts
    import create from 'zustand';
    
    interface AuthState {
      user: User | null;
      isLoading: boolean;
      error: Error | null;
      setUser: (user: User | null) => void;
      setLoading: (loading: boolean) => void;
      setError: (error: Error | null) => void;
    }
    
    export const useAuthStore = create<AuthState>((set) => ({
      user: null,
      isLoading: true,
      error: null,
      setUser: (user) => set({ user }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
    }));
    ```
    在组件中使用: `const { user, setUser } = useAuthStore();`

## **10\. API 通信 (API Communication)**

(详细阐述 PRD 第 5.6 节)

### **10.1. Axios 实例配置 (`services/api.ts`)**

```typescript
// services/api.ts
import axios from 'axios';
import { auth } from './firebase'; // 假设 Firebase 初始化在此

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL, // 从环境变量读取 API 基础路径
  headers: {
    'Content-Type': 'application/json',
  },
});
```

### **10.2. 请求与响应拦截器实现 (Error Handling, Token Attachment)**

```typescript
// services/api.ts (续)

// 请求拦截器: 附加 ID Token
apiClient.interceptors.request.use(
  async (config) => {
    if (auth.currentUser) {
      try {
        const token = await auth.currentUser.getIdToken(true); // true 强制刷新（如果需要）
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Error getting ID token:', error);
        // 可以选择在此处处理 token 获取失败的情况，例如重定向到登录页
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器: 全局错误处理 (FR-305)
apiClient.interceptors.response.use(
  (response) => {
    return response; // 直接返回成功的响应
  },
  (error) => {
    // PRD-FR-305: 使用 Axios 的响应拦截器全局处理常见的 API 错误
    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 401: // 未授权
          // TODO: 处理未授权逻辑，例如重定向到登录页，清除用户状态
          console.error('Unauthorized access - 401', data);
          // window.location.href = '/login'; // 简单粗暴的方式
          break;
        case 403: // 禁止访问
          console.error('Forbidden - 403', data);
          // TODO: 显示禁止访问的提示
          break;
        case 404: // 未找到
          console.error('Not Found - 404', data);
          break;
        case 500: // 服务器内部错误
          console.error('Server Error - 500', data);
          // TODO: 显示通用服务器错误提示
          break;
        default:
          console.error(`Error ${status}`, data);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('No response received:', error.request);
      // TODO: 处理网络错误或服务器无响应
    } else {
      // 发送请求时出了点问题
      console.error('Error setting up request:', error.message);
    }
    // 应该向上抛出错误，让调用方也能处理
    return Promise.reject(error);
  }
);

export default apiClient;
```
*   **使用:** 在应用的任何地方导入 `apiClient` 并使用它来发起 HTTP 请求。
*   **数据获取库:** 考虑使用 SWR 或 React Query 来简化数据获取、缓存、状态同步和错误处理，它们可以与 Axios 很好地集成。

## **11\. 错误处理机制 (Error Handling Mechanism)**

(对应 PRD 第 7 节)

### **11.1. React Error Boundaries (FR-301)**

*   **创建 Error Boundary 组件 (`ErrorBoundary.tsx`):**
    ```typescript
    // components/ErrorBoundary.tsx
    import React, { Component, ErrorInfo, ReactNode } from 'react';
    
    interface Props {
      children: ReactNode;
      fallbackUI?: ReactNode; // 可选的自定义回退 UI
    }
    
    interface State {
      hasError: boolean;
      error?: Error;
    }
    
    class ErrorBoundary extends Component<Props, State> {
      public state: State = {
        hasError: false,
      };
    
      public static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error };
      }
    
      public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error("Uncaught error:", error, errorInfo);
        // TODO: 可以将错误上报到 Sentry, LogRocket 等服务
      }
    
      public render() {
        if (this.state.hasError) {
          return this.props.fallbackUI || <h1>Sorry.. there was an error.</h1>;
        }
        return this.props.children;
      }
    }
    export default ErrorBoundary;
    ```
*   **使用:** 在 `_app.tsx` 中包裹整个应用，或在特定可能出错的 UI 部分使用。
    ```typescript
    // pages/_app.tsx
    // ...
    <ErrorBoundary fallbackUI={<p>An application error occurred.</p>}>
      <Component {...pageProps} />
    </ErrorBoundary>
    // ...
    ```

### **11.2. Next.js 错误处理 (FR-302)**

*   **自定义错误页面:** 创建 `pages/_error.tsx` 来处理 HTTP 错误 (如 404, 500)。
    ```typescript
    // pages/_error.tsx
    function Error({ statusCode }) {
      return (
        <p>
          {statusCode
            ? `An error ${statusCode} occurred on server`
            : 'An error occurred on client'}
        </p>
      );
    }
    
    Error.getInitialProps = ({ res, err }) => {
      const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
      return { statusCode };
    };
    
    export default Error;
    ```
*   Next.js 13+ (App Router) 中有不同的错误处理机制 (`error.js` 文件)。

### **11.3. 异步操作错误 (FR-304, FR-306)**

*   **`try...catch`:** 在使用 `async/await` 的异步函数中捕获错误。
    ```typescript
    async function fetchData() {
      try {
        const response = await apiClient.get('/data');
        // ... process data
      } catch (error) {
        console.error("Failed to fetch data:", error);
        // 显示用户友好的错误提示 (FR-306)
        // e.g., set একটা error state that a Toast component listens to
        showToast('Error fetching data. Please try again.', 'error');
      }
    }
    ```
*   **Promise `.catch()`:**
    ```typescript
    apiClient.get('/data')
      .then(response => { /* ... */ })
      .catch(error => {
        console.error("Failed to fetch data:", error);
        showToast('Error fetching data. Please try again.', 'error');
      });
    ```
*   **Axios 拦截器:** 已在 10.2 节中讨论，用于全局处理 API 错误。
*   **用户友好提示 (FR-306):**
    *   使用 Toast/Notification 组件显示非阻塞性错误。
    *   对于表单提交错误，在表单内或字段旁显示具体错误信息。
    *   避免直接显示技术性错误码或堆栈跟踪给用户。

## **12\. 构建与部署 (Build & Deployment)**

### **12.1. Next.js 构建命令**

*   `npm run build` 或 `yarn build`: 生成生产环境的优化构建包 (在 `.next` 目录)。
*   `npm run start` 或 `yarn start`: 启动生产服务器 (需要先执行 build)。

### **12.2. 推荐部署平台与配置要点**

*   **Vercel (首选):**
    *   由 Next.js 的创建者开发，对 Next.js 提供原生、深度集成和优化。
    *   通过 Git 集成实现 CI/CD，推送代码自动部署。
    *   自动处理 SSG, SSR, API Routes, Image Optimization。
    *   免费套餐适合个人项目和小团队。
    *   配置：连接 GitHub/GitLab/Bitbucket 仓库，Vercel 会自动检测 Next.js 项目并进行构建部署。环境变量可以在 Vercel 项目设置中配置。
*   **Netlify:**
    *   也提供对 Next.js 的良好支持 (通过 `netlify-plugin-nextjs`)。
    *   类似的 Git 集成和 CI/CD。
    *   配置：可能需要安装和配置 `netlify-plugin-nextjs`。
*   **其他平台 (AWS Amplify, Google Cloud Run, Docker):**
    *   如果需要更复杂的部署环境或已有云基础设施，可以将 Next.js 应用容器化 (使用 Docker) 或部署到支持 Node.js 的 PaaS/FaaS 平台。
    *   配置：需要手动配置构建流程、服务器环境、反向代理等。

## **13\. 代码规范与质量保证 (Code Standards & Quality Assurance)**

### **13.1. ESLint, Prettier 配置**

*   **ESLint:**
    *   安装: `eslint`, `eslint-plugin-react`, `eslint-plugin-react-hooks`, `eslint-plugin-jsx-a11y`, `@typescript-eslint/parser`, `@typescript-eslint/eslint-plugin`, `eslint-config-next`。
    *   配置 (`.eslintrc.json`): 继承 `next/core-web-vitals`，并根据项目需求添加规则。
*   **Prettier:**
    *   安装: `prettier`, `eslint-config-prettier` (解决与 ESLint 冲突), `eslint-plugin-prettier` (将 Prettier 作为 ESLint 规则运行)。
    *   配置 (`.prettierrc.json`): 定义代码格式化规则 (如缩进、分号、引号等)。
*   **VS Code 集成:** 安装 ESLint 和 Prettier 插件，并配置保存时自动格式化和修复。
*   **Git Hooks:** 使用 `husky` 和 `lint-staged` 在提交代码前自动运行 ESLint 和 Prettier。

### **13.2. 测试策略 (Testing Strategy)**

*   **单元测试 (Unit Tests):**
    *   **工具:** [`Jest`](https://jestjs.io/) (Next.js 内置或易于集成) + [`React Testing Library`](https://testing-library.com/docs/react-testing-library/intro/)。
    *   **目标:** 测试独立的 React 组件、工具函数、状态管理逻辑 (如 Zustand store 的 actions/reducers)。
    *   **关注点:** 组件是否根据 props 正确渲染、用户交互是否触发预期行为、函数是否返回正确输出。
*   **集成测试 (Integration Tests):**
    *   **工具:** Jest + React Testing Library。
    *   **目标:** 测试多个组件协同工作的场景，例如表单提交、页面导航、涉及 Context 或全局状态的交互。
*   **端到端测试 (E2E Tests) (可选，但推荐):**
    *   **工具:** [`Cypress`](https://www.cypress.io/) 或 [`Playwright`](https://playwright.dev/)。
    *   **目标:** 模拟真实用户在浏览器中的操作流程，测试完整的用户场景（如注册-登录-文件上传-转换-下载）。
*   **测试覆盖率:** 设定合理的测试覆盖率目标，并使用 Jest 的 `--coverage` 选项生成报告。

## **14\. 未来考虑的技术演进 (Future Technical Considerations)**

(对应 PRD 第 8 节)

*   **支持更多输入/输出文件格式:**
    *   前端可能需要调整文件类型校验。
    *   核心转换逻辑在后端，前端主要配合 API 变更。
*   **高级 Markdown 编辑和自定义选项:**
    *   可能需要集成更强大的前端 Markdown 编辑器 (如 `react-markdown-editor-lite`, `Milkdown`)。
    *   自定义选项的 UI 和状态管理。
*   **集成云存储服务:**
    *   前端需要实现 OAuth 流程与云服务提供商（Google Drive, Dropbox）的 API 交互。
    *   文件选择器可能需要替换为云服务的文件浏览器。
*   **桌面客户端或浏览器插件:**
    *   桌面客户端: 可以考虑使用 Electron, Tauri 等技术。
    *   浏览器插件: 标准 WebExtension API。
    *   两者都需要与核心 Web 应用的 API 进行交互，可能需要共享部分认证和业务逻辑。
*   **引入团队协作功能:**
    *   前端需要实现实时协作特性（可能涉及 WebSocket, CRDTs），或与支持协作的后端服务集成。
    *   用户权限管理、共享设置等 UI。
