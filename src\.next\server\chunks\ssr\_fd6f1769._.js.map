{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/layout/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/ClientLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/ClientLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/layout/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/ClientLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/ClientLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\n\nexport const metadata: Metadata = {\n  title: \"All to Markdown - Convert Any Document to AI-Friendly Markdown\",\n  description: \"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Make your content truly understandable by AI models like ChatGPT and Claude.\",\n  keywords: \"markdown converter, ai friendly format, chatgpt friendly, pdf to markdown, word to markdown, excel to markdown, html to markdown, image to markdown, website to markdown, url to markdown, doc to markdown, docx to markdown, xlsx to markdown, document conversion, ai readable\",\n  openGraph: {\n    title: \"All to Markdown - Convert Any Document to AI-Friendly Markdown\",\n    description: \"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.\",\n    url: \"https://alltomarkdown.com\",\n    siteName: \"All to Markdown\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"All to Markdown - Convert Any Document to AI-Friendly Markdown\",\n    description: \"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.\",\n  },\n};\n\nimport ClientLayout from '../components/layout/ClientLayout';\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return <ClientLayout>{children}</ClientLayout>;\n}\n"], "names": [], "mappings": ";;;;;AAqBA;;;AAlBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;;AAIe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBAAO,8OAAC,qIAAA,CAAA,UAAY;kBAAE;;;;;;AACxB", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}