(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[907],{7098:(e,n,t)=>{Promise.resolve().then(t.bind(t,8670))},7190:(e,n,t)=>{"use strict";t.d(n,{A:()=>s});var o=t(7985),a=t(1218);let r={common:{siteName:"All to Markdown",home:"Home",convert:"Convert",pricing:"Pricing",login:"Login",logout:"Logout",profile:"Profile",subscription:"Subscription",user:"User",footer:"\xa9 {{year}} All to Markdown. All rights reserved.",languageSwitcher:"Language",english:"English",chinese:"中文",userGroupPlaceholder:"N/A",paymentStatusPlaceholder:"N/A",openUserMenu:"Open user menu",userGroup:"User Group",paymentStatus:"Payment Status"},home:{title:"Convert Documents to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.",startConverting:"Start Converting",viewPricing:"View Pricing",aiTitle:"Why Markdown is Perfect for AI",aiDescription:"Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.",clearStructure:"Clear Structure",clearStructureDesc:"Markdown's simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.",enhancedResponses:"Enhanced AI Responses",enhancedResponsesDesc:"When feeding Markdown to AI like ChatGPT or Claude, you'll get more accurate responses because the AI can better understand context and relationships in your content.",noFormatting:"No Formatting Noise",noFormattingDesc:"Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.",convertAnyFormat:"Convert any document format to clean Markdown",convertDesc:"Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.",supportedFormats:"Supported File Formats",easyUpload:"Easy File Upload",easyUploadDesc:"Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.",advancedOptions:"Advanced Options",advancedOptionsDesc:"Customize your Markdown output with options for different Markdown flavors, image handling, and more.",batchProcessing:"Batch Processing",batchProcessingDesc:"Convert multiple files at once and download them individually or as a zip archive.",fastConversion:"Fast Conversion",fastConversionDesc:"Our optimized conversion engine processes your documents quickly, saving you time and effort."},convert:{title:"Convert to AI-Friendly Markdown",description:"Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.",aiTip:"AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.",startConversion:"Start Conversion",options:"Conversion Options",markdownFlavor:"Markdown Flavor",markdownFlavorDesc:"Select the Markdown specification to follow",aiOptimized:"AI-Optimized Format",aiOptimizedDesc:"Optimize output for AI models like ChatGPT and Claude",advancedOptions:"Advanced Options",imageHandling:"Image Handling",imageHandlingDesc:"How to handle images in the converted Markdown",enableImageDescription:"Generate descriptive text for images",enableImageDescriptionDesc:"Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.",imageDescriptionAttachmentMode:"Description Attachment Method:",attachmentModeKeepImage:"Keep Image",attachmentModeReplaceImage:"Replace Image",tableHandling:"Table Handling",tableHandlingDesc:"Table formatting style in the converted Markdown",successMessage:"Your file has been successfully converted to Markdown!",successMessagePlural:"{{count}} files have been successfully converted to Markdown!",aiSuccessTip:"Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.",download:"Download .md",downloadAll:"Download All (.zip)"},pricing:{title:"Pricing",subtitle:"Pricing plans for all needs",description:"Choose the perfect plan for your document conversion needs. All plans include our core conversion features.",free:"Free",pro:"Pro",enterprise:"Enterprise",mostPopular:"Most popular",monthly:"/month",yearly:"/year",getStarted:"Get started",subscribe:"Subscribe"},auth:{loginTitle:"Login",loginDescription:"Login to your account",emailLabel:"Email",passwordLabel:"Password",loginButton:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",signUpLink:"Sign up",registerTitle:"Sign Up",registerDescription:"Create a new account",confirmPasswordLabel:"Confirm Password",registerButton:"Sign Up",hasAccount:"Already have an account?",signInLink:"Sign in",orContinueWith:"Or continue with",github:"GitHub",google:"Google",magicLinkSent:"Magic link sent!",checkYourEmail:"Check your email for the magic link to login."}},i={common:{siteName:"All to Markdown",home:"首页",convert:"转换",pricing:"价格",login:"登录",logout:"退出登录",profile:"个人资料",subscription:"订阅状态",user:"用户",footer:"\xa9 {{year}} All to Markdown. 保留所有权利。",languageSwitcher:"语言",english:"English",chinese:"中文",userGroupPlaceholder:"暂无",paymentStatusPlaceholder:"暂无",openUserMenu:"打开用户菜单",userGroup:"用户组",paymentStatus:"支付状态",register:"注册"},home:{title:"将文档转换为AI友好的Markdown格式",description:"将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。",startConverting:"开始转换",viewPricing:"查看价格",aiTitle:"为什么Markdown对AI来说是完美的",aiDescription:"Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。",clearStructure:"清晰的结构",clearStructureDesc:"Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。",enhancedResponses:"增强的AI响应",enhancedResponsesDesc:"当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。",noFormatting:"没有格式噪音",noFormattingDesc:"与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。",convertAnyFormat:"将任何文档格式转换为清晰的Markdown",convertDesc:"我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。",supportedFormats:"支持的文件格式",easyUpload:"轻松上传文件",easyUploadDesc:"拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。",advancedOptions:"高级选项",advancedOptionsDesc:"使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。",batchProcessing:"批量处理",batchProcessingDesc:"一次转换多个文件，并单独下载或作为zip存档下载。",fastConversion:"快速转换",fastConversionDesc:"我们优化的转换引擎快速处理您的文档，节省您的时间和精力。"},convert:{title:"转换为AI友好的Markdown",description:"上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。",aiTip:"AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。",startConversion:"开始转换",options:"转换选项",markdownFlavor:"Markdown风格",markdownFlavorDesc:"选择要遵循的Markdown规范",aiOptimized:"AI优化格式",aiOptimizedDesc:"为ChatGPT和Claude等AI模型优化输出",advancedOptions:"高级选项",imageHandling:"图像处理",imageHandlingDesc:"如何处理转换后的Markdown中的图像",enableImageDescription:"为图片生成描述性文字",enableImageDescriptionDesc:"为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。",imageDescriptionAttachmentMode:"描述文字附加方式:",attachmentModeKeepImage:"保留图片",attachmentModeReplaceImage:"替换图片",tableHandling:"表格处理",tableHandlingDesc:"转换后的Markdown中的表格格式样式",successMessage:"您的文件已成功转换为Markdown！",successMessagePlural:"{{count}}个文件已成功转换为Markdown！",aiSuccessTip:"您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。",download:"下载.md",downloadAll:"下载全部(.zip)"},pricing:{title:"价格",subtitle:"满足所有需求的价格计划",description:"选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。",free:"免费版",pro:"专业版",enterprise:"企业版",mostPopular:"最受欢迎",monthly:"/月",yearly:"/年",getStarted:"开始使用",subscribe:"订阅"},auth:{loginTitle:"登录",loginDescription:"登录您的账户",emailLabel:"邮箱",passwordLabel:"密码",loginButton:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账户？",signUpLink:"注册",registerTitle:"注册",registerDescription:"创建一个新账户",confirmPasswordLabel:"确认密码",registerButton:"注册",hasAccount:"已有账户？",signInLink:"登录",orContinueWith:"或继续使用",github:"GitHub",google:"Google",magicLinkSent:"魔法链接已发送！",checkYourEmail:"请检查您的邮箱以获取魔法链接进行登录。"}},s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["common","auth"],t=(0,o.Q_)();return t.use(a.r9).init({lng:e,ns:n,fallbackLng:"en",interpolation:{escapeValue:!1},resources:{en:{...r},zh:{...i}}}),t}},8670:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>c});var o=t(5155),a=t(2115);let r=a.forwardRef(function(e,n){let{title:t,titleId:o,...r}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":o},r),t?a.createElement("title",{id:o},t):null,a.createElement("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z",clipRule:"evenodd"}))});var i=t(1218),s=t(7190);function l(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];return n.filter(Boolean).join(" ")}function c(){let{t:e,i18n:n}=(0,i.Bd)(["pricing","common"]),t=(0,s.A)(n.language,["pricing","common"]),[c]=a.useState("monthly"),d=[{name:t.t("pricing:free"),id:"tier-free",href:"/convert",price:{monthly:"$0",annually:"$0"},description:"Perfect for occasional use and small documents.",features:["Convert up to 5 files per day","Max file size: 5MB","Basic Markdown conversion","Standard support"],mostPopular:!1},{name:t.t("pricing:pro"),id:"tier-pro",href:"#",price:{monthly:"$15",annually:"$144"},description:"Ideal for regular users and content creators.",features:["Unlimited conversions","Max file size: 20MB","Advanced Markdown options","Batch processing","Priority support","No ads"],mostPopular:!0},{name:t.t("pricing:enterprise"),id:"tier-enterprise",href:"#",price:{monthly:"$39",annually:"$384"},description:"For teams and professional content creators.",features:["Everything in Pro","Max file size: 50MB","API access","Custom conversion options","Team management","Dedicated support","Advanced analytics"],mostPopular:!1}];return(0,o.jsx)("div",{className:"bg-white py-24 sm:py-32",children:(0,o.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"mx-auto max-w-4xl text-center",children:[(0,o.jsx)("h1",{className:"text-base font-semibold leading-7 text-blue-600",children:e("pricing:title")}),(0,o.jsx)("p",{className:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl",children:e("pricing:subtitle")})]}),(0,o.jsx)("p",{className:"mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600",children:e("pricing:description")}),(0,o.jsx)("div",{className:"mt-16 flex justify-center",children:(0,o.jsx)("div",{className:"grid grid-cols-1 gap-y-8 sm:grid-cols-2 sm:gap-x-8 lg:grid-cols-3",children:d.map(n=>(0,o.jsxs)("div",{className:l(n.mostPopular?"ring-2 ring-blue-600":"ring-1 ring-gray-200","rounded-3xl p-8 xl:p-10"),children:[(0,o.jsxs)("div",{className:"flex items-center justify-between gap-x-4",children:[(0,o.jsx)("h2",{id:n.id,className:"text-lg font-semibold leading-8 text-gray-900",children:n.name}),n.mostPopular?(0,o.jsx)("p",{className:"rounded-full bg-blue-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-blue-600",children:e("pricing:mostPopular")}):null]}),(0,o.jsx)("p",{className:"mt-4 text-sm leading-6 text-gray-600",children:n.description}),(0,o.jsxs)("p",{className:"mt-6 flex items-baseline gap-x-1",children:[(0,o.jsx)("span",{className:"text-4xl font-bold tracking-tight text-gray-900",children:"annually"===c?n.price.annually:n.price.monthly}),(0,o.jsx)("span",{className:"text-sm font-semibold leading-6 text-gray-600",children:"annually"===c?e("pricing:yearly"):e("pricing:monthly")})]}),(0,o.jsx)("a",{href:n.href,"aria-describedby":n.id,className:l(n.mostPopular?"bg-blue-600 text-white shadow-sm hover:bg-blue-500":"text-blue-600 ring-1 ring-inset ring-blue-200 hover:ring-blue-300","mt-6 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"),children:n.name===t.t("pricing:free")?e("pricing:getStarted"):e("pricing:subscribe")}),(0,o.jsx)("ul",{role:"list",className:"mt-8 space-y-3 text-sm leading-6 text-gray-600",children:n.features.map(e=>(0,o.jsxs)("li",{className:"flex gap-x-3",children:[(0,o.jsx)(r,{className:"h-6 w-5 flex-none text-blue-600","aria-hidden":"true"}),e]},e))})]},n.id))})})]})})}}},e=>{var n=n=>e(e.s=n);e.O(0,[218,985,441,684,358],()=>n(7098)),_N_E=e.O()}]);