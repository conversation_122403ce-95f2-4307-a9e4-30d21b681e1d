"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[553],{822:(e,t,n)=>{n.d(t,{A:()=>en});let{entries:r,setPrototypeOf:o,isFrozen:i,getPrototypeOf:a,getOwnPropertyDescriptor:l}=Object,{freeze:u,seal:s,create:c}=Object,{apply:d,construct:f}="undefined"!=typeof Reflect&&Reflect;u||(u=function(e){return e}),s||(s=function(e){return e}),d||(d=function(e,t,n){return e.apply(t,n)}),f||(f=function(e,t){return new e(...t)});let p=N(Array.prototype.forEach),m=N(Array.prototype.lastIndexOf),g=N(Array.prototype.pop),h=N(Array.prototype.push),v=N(Array.prototype.splice),y=N(String.prototype.toLowerCase),E=N(String.prototype.toString),b=N(String.prototype.match),T=N(String.prototype.replace),w=N(String.prototype.indexOf),A=N(String.prototype.trim),S=N(Object.prototype.hasOwnProperty),L=N(RegExp.prototype.test),C=(Z=TypeError,function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return f(Z,t)});function N(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return d(e,t,r)}}function k(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;o&&o(e,null);let r=t.length;for(;r--;){let o=t[r];if("string"==typeof o){let e=n(o);e!==o&&(i(t)||(t[r]=e),o=e)}e[o]=!0}return e}function D(e){let t=c(null);for(let[n,o]of r(e))S(e,n)&&(Array.isArray(o)?t[n]=function(e){for(let t=0;t<e.length;t++)S(e,t)||(e[t]=null);return e}(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=D(o):t[n]=o);return t}function _(e,t){for(;null!==e;){let n=l(e,t);if(n){if(n.get)return N(n.get);if("function"==typeof n.value)return N(n.value)}e=a(e)}return function(){return null}}let O=u(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),I=u(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),R=u(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),x=u(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=u(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),P=u(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),F=u(["#text"]),H=u(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),U=u(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),j=u(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),z=u(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=s(/<%[\w\W]*|[\w\W]*%>/gm),G=s(/\$\{[\w\W]*/gm),Y=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),V=s(/^aria-[\-\w]+$/),q=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),K=s(/^(?:\w+script|data):/i),X=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),$=s(/^html$/i);var Z,J=Object.freeze({__proto__:null,ARIA_ATTR:V,ATTR_WHITESPACE:X,CUSTOM_ELEMENT:s(/^[a-z][.\w]*(-[.\w]+)+$/i),DATA_ATTR:Y,DOCTYPE_NAME:$,ERB_EXPR:W,IS_ALLOWED_URI:q,IS_SCRIPT_OR_DATA:K,MUSTACHE_EXPR:B,TMPLIT_EXPR:G});let Q={element:1,text:3,progressingInstruction:7,comment:8,document:9},ee=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null,r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));let o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}},et=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};var en=function e(){let t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,o=t=>e(t);if(o.version="3.2.5",o.removed=[],!n||!n.document||n.document.nodeType!==Q.document||!n.Element)return o.isSupported=!1,o;let{document:i}=n,a=i,l=a.currentScript,{DocumentFragment:s,HTMLTemplateElement:d,Node:f,Element:N,NodeFilter:B,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:V}=n,K=N.prototype,X=_(K,"cloneNode"),Z=_(K,"remove"),en=_(K,"nextSibling"),er=_(K,"childNodes"),eo=_(K,"parentNode");if("function"==typeof d){let e=i.createElement("template");e.content&&e.content.ownerDocument&&(i=e.content.ownerDocument)}let ei="",{implementation:ea,createNodeIterator:el,createDocumentFragment:eu,getElementsByTagName:es}=i,{importNode:ec}=a,ed=et();o.isSupported="function"==typeof r&&"function"==typeof eo&&ea&&void 0!==ea.createHTMLDocument;let{MUSTACHE_EXPR:ef,ERB_EXPR:ep,TMPLIT_EXPR:em,DATA_ATTR:eg,ARIA_ATTR:eh,IS_SCRIPT_OR_DATA:ev,ATTR_WHITESPACE:ey,CUSTOM_ELEMENT:eE}=J,{IS_ALLOWED_URI:eb}=J,eT=null,ew=k({},[...O,...I,...R,...M,...F]),eA=null,eS=k({},[...H,...U,...j,...z]),eL=Object.seal(c(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),eC=null,eN=null,ek=!0,eD=!0,e_=!1,eO=!0,eI=!1,eR=!0,ex=!1,eM=!1,eP=!1,eF=!1,eH=!1,eU=!1,ej=!0,ez=!1,eB=!0,eW=!1,eG={},eY=null,eV=k({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),eq=null,eK=k({},["audio","video","img","source","image","track"]),eX=null,e$=k({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),eZ="http://www.w3.org/1998/Math/MathML",eJ="http://www.w3.org/2000/svg",eQ="http://www.w3.org/1999/xhtml",e0=eQ,e1=!1,e2=null,e3=k({},[eZ,eJ,eQ],E),e4=k({},["mi","mo","mn","ms","mtext"]),e5=k({},["annotation-xml"]),e6=k({},["title","style","font","a","script"]),e9=null,e8=["application/xhtml+xml","text/html"],e7=null,te=null,tt=i.createElement("form"),tn=function(e){return e instanceof RegExp||e instanceof Function},tr=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!te||te!==e){if(e&&"object"==typeof e||(e={}),e=D(e),e7="application/xhtml+xml"===(e9=-1===e8.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE)?E:y,eT=S(e,"ALLOWED_TAGS")?k({},e.ALLOWED_TAGS,e7):ew,eA=S(e,"ALLOWED_ATTR")?k({},e.ALLOWED_ATTR,e7):eS,e2=S(e,"ALLOWED_NAMESPACES")?k({},e.ALLOWED_NAMESPACES,E):e3,eX=S(e,"ADD_URI_SAFE_ATTR")?k(D(e$),e.ADD_URI_SAFE_ATTR,e7):e$,eq=S(e,"ADD_DATA_URI_TAGS")?k(D(eK),e.ADD_DATA_URI_TAGS,e7):eK,eY=S(e,"FORBID_CONTENTS")?k({},e.FORBID_CONTENTS,e7):eV,eC=S(e,"FORBID_TAGS")?k({},e.FORBID_TAGS,e7):{},eN=S(e,"FORBID_ATTR")?k({},e.FORBID_ATTR,e7):{},eG=!!S(e,"USE_PROFILES")&&e.USE_PROFILES,ek=!1!==e.ALLOW_ARIA_ATTR,eD=!1!==e.ALLOW_DATA_ATTR,e_=e.ALLOW_UNKNOWN_PROTOCOLS||!1,eO=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,eI=e.SAFE_FOR_TEMPLATES||!1,eR=!1!==e.SAFE_FOR_XML,ex=e.WHOLE_DOCUMENT||!1,eF=e.RETURN_DOM||!1,eH=e.RETURN_DOM_FRAGMENT||!1,eU=e.RETURN_TRUSTED_TYPE||!1,eP=e.FORCE_BODY||!1,ej=!1!==e.SANITIZE_DOM,ez=e.SANITIZE_NAMED_PROPS||!1,eB=!1!==e.KEEP_CONTENT,eW=e.IN_PLACE||!1,eb=e.ALLOWED_URI_REGEXP||q,e0=e.NAMESPACE||eQ,e4=e.MATHML_TEXT_INTEGRATION_POINTS||e4,e5=e.HTML_INTEGRATION_POINTS||e5,eL=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&tn(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(eL.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&tn(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(eL.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(eL.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),eI&&(eD=!1),eH&&(eF=!0),eG&&(eT=k({},F),eA=[],!0===eG.html&&(k(eT,O),k(eA,H)),!0===eG.svg&&(k(eT,I),k(eA,U),k(eA,z)),!0===eG.svgFilters&&(k(eT,R),k(eA,U),k(eA,z)),!0===eG.mathMl&&(k(eT,M),k(eA,j),k(eA,z))),e.ADD_TAGS&&(eT===ew&&(eT=D(eT)),k(eT,e.ADD_TAGS,e7)),e.ADD_ATTR&&(eA===eS&&(eA=D(eA)),k(eA,e.ADD_ATTR,e7)),e.ADD_URI_SAFE_ATTR&&k(eX,e.ADD_URI_SAFE_ATTR,e7),e.FORBID_CONTENTS&&(eY===eV&&(eY=D(eY)),k(eY,e.FORBID_CONTENTS,e7)),eB&&(eT["#text"]=!0),ex&&k(eT,["html","head","body"]),eT.table&&(k(eT,["tbody"]),delete eC.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw C('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw C('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ei=(t=e.TRUSTED_TYPES_POLICY).createHTML("")}else void 0===t&&(t=ee(V,l)),null!==t&&"string"==typeof ei&&(ei=t.createHTML(""));u&&u(e),te=e}},to=k({},[...I,...R,...x]),ti=k({},[...M,...P]),ta=function(e){let t=eo(e);t&&t.tagName||(t={namespaceURI:e0,tagName:"template"});let n=y(e.tagName),r=y(t.tagName);return!!e2[e.namespaceURI]&&(e.namespaceURI===eJ?t.namespaceURI===eQ?"svg"===n:t.namespaceURI===eZ?"svg"===n&&("annotation-xml"===r||e4[r]):!!to[n]:e.namespaceURI===eZ?t.namespaceURI===eQ?"math"===n:t.namespaceURI===eJ?"math"===n&&e5[r]:!!ti[n]:e.namespaceURI===eQ?(t.namespaceURI!==eJ||!!e5[r])&&(t.namespaceURI!==eZ||!!e4[r])&&!ti[n]&&(e6[n]||!to[n]):"application/xhtml+xml"===e9&&!!e2[e.namespaceURI])},tl=function(e){h(o.removed,{element:e});try{eo(e).removeChild(e)}catch(t){Z(e)}},tu=function(e,t){try{h(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){h(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(eF||eH)try{tl(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},ts=function(e){let n=null,r=null;if(eP)e="<remove></remove>"+e;else{let t=b(e,/^[\r\n\t ]+/);r=t&&t[0]}"application/xhtml+xml"===e9&&e0===eQ&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");let o=t?t.createHTML(e):e;if(e0===eQ)try{n=new Y().parseFromString(o,e9)}catch(e){}if(!n||!n.documentElement){n=ea.createDocument(e0,"template",null);try{n.documentElement.innerHTML=e1?ei:o}catch(e){}}let a=n.body||n.documentElement;return(e&&r&&a.insertBefore(i.createTextNode(r),a.childNodes[0]||null),e0===eQ)?es.call(n,ex?"html":"body")[0]:ex?n.documentElement:a},tc=function(e){return el.call(e.ownerDocument||e,e,B.SHOW_ELEMENT|B.SHOW_COMMENT|B.SHOW_TEXT|B.SHOW_PROCESSING_INSTRUCTION|B.SHOW_CDATA_SECTION,null)},td=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},tf=function(e){return"function"==typeof f&&e instanceof f};function tp(e,t,n){p(e,e=>{e.call(o,t,n,te)})}let tm=function(e){let t=null;if(tp(ed.beforeSanitizeElements,e,null),td(e))return tl(e),!0;let n=e7(e.nodeName);if(tp(ed.uponSanitizeElement,e,{tagName:n,allowedTags:eT}),e.hasChildNodes()&&!tf(e.firstElementChild)&&L(/<[/\w!]/g,e.innerHTML)&&L(/<[/\w!]/g,e.textContent)||e.nodeType===Q.progressingInstruction||eR&&e.nodeType===Q.comment&&L(/<[/\w]/g,e.data))return tl(e),!0;if(!eT[n]||eC[n]){if(!eC[n]&&th(n)&&(eL.tagNameCheck instanceof RegExp&&L(eL.tagNameCheck,n)||eL.tagNameCheck instanceof Function&&eL.tagNameCheck(n)))return!1;if(eB&&!eY[n]){let t=eo(e)||e.parentNode,n=er(e)||e.childNodes;if(n&&t){let r=n.length;for(let o=r-1;o>=0;--o){let r=X(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,en(e))}}}return tl(e),!0}return e instanceof N&&!ta(e)||("noscript"===n||"noembed"===n||"noframes"===n)&&L(/<\/no(script|embed|frames)/i,e.innerHTML)?(tl(e),!0):(eI&&e.nodeType===Q.text&&(t=e.textContent,p([ef,ep,em],e=>{t=T(t,e," ")}),e.textContent!==t&&(h(o.removed,{element:e.cloneNode()}),e.textContent=t)),tp(ed.afterSanitizeElements,e,null),!1)},tg=function(e,t,n){if(ej&&("id"===t||"name"===t)&&(n in i||n in tt))return!1;if(eD&&!eN[t]&&L(eg,t));else if(ek&&L(eh,t));else if(!eA[t]||eN[t]){if(!(th(e)&&(eL.tagNameCheck instanceof RegExp&&L(eL.tagNameCheck,e)||eL.tagNameCheck instanceof Function&&eL.tagNameCheck(e))&&(eL.attributeNameCheck instanceof RegExp&&L(eL.attributeNameCheck,t)||eL.attributeNameCheck instanceof Function&&eL.attributeNameCheck(t))||"is"===t&&eL.allowCustomizedBuiltInElements&&(eL.tagNameCheck instanceof RegExp&&L(eL.tagNameCheck,n)||eL.tagNameCheck instanceof Function&&eL.tagNameCheck(n))))return!1}else if(eX[t]);else if(L(eb,T(n,ey,"")));else if(("src"===t||"xlink:href"===t||"href"===t)&&"script"!==e&&0===w(n,"data:")&&eq[e]);else if(e_&&!L(ev,T(n,ey,"")));else if(n)return!1;return!0},th=function(e){return"annotation-xml"!==e&&b(e,eE)},tv=function(e){tp(ed.beforeSanitizeAttributes,e,null);let{attributes:n}=e;if(!n||td(e))return;let r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:eA,forceKeepAttr:void 0},i=n.length;for(;i--;){let{name:a,namespaceURI:l,value:u}=n[i],s=e7(a),c="value"===a?u:A(u);if(r.attrName=s,r.attrValue=c,r.keepAttr=!0,r.forceKeepAttr=void 0,tp(ed.uponSanitizeAttribute,e,r),c=r.attrValue,ez&&("id"===s||"name"===s)&&(tu(a,e),c="user-content-"+c),eR&&L(/((--!?|])>)|<\/(style|title)/i,c)){tu(a,e);continue}if(r.forceKeepAttr||(tu(a,e),!r.keepAttr))continue;if(!eO&&L(/\/>/i,c)){tu(a,e);continue}eI&&p([ef,ep,em],e=>{c=T(c,e," ")});let d=e7(e.nodeName);if(tg(d,s,c)){if(t&&"object"==typeof V&&"function"==typeof V.getAttributeType)if(l);else switch(V.getAttributeType(d,s)){case"TrustedHTML":c=t.createHTML(c);break;case"TrustedScriptURL":c=t.createScriptURL(c)}try{l?e.setAttributeNS(l,a,c):e.setAttribute(a,c),td(e)?tl(e):g(o.removed)}catch(e){}}}tp(ed.afterSanitizeAttributes,e,null)},ty=function e(t){let n=null,r=tc(t);for(tp(ed.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)tp(ed.uponSanitizeShadowNode,n,null),tm(n),tv(n),n.content instanceof s&&e(n.content);tp(ed.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,i=null,l=null,u=null;if((e1=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!tf(e))if("function"==typeof e.toString){if("string"!=typeof(e=e.toString()))throw C("dirty is not a string, aborting")}else throw C("toString is not a function");if(!o.isSupported)return e;if(eM||tr(n),o.removed=[],"string"==typeof e&&(eW=!1),eW){if(e.nodeName){let t=e7(e.nodeName);if(!eT[t]||eC[t])throw C("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof f)(i=(r=ts("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType===Q.element&&"BODY"===i.nodeName||"HTML"===i.nodeName?r=i:r.appendChild(i);else{if(!eF&&!eI&&!ex&&-1===e.indexOf("<"))return t&&eU?t.createHTML(e):e;if(!(r=ts(e)))return eF?null:eU?ei:""}r&&eP&&tl(r.firstChild);let c=tc(eW?e:r);for(;l=c.nextNode();)tm(l),tv(l),l.content instanceof s&&ty(l.content);if(eW)return e;if(eF){if(eH)for(u=eu.call(r.ownerDocument);r.firstChild;)u.appendChild(r.firstChild);else u=r;return(eA.shadowroot||eA.shadowrootmode)&&(u=ec.call(a,u,!0)),u}let d=ex?r.outerHTML:r.innerHTML;return ex&&eT["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&L($,r.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+d),eI&&p([ef,ep,em],e=>{d=T(d,e," ")}),t&&eU?t.createHTML(d):d},o.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};tr(e),eM=!0},o.clearConfig=function(){te=null,eM=!1},o.isValidAttribute=function(e,t,n){return te||tr({}),tg(e7(e),e7(t),n)},o.addHook=function(e,t){"function"==typeof t&&h(ed[e],t)},o.removeHook=function(e,t){if(void 0!==t){let n=m(ed[e],t);return -1===n?void 0:v(ed[e],n,1)[0]}return g(ed[e])},o.removeHooks=function(e){ed[e]=[]},o.removeAllHooks=function(){ed=et()},o}()},1368:(e,t,n)=>{let r;n.d(t,{A:()=>l});let o={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},i=new Uint8Array(16),a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let l=function(e,t,n){if(o.randomUUID&&!t&&!e)return o.randomUUID();let l=(e=e||{}).random??e.rng?.()??function(){if(!r){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(i)}();if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((n=n||0)<0||n+16>t.length)throw RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[n+e]=l[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(l)}},9731:(e,t,n)=>{n.d(t,{EN:()=>eM});var r,o,i,a=n(2115);let l="undefined"!=typeof document?a.useLayoutEffect:()=>{};function u(e){return e.nativeEvent=e,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function s(e){let t=(0,a.useRef)({isFocused:!1,observer:null});l(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let n=function(e){let t=(0,a.useRef)(null);return l(()=>{t.current=e},[e]),(0,a.useCallback)((...e)=>{let n=t.current;return null==n?void 0:n(...e)},[])}(t=>{null==e||e(t)});return(0,a.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let r=e.target;r.addEventListener("focusout",e=>{t.current.isFocused=!1,r.disabled&&n(u(e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&r.disabled){var e;null==(e=t.current.observer)||e.disconnect();let n=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:n})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:n}))}}),t.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[n])}function c(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null==(t=window.navigator.userAgentData)?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function d(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null==(t=window.navigator.userAgentData)?void 0:t.platform)||window.navigator.platform)}function f(e){let t=null;return()=>(null==t&&(t=e()),t)}let p=f(function(){return d(/^Mac/i)}),m=f(function(){return d(/^iPhone/i)}),g=f(function(){return d(/^iPad/i)||p()&&navigator.maxTouchPoints>1}),h=f(function(){return m()||g()});f(function(){return p()||h()}),f(function(){return c(/AppleWebKit/i)&&!v()});let v=f(function(){return c(/Chrome/i)}),y=f(function(){return c(/Android/i)});f(function(){return c(/Firefox/i)});let E=e=>{var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document},b=e=>e&&"window"in e&&e.window===e?e:E(e).defaultView||window,T=null,w=new Set,A=new Map,S=!1,L=!1,C={Tab:!0,Escape:!0};function N(e,t){for(let n of w)n(e,t)}function k(e){S=!0,e.metaKey||!p()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(T="keyboard",N("keyboard",e))}function D(e){T="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(S=!0,N("pointer",e))}function _(e){(0===e.mozInputSource&&e.isTrusted||(y()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType))&&(S=!0,T="virtual")}function O(e){e.target!==window&&e.target!==document&&e.isTrusted&&(S||L||(T="virtual",N("virtual",e)),S=!1,L=!1)}function I(){S=!1,L=!0}function R(e){if("undefined"==typeof window||A.get(b(e)))return;let t=b(e),n=E(e),r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){S=!0,r.apply(this,arguments)},n.addEventListener("keydown",k,!0),n.addEventListener("keyup",k,!0),n.addEventListener("click",_,!0),t.addEventListener("focus",O,!0),t.addEventListener("blur",I,!1),"undefined"!=typeof PointerEvent&&(n.addEventListener("pointerdown",D,!0),n.addEventListener("pointermove",D,!0),n.addEventListener("pointerup",D,!0)),t.addEventListener("beforeunload",()=>{x(e)},{once:!0}),A.set(t,{focus:r})}let x=(e,t)=>{let n=b(e),r=E(e);t&&r.removeEventListener("DOMContentLoaded",t),A.has(n)&&(n.HTMLElement.prototype.focus=A.get(n).focus,r.removeEventListener("keydown",k,!0),r.removeEventListener("keyup",k,!0),r.removeEventListener("click",_,!0),n.removeEventListener("focus",O,!0),n.removeEventListener("blur",I,!1),"undefined"!=typeof PointerEvent&&(r.removeEventListener("pointerdown",D,!0),r.removeEventListener("pointermove",D,!0),r.removeEventListener("pointerup",D,!0)),A.delete(n))};function M(){return"pointer"!==T}"undefined"!=typeof document&&function(e){let t,n=E(void 0);"loading"!==n.readyState?R(void 0):(t=()=>{R(e)},n.addEventListener("DOMContentLoaded",t)),()=>x(e,t)}();let P=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function F(e,t){1;return!!t&&!!e&&e.contains(t)}let H=(e=document)=>{var t;1;return e.activeElement};function U(e){return 0,e.target}function j(){let e=(0,a.useRef)(new Map),t=(0,a.useCallback)((t,n,r,o)=>{let i=(null==o?void 0:o.once)?(...t)=>{e.current.delete(r),r(...t)}:r;e.current.set(r,{type:n,eventTarget:t,fn:i,options:o}),t.addEventListener(n,i,o)},[]),n=(0,a.useCallback)((t,n,r,o)=>{var i;let a=(null==(i=e.current.get(r))?void 0:i.fn)||r;t.removeEventListener(n,a,o),e.current.delete(r)},[]),r=(0,a.useCallback)(()=>{e.current.forEach((e,t)=>{n(e.eventTarget,e.type,t,e.options)})},[n]);return(0,a.useEffect)(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}let z=!1,B=0;function W(e){"touch"===e.pointerType&&(z=!0,setTimeout(()=>{z=!1},50))}function G(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent&&document.addEventListener("pointerup",W),B++,()=>{--B>0||"undefined"!=typeof PointerEvent&&document.removeEventListener("pointerup",W)}}var Y=Object.defineProperty,V=(e,t,n)=>t in e?Y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,q=(e,t,n)=>(V(e,"symbol"!=typeof t?t+"":t,n),n);class K{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){q(this,"current",this.detect()),q(this,"handoffState","pending"),q(this,"currentId",0)}}let X=new K;function $(e){var t,n;return X.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}function Z(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add(()=>e.removeEventListener(n,r,o))),requestAnimationFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let o=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];let o=setTimeout(...n);return t.add(()=>clearTimeout(o))},microTask(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];let i={current:!0};return e=()=>{i.current&&r[0]()},"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e})),t.add(()=>{i.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(e){let t=Z();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function J(){let[e]=(0,a.useState)(Z);return(0,a.useEffect)(()=>()=>e.dispose(),[e]),e}let Q=(e,t)=>{X.isServer?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)},ee=function(e){let t,n=(t=(0,a.useRef)(e),Q(()=>{t.current=e},[e]),t);return a.useCallback(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.current(...t)},[n])},et=Symbol();function en(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=(0,a.useRef)(t);(0,a.useEffect)(()=>{r.current=t},[t]);let o=ee(e=>{for(let t of r.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[et]))?void 0:o}var er=n(9509);void 0!==er&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==er?void 0:er.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(o=null==Element?void 0:Element.prototype)?void 0:o.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var eo=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(eo||{});let ei=(0,a.createContext)(()=>{});function ea(e){let{value:t,children:n}=e;return a.createElement(ei.Provider,{value:t},n)}let el=(0,a.createContext)(null);el.displayName="OpenClosedContext";var eu=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(eu||{});function es(e){let{value:t,children:n}=e;return a.createElement(el.Provider,{value:t},n)}function ec(e){let{children:t}=e;return a.createElement(el.Provider,{value:null},t)}function ed(e,t){for(var n=arguments.length,r=Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,ed),i}function ef(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var ep=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(ep||{}),em=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(em||{});function eg(){let e,t,n=(e=(0,a.useRef)([]),t=(0,a.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t});return(0,a.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:i,visible:a=!0,name:l,mergeRefs:u}=e;u=null!=u?u:ev;let s=ey(n,t);if(a)return eh(s,r,o,l,u);let c=null!=i?i:0;if(2&c){let{static:e=!1,...t}=s;if(e)return eh(t,r,o,l,u)}if(1&c){let{unmount:e=!0,...t}=s;return ed(+!e,{0:()=>null,1:()=>eh({...t,hidden:!0,style:{display:"none"}},r,o,l,u)})}return eh(s,r,o,l,u)})({mergeRefs:n,...e}),[n])}function eh(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,{as:i=n,children:l,refName:u="ref",...s}=ew(e,["unmount","static"]),c=void 0!==e.ref?{[u]:e.ref}:{},d="function"==typeof l?l(t):l;"className"in s&&s.className&&"function"==typeof s.className&&(s.className=s.className(t)),s["aria-labelledby"]&&s["aria-labelledby"]===s.id&&(s["aria-labelledby"]=void 0);let f={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(f["data-headlessui-state"]=n.join(" "),n))f["data-".concat(e)]=""}if(i===a.Fragment&&(Object.keys(eT(s)).length>0||Object.keys(eT(f)).length>0))if(!(0,a.isValidElement)(d)||Array.isArray(d)&&d.length>1){if(Object.keys(eT(s)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(r,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(eT(s)).concat(Object.keys(eT(f))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var p;let e=d.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return ef(t(...n),s.className)}:ef(t,s.className),r=ey(d.props,eT(ew(s,["ref"])));for(let e in f)e in r&&delete f[e];return(0,a.cloneElement)(d,Object.assign({},r,f,c,{ref:o((p=d,a.version.split(".")[0]>="19"?p.props.ref:p.ref),c.ref)},n?{className:n}:{}))}return(0,a.createElement)(i,Object.assign({},ew(s,["ref"]),i!==a.Fragment&&c,i!==a.Fragment&&f),d)}function ev(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function ey(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(o[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function eE(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];for(let e in o)Object.assign(r,{[e](){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];for(let t of o[e])null==t||t(...n)}});return r}function eb(e){var t;return Object.assign((0,a.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function eT(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function ew(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}let eA=null!=(i=a.startTransition)?i:function(e){e()};var eS=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(eS||{}),eL=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eL||{}),eC=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.SetButtonElement=4]="SetButtonElement",e[e.SetPanelElement=5]="SetPanelElement",e))(eC||{});let eN={0:e=>({...e,disclosureState:ed(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId},4:(e,t)=>e.buttonElement===t.element?e:{...e,buttonElement:t.element},5:(e,t)=>e.panelElement===t.element?e:{...e,panelElement:t.element}},ek=(0,a.createContext)(null);function eD(e){let t=(0,a.useContext)(ek);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Disclosure /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,eD),t}return t}ek.displayName="DisclosureContext";let e_=(0,a.createContext)(null);e_.displayName="DisclosureAPIContext";let eO=(0,a.createContext)(null);function eI(e,t){return ed(t.type,eN,e,t)}eO.displayName="DisclosurePanelContext";let eR=a.Fragment,ex=ep.RenderStrategy|ep.Static,eM=Object.assign(eb(function(e,t){let{defaultOpen:n=!1,...r}=e,o=(0,a.useRef)(null),i=en(t,function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[et]:t})}(e=>{o.current=e},void 0===e.as||e.as===a.Fragment)),l=(0,a.useReducer)(eI,{disclosureState:+!n,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:u,buttonId:s},c]=l,d=ee(e=>{c({type:1});let t=$(o);if(!t||!s)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(s):t.getElementById(s);null==n||n.focus()}),f=(0,a.useMemo)(()=>({close:d}),[d]),p=(0,a.useMemo)(()=>({open:0===u,close:d}),[u,d]),m=eg();return a.createElement(ek.Provider,{value:l},a.createElement(e_.Provider,{value:f},a.createElement(ea,{value:d},a.createElement(es,{value:ed(u,{0:eu.Open,1:eu.Closed})},m({ourProps:{ref:i},theirProps:r,slot:p,defaultTag:eR,name:"Disclosure"})))))}),{Button:eb(function(e,t){var n;let r=(0,a.useId)(),{id:o="headlessui-disclosure-button-".concat(r),disabled:i=!1,autoFocus:l=!1,...c}=e,[d,f]=eD("Disclosure.Button"),p=(0,a.useContext)(eO),m=null!==p&&p===d.panelId,g=en((0,a.useRef)(null),t,ee(e=>{if(!m)return f({type:4,element:e})}));(0,a.useEffect)(()=>{if(!m)return f({type:2,buttonId:o}),()=>{f({type:2,buttonId:null})}},[o,f,m]);let h=ee(e=>{var t;if(m){if(1===d.disclosureState)return;switch(e.key){case eS.Space:case eS.Enter:e.preventDefault(),e.stopPropagation(),f({type:0}),null==(t=d.buttonElement)||t.focus()}}else switch(e.key){case eS.Space:case eS.Enter:e.preventDefault(),e.stopPropagation(),f({type:0})}}),v=ee(e=>{e.key===eS.Space&&e.preventDefault()}),y=ee(e=>{var t;(function(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r})(e.currentTarget)||i||(m?(f({type:0}),null==(t=d.buttonElement)||t.focus()):f({type:0}))}),{isFocusVisible:T,focusProps:A}=function(e={}){var t,n,r;let{autoFocus:o=!1,isTextInput:i,within:l}=e,c=(0,a.useRef)({isFocused:!1,isFocusVisible:o||M()}),[d,f]=(0,a.useState)(!1),[p,m]=(0,a.useState)(()=>c.current.isFocused&&c.current.isFocusVisible),g=(0,a.useCallback)(()=>m(c.current.isFocused&&c.current.isFocusVisible),[]),h=(0,a.useCallback)(e=>{c.current.isFocused=e,f(e),g()},[g]);t=e=>{c.current.isFocusVisible=e,g()},n=[],r={isTextInput:i},R(),(0,a.useEffect)(()=>{let e=(e,n)=>{(function(e,t,n){let r=E(null==n?void 0:n.target),o="undefined"!=typeof window?b(null==n?void 0:n.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?b(null==n?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,a="undefined"!=typeof window?b(null==n?void 0:n.target).HTMLElement:HTMLElement,l="undefined"!=typeof window?b(null==n?void 0:n.target).KeyboardEvent:KeyboardEvent;return!((e=e||r.activeElement instanceof o&&!P.has(r.activeElement.type)||r.activeElement instanceof i||r.activeElement instanceof a&&r.activeElement.isContentEditable)&&"keyboard"===t&&n instanceof l&&!C[n.key])})(!!(null==r?void 0:r.isTextInput),e,n)&&t(M())};return w.add(e),()=>{w.delete(e)}},n);let{focusProps:v}=function(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e,i=(0,a.useCallback)(e=>{if(e.target===e.currentTarget)return r&&r(e),o&&o(!1),!0},[r,o]),l=s(i),u=(0,a.useCallback)(e=>{let t=E(e.target),r=t?H(t):H();e.target===e.currentTarget&&r===U(e.nativeEvent)&&(n&&n(e),o&&o(!0),l(e))},[o,n,l]);return{focusProps:{onFocus:!t&&(n||o||r)?u:void 0,onBlur:!t&&(r||o)?i:void 0}}}({isDisabled:l,onFocusChange:h}),{focusWithinProps:y}=function(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,i=(0,a.useRef)({isFocusWithin:!1}),{addGlobalListener:l,removeAllGlobalListeners:c}=j(),d=(0,a.useCallback)(e=>{e.currentTarget.contains(e.target)&&i.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(i.current.isFocusWithin=!1,c(),n&&n(e),o&&o(!1))},[n,o,i,c]),f=s(d),p=(0,a.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=E(e.target),n=H(t);if(!i.current.isFocusWithin&&n===U(e.nativeEvent)){r&&r(e),o&&o(!0),i.current.isFocusWithin=!0,f(e);let n=e.currentTarget;l(t,"focus",e=>{if(i.current.isFocusWithin&&!F(n,e.target)){let r=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});Object.defineProperty(r,"target",{value:n}),Object.defineProperty(r,"currentTarget",{value:n}),d(u(r))}},{capture:!0})}},[r,o,f,l,d]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:p,onBlur:d}}}({isDisabled:!l,onFocusWithinChange:h});return{isFocused:d,isFocusVisible:p,focusProps:l?y:v}}({autoFocus:l}),{isHovered:S,hoverProps:L}=function(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[i,l]=(0,a.useState)(!1),u=(0,a.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,a.useEffect)(G,[]);let{addGlobalListener:s,removeAllGlobalListeners:c}=j(),{hoverProps:d,triggerHoverEnd:f}=(0,a.useMemo)(()=>{let e=(e,r)=>{if(u.pointerType=r,o||"touch"===r||u.isHovered||!e.currentTarget.contains(e.target))return;u.isHovered=!0;let a=e.currentTarget;u.target=a,s(E(e.target),"pointerover",e=>{u.isHovered&&u.target&&!F(u.target,e.target)&&i(e,e.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:a,pointerType:r}),n&&n(!0),l(!0)},i=(e,t)=>{let o=u.target;u.pointerType="",u.target=null,"touch"!==t&&u.isHovered&&o&&(u.isHovered=!1,c(),r&&r({type:"hoverend",target:o,pointerType:t}),n&&n(!1),l(!1))},a={};return"undefined"!=typeof PointerEvent&&(a.onPointerEnter=t=>{z&&"mouse"===t.pointerType||e(t,t.pointerType)},a.onPointerLeave=e=>{!o&&e.currentTarget.contains(e.target)&&i(e,e.pointerType)}),{hoverProps:a,triggerHoverEnd:i}},[t,n,r,o,u,s,c]);return(0,a.useEffect)(()=>{o&&f({currentTarget:u.target},u.pointerType)},[o]),{hoverProps:d,isHovered:i}}({isDisabled:i}),{pressed:N,pressProps:k}=function(){let{disabled:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,a.useRef)(null),[n,r]=(0,a.useState)(!1),o=J(),i=ee(()=>{t.current=null,r(!1),o.dispose()}),l=ee(e=>{if(o.dispose(),null===t.current){t.current=e.currentTarget,r(!0);{let n=$(e.currentTarget);o.addEventListener(n,"pointerup",i,!1),o.addEventListener(n,"pointermove",e=>{if(t.current){var n,o;let i,a;r((i=e.width/2,a=e.height/2,n={top:e.clientY-a,right:e.clientX+i,bottom:e.clientY+a,left:e.clientX-i},o=t.current.getBoundingClientRect(),!(!n||!o||n.right<o.left||n.left>o.right||n.bottom<o.top||n.top>o.bottom)))}},!1),o.addEventListener(n,"pointercancel",i,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:l,onPointerUp:i,onClick:i}}}({disabled:i}),D=(0,a.useMemo)(()=>({open:0===d.disclosureState,hover:S,active:N,disabled:i,focus:T,autofocus:l}),[d,S,N,T,i,l]),_=(n=d.buttonElement,(0,a.useMemo)(()=>{var t;if(e.type)return e.type;let r=null!=(t=e.as)?t:"button";if("string"==typeof r&&"button"===r.toLowerCase()||(null==n?void 0:n.tagName)==="BUTTON"&&!n.hasAttribute("type"))return"button"},[e.type,e.as,n])),O=m?eE({ref:g,type:_,disabled:i||void 0,autoFocus:l,onKeyDown:h,onClick:y},A,L,k):eE({ref:g,id:o,type:_,"aria-expanded":0===d.disclosureState,"aria-controls":d.panelElement?d.panelId:void 0,disabled:i||void 0,autoFocus:l,onKeyDown:h,onKeyUp:v,onClick:y},A,L,k);return eg()({ourProps:O,theirProps:c,slot:D,defaultTag:"button",name:"Disclosure.Button"})}),Panel:eb(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-disclosure-panel-".concat(n),transition:o=!1,...i}=e,[l,u]=eD("Disclosure.Panel"),{close:s}=function e(t){let n=(0,a.useContext)(e_);if(null===n){let n=Error("<".concat(t," /> is missing a parent <Disclosure /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(n,e),n}return n}("Disclosure.Panel"),[c,d]=(0,a.useState)(null),f=en(t,ee(e=>{eA(()=>u({type:5,element:e}))}),d);(0,a.useEffect)(()=>(u({type:3,panelId:r}),()=>{u({type:3,panelId:null})}),[r,u]);let p=(0,a.useContext)(el),[m,g]=function(e,t,n,r){let[o,i]=(0,a.useState)(n),{hasFlag:l,addFlag:u,removeFlag:s}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,a.useState)(e),r=(0,a.useCallback)(e=>n(e),[t]),o=(0,a.useCallback)(e=>n(t=>t|e),[t]),i=(0,a.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:(0,a.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,a.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),c=(0,a.useRef)(!1),d=(0,a.useRef)(!1);return Q(()=>{if(e){if(n&&i(!0),!t){n&&u(3);return}return function(e,t){let{prepare:n,run:r,done:o,inFlight:i}=t,a=Z();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:i}),a.nextFrame(()=>{r(),a.requestAnimationFrame(()=>{a.add(function(e,t){var n,r;let o=Z();if(!e)return o.dispose;let i=!1;o.add(()=>{i=!0});let a=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===a.length?t():Promise.allSettled(a.map(e=>e.finished)).then(()=>{i||t()}),o.dispose}(e,o))})}),a.dispose}(t,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,d.current||(n?(u(3),s(4)):(u(4),s(2)))},run(){d.current?n?(s(3),u(4)):(s(4),u(3)):n?s(1):u(1)},done(){var e;d.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(c.current=!1,s(7),n||i(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,J()]),e?[o,{closed:l(1),enter:l(2),leave:l(4),transition:l(2)||l(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(o,c,null!==p?(p&eu.Open)===eu.Open:0===l.disclosureState),h=(0,a.useMemo)(()=>({open:0===l.disclosureState,close:s}),[l.disclosureState,s]),v={ref:f,id:r,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(g)},y=eg();return a.createElement(ec,null,a.createElement(eO.Provider,{value:l.panelId},y({ourProps:v,theirProps:i,slot:h,defaultTag:"div",features:ex,visible:m,name:"Disclosure.Panel"})))})})}}]);