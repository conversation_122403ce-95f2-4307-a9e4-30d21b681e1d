(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{1851:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>m});var o=n(5155),a=n(2115),s=n(6874),r=n.n(s),i=n(5695),l=n(4983),c=n(4032),d=n(1218),u=n(7190);function m(){let{t:e,i18n:t}=(0,d.Bd)(["auth","common"]),n=(0,u.A)(t.language,["auth","common"]),[s,m]=(0,a.useState)(""),[g,p]=(0,a.useState)(""),[h,f]=(0,a.useState)(""),[w,b]=(0,a.useState)(!1),[v,y]=(0,a.useState)(null),[k,x]=(0,a.useState)(null),A=(0,i.useRouter)(),[I,M]=(0,a.useState)(null);(0,a.useEffect)(()=>{M((0,l.A)())},[]);let C=async e=>{if(e.preventDefault(),b(!0),y(null),x(null),!I){y(n.t("auth:supabaseNotInitialized",{ns:"auth",defaultValue:"Supabase client is not initialized."})),b(!1);return}if(g!==h){y(n.t("auth:passwordsDoNotMatch",{ns:"auth",defaultValue:"Passwords do not match."})),b(!1);return}try{let{data:e,error:t}=await I.auth.signUp({email:s,password:g,options:{emailRedirectTo:"".concat(window.location.origin,"/login")}});if(t)throw t;e.user&&!e.session?x(n.t("auth:registrationSuccessConfirmEmail",{ns:"auth",defaultValue:"Registration successful! Please check your email to confirm your account."})):e.user&&e.session?x(n.t("auth:registrationSuccessLoggedIn",{ns:"auth",defaultValue:"Registration successful! You are now logged in."})):x(n.t("auth:registrationSuccessConfirmLink",{ns:"auth",defaultValue:"Registration successful! Please check your email for a confirmation link."})),m(""),p(""),f(""),setTimeout(()=>{A.push("/login")},3e3)}catch(e){e instanceof c.lR?y(e.message):y(n.t("auth:registrationError",{ns:"auth",defaultValue:"An unexpected error occurred during registration."}))}finally{b(!1)}};return(0,o.jsxs)("div",{className:"flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,o.jsx)("h2",{className:"mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900",children:e("auth:registerTitle")})}),(0,o.jsx)("div",{className:"mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]",children:(0,o.jsxs)("div",{className:"bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12",children:[(0,o.jsxs)("form",{className:"space-y-6",onSubmit:C,children:[v&&(0,o.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,o.jsx)("div",{className:"flex",children:(0,o.jsx)("div",{className:"ml-3",children:(0,o.jsx)("h3",{className:"text-sm font-medium text-red-800",children:v})})})}),k&&(0,o.jsx)("div",{className:"rounded-md bg-green-50 p-4",children:(0,o.jsx)("div",{className:"flex",children:(0,o.jsx)("div",{className:"ml-3",children:(0,o.jsx)("h3",{className:"text-sm font-medium text-green-800",children:k})})})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:emailLabel")}),(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:s,onChange:e=>m(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:passwordLabel")}),(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:g,onChange:e=>p(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"confirm-password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:confirmPasswordLabel")}),(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,value:h,onChange:e=>f(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,o.jsx)("div",{children:(0,o.jsx)("button",{type:"submit",disabled:w,className:"flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:w?e("auth:creatingAccount",{ns:"auth",defaultValue:"Creating account..."}):e("auth:registerButton")})})]}),(0,o.jsxs)("p",{className:"mt-10 text-center text-sm text-gray-500",children:[e("auth:hasAccount")," ",(0,o.jsx)(r(),{href:"/login",className:"font-semibold leading-6 text-blue-600 hover:text-blue-500",children:e("auth:signInLink")})]})]})})]})}},4983:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(9724);let a=null,s=()=>{if(a)return a;let e="https://rpzceoedurujspnnyvkm.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._zuv48OJiram1Q_QXndbl6exkL3P8qrgI_MfybQzCEo";if(!e)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");if(!t)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");return a=(0,o.UU)(e,t,{auth:{debug:!0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{fetch:async(e,t)=>{console.log("[Supabase Debug] Fetch request to: ".concat(e)),console.log("[Supabase Debug] Fetch options:",t);try{let n=await fetch(e,t);return console.log("[Supabase Debug] Response status: ".concat(n.status)),n}catch(e){throw console.error("[Supabase Debug] Fetch error:",e),e}}}}),console.log("[Supabase Debug] Client initialized with URL:",e),a}},5695:(e,t,n)=>{"use strict";var o=n(8999);n.o(o,"usePathname")&&n.d(t,{usePathname:function(){return o.usePathname}}),n.o(o,"useRouter")&&n.d(t,{useRouter:function(){return o.useRouter}})},7190:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var o=n(7985),a=n(1218);let s={common:{siteName:"All to Markdown",home:"Home",convert:"Convert",pricing:"Pricing",login:"Login",logout:"Logout",profile:"Profile",subscription:"Subscription",user:"User",footer:"\xa9 {{year}} All to Markdown. All rights reserved.",languageSwitcher:"Language",english:"English",chinese:"中文",userGroupPlaceholder:"N/A",paymentStatusPlaceholder:"N/A",openUserMenu:"Open user menu",userGroup:"User Group",paymentStatus:"Payment Status"},home:{title:"Convert Documents to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.",startConverting:"Start Converting",viewPricing:"View Pricing",aiTitle:"Why Markdown is Perfect for AI",aiDescription:"Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.",clearStructure:"Clear Structure",clearStructureDesc:"Markdown's simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.",enhancedResponses:"Enhanced AI Responses",enhancedResponsesDesc:"When feeding Markdown to AI like ChatGPT or Claude, you'll get more accurate responses because the AI can better understand context and relationships in your content.",noFormatting:"No Formatting Noise",noFormattingDesc:"Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.",convertAnyFormat:"Convert any document format to clean Markdown",convertDesc:"Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.",supportedFormats:"Supported File Formats",easyUpload:"Easy File Upload",easyUploadDesc:"Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.",advancedOptions:"Advanced Options",advancedOptionsDesc:"Customize your Markdown output with options for different Markdown flavors, image handling, and more.",batchProcessing:"Batch Processing",batchProcessingDesc:"Convert multiple files at once and download them individually or as a zip archive.",fastConversion:"Fast Conversion",fastConversionDesc:"Our optimized conversion engine processes your documents quickly, saving you time and effort."},convert:{title:"Convert to AI-Friendly Markdown",description:"Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.",aiTip:"AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.",startConversion:"Start Conversion",options:"Conversion Options",markdownFlavor:"Markdown Flavor",markdownFlavorDesc:"Select the Markdown specification to follow",aiOptimized:"AI-Optimized Format",aiOptimizedDesc:"Optimize output for AI models like ChatGPT and Claude",advancedOptions:"Advanced Options",imageHandling:"Image Handling",imageHandlingDesc:"How to handle images in the converted Markdown",enableImageDescription:"Generate descriptive text for images",enableImageDescriptionDesc:"Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.",imageDescriptionAttachmentMode:"Description Attachment Method:",attachmentModeKeepImage:"Keep Image",attachmentModeReplaceImage:"Replace Image",tableHandling:"Table Handling",tableHandlingDesc:"Table formatting style in the converted Markdown",successMessage:"Your file has been successfully converted to Markdown!",successMessagePlural:"{{count}} files have been successfully converted to Markdown!",aiSuccessTip:"Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.",download:"Download .md",downloadAll:"Download All (.zip)"},pricing:{title:"Pricing",subtitle:"Pricing plans for all needs",description:"Choose the perfect plan for your document conversion needs. All plans include our core conversion features.",free:"Free",pro:"Pro",enterprise:"Enterprise",mostPopular:"Most popular",monthly:"/month",yearly:"/year",getStarted:"Get started",subscribe:"Subscribe"},auth:{loginTitle:"Login",loginDescription:"Login to your account",emailLabel:"Email",passwordLabel:"Password",loginButton:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",signUpLink:"Sign up",registerTitle:"Sign Up",registerDescription:"Create a new account",confirmPasswordLabel:"Confirm Password",registerButton:"Sign Up",hasAccount:"Already have an account?",signInLink:"Sign in",orContinueWith:"Or continue with",github:"GitHub",google:"Google",magicLinkSent:"Magic link sent!",checkYourEmail:"Check your email for the magic link to login."}},r={common:{siteName:"All to Markdown",home:"首页",convert:"转换",pricing:"价格",login:"登录",logout:"退出登录",profile:"个人资料",subscription:"订阅状态",user:"用户",footer:"\xa9 {{year}} All to Markdown. 保留所有权利。",languageSwitcher:"语言",english:"English",chinese:"中文",userGroupPlaceholder:"暂无",paymentStatusPlaceholder:"暂无",openUserMenu:"打开用户菜单",userGroup:"用户组",paymentStatus:"支付状态",register:"注册"},home:{title:"将文档转换为AI友好的Markdown格式",description:"将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。",startConverting:"开始转换",viewPricing:"查看价格",aiTitle:"为什么Markdown对AI来说是完美的",aiDescription:"Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。",clearStructure:"清晰的结构",clearStructureDesc:"Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。",enhancedResponses:"增强的AI响应",enhancedResponsesDesc:"当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。",noFormatting:"没有格式噪音",noFormattingDesc:"与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。",convertAnyFormat:"将任何文档格式转换为清晰的Markdown",convertDesc:"我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。",supportedFormats:"支持的文件格式",easyUpload:"轻松上传文件",easyUploadDesc:"拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。",advancedOptions:"高级选项",advancedOptionsDesc:"使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。",batchProcessing:"批量处理",batchProcessingDesc:"一次转换多个文件，并单独下载或作为zip存档下载。",fastConversion:"快速转换",fastConversionDesc:"我们优化的转换引擎快速处理您的文档，节省您的时间和精力。"},convert:{title:"转换为AI友好的Markdown",description:"上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。",aiTip:"AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。",startConversion:"开始转换",options:"转换选项",markdownFlavor:"Markdown风格",markdownFlavorDesc:"选择要遵循的Markdown规范",aiOptimized:"AI优化格式",aiOptimizedDesc:"为ChatGPT和Claude等AI模型优化输出",advancedOptions:"高级选项",imageHandling:"图像处理",imageHandlingDesc:"如何处理转换后的Markdown中的图像",enableImageDescription:"为图片生成描述性文字",enableImageDescriptionDesc:"为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。",imageDescriptionAttachmentMode:"描述文字附加方式:",attachmentModeKeepImage:"保留图片",attachmentModeReplaceImage:"替换图片",tableHandling:"表格处理",tableHandlingDesc:"转换后的Markdown中的表格格式样式",successMessage:"您的文件已成功转换为Markdown！",successMessagePlural:"{{count}}个文件已成功转换为Markdown！",aiSuccessTip:"您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。",download:"下载.md",downloadAll:"下载全部(.zip)"},pricing:{title:"价格",subtitle:"满足所有需求的价格计划",description:"选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。",free:"免费版",pro:"专业版",enterprise:"企业版",mostPopular:"最受欢迎",monthly:"/月",yearly:"/年",getStarted:"开始使用",subscribe:"订阅"},auth:{loginTitle:"登录",loginDescription:"登录您的账户",emailLabel:"邮箱",passwordLabel:"密码",loginButton:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账户？",signUpLink:"注册",registerTitle:"注册",registerDescription:"创建一个新账户",confirmPasswordLabel:"确认密码",registerButton:"注册",hasAccount:"已有账户？",signInLink:"登录",orContinueWith:"或继续使用",github:"GitHub",google:"Google",magicLinkSent:"魔法链接已发送！",checkYourEmail:"请检查您的邮箱以获取魔法链接进行登录。"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["common","auth"],n=(0,o.Q_)();return n.use(a.r9).init({lng:e,ns:t,fallbackLng:"en",interpolation:{escapeValue:!1},resources:{en:{...s},zh:{...r}}}),n}},9519:(e,t,n)=>{Promise.resolve().then(n.bind(n,1851))}},e=>{var t=t=>e(e.s=t);e.O(0,[218,897,985,874,441,684,358],()=>t(9519)),_N_E=e.O()}]);