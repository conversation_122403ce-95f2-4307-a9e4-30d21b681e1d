# API 文档

本文档详细概述了用于与文档转换服务交互的 API 端点。

## 认证

大多数端点需要通过 Bearer 令牌进行认证。通过注册和登录获取令牌。

**令牌请求头:**

`Authorization: Bearer <YOUR_ACCESS_TOKEN>`

## 1. 用户管理 (`/api/v1/users`)

### 1.1. 注册用户

*   **端点:** `POST /api/v1/users/register`
*   **描述:** 在系统中注册一个新用户。认证过程由 Supabase 处理，成功后会在本地数据库创建用户记录。
*   **请求体:** `schemas.UserCreate`
    ```json
    {
        "email": "<EMAIL>",
        "username": "newuser",
        "password": "securepassword123",
        "full_name": "新用户"
    }
    ```
*   **响应:** `schemas.User` (成功注册后的用户详细信息)
    ```json
    {
        "id": "user_uuid",
        "email": "<EMAIL>",
        "username": "newuser",
        "full_name": "新用户",
        "is_active": true,
        "is_superuser": false,
        "tier": "free",
        "monthly_conversion_limit": 10, // 示例
        "max_file_size_bytes": 5242880, // 示例 (5MB)
        "conversions_this_month": 0,
        "last_conversion_reset": "2025-05-11T15:28:00Z" // 示例
    }
    ```
*   **状态码:**
    *   `200 OK`: 用户注册成功。
    *   `400 Bad Request`: Supabase 注册错误 (例如，用户已存在，密码太弱等)。详细信息将在响应中提供。
    *   `500 Internal Server Error`: 注册过程中发生意外错误。

### 1.2. 登录

*   **端点:** `POST /api/v1/users/login`
*   **描述:** 对用户进行身份验证并返回访问令牌。认证过程由 Supabase 处理。
*   **请求体:** `OAuth2PasswordRequestForm` (表单数据)
    *   `username`: **用户的电子邮件地址**。
    *   `password`: 用户的密码。
*   **响应:** `schemas.Token`
    ```json
    {
        "access_token": "your_jwt_access_token",
        "token_type": "bearer"
    }
    ```
*   **状态码:**
    *   `200 OK`: 登录成功。
    *   `401 Unauthorized`: Supabase 登录错误 (例如，电子邮件或密码不正确)。详细信息将在响应中提供。
    *   `400 Bad Request`: 非活动用户帐户 (在本地数据库中标记为非活动)。
    *   `500 Internal Server Error`: 登录过程中发生意外错误。

### 1.2.1. Supabase 认证回调

*   **端点:** `POST /api/v1/users/auth/supabase/callback`
*   **描述:** 处理前端在 Supabase 成功认证（登录或注册）后发送的回调。此端点接收 Supabase 提供的 access token，验证它，然后在本地数据库中查找或创建相应的用户记录，并最终返回一个用于访问本 API 其他受保护端点的本地 JWT 访问令牌。
*   **认证:** 此端点本身不需要 Bearer 令牌认证，因为它依赖于请求体中提供的 Supabase access token 进行验证。
*   **请求体:** `schemas.SupabaseToken` (JSON)
    ```json
    {
        "access_token": "supabase_user_access_token_from_frontend"
    }
    ```
*   **响应:** `schemas.Token` (与 `/login` 端点相同，包含本地 JWT)
    ```json
    {
        "access_token": "your_local_jwt_access_token",
        "token_type": "bearer"
    }
    ```
*   **状态码:**
    *   `200 OK`: Supabase token 验证成功，本地用户已同步，本地 JWT 已返回。
    *   `401 Unauthorized`: 提供的 Supabase access token 无效、已过期或无法验证。
    *   `400 Bad Request`: 本地用户帐户被禁用 (inactive)，或者无法从 Supabase token 获取必要的电子邮件信息。
    *   `500 Internal Server Error`: 处理回调过程中发生意外的服务器内部错误。
### 1.3. 获取当前用户信息

*   **端点:** `GET /api/v1/users/me`
*   **描述:** 检索有关当前已认证用户的信息。
*   **认证:** 必需。
*   **响应:** `schemas.User` (已认证用户的详细信息)
*   **状态码:**
    *   `200 OK`: 用户详细信息已检索。
    *   `401 Unauthorized`: 需要认证。

### 1.4. 获取当前用户限制

*   **端点:** `GET /api/v1/users/me/limits`
*   **描述:** 检索当前已认证用户的等级和使用限制。
*   **认证:** 必需。
*   **响应:** `schemas.UserWithLimits`
    ```json
    {
        "id": "user_uuid",
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "测试用户",
        "is_active": true,
        "is_superuser": false,
        "tier": "free",
        "monthly_conversion_limit": 10,
        "max_file_size_bytes": 5242880,
        "conversions_this_month": 2,
        "last_conversion_reset": "2025-05-01T00:00:00Z",
        "feature_flags": {
            "ocr_enabled": false,
            "vlm_enabled": false,
            "auto_summarize": false,
            "grammar_correction": false,
            "batch_processing_enabled": false
        }
    }
    ```
*   **状态码:**
    *   `200 OK`: 用户限制已检索。
    *   `401 Unauthorized`: 需要认证。

### 1.5. 更新当前用户信息

*   **端点:** `PUT /api/v1/users/me`
*   **描述:** 更新当前已认证用户的信息。
*   **认证:** 必需。
*   **请求体:** `schemas.UserUpdate` (要更新的字段)
    ```json
    {
        "full_name": "更新后的用户名",
        "email": "<EMAIL>" // 可选
    }
    ```
*   **响应:** `schemas.User` (更新后的用户详细信息)
*   **状态码:**
    *   `200 OK`: 用户更新成功。
    *   `401 Unauthorized`: 需要认证。

### 1.6. 更新用户等级 (仅限管理员)

*   **端点:** `PUT /api/v1/users/users/{user_id}/tier`
*   **描述:** 更新特定用户的订阅等级。此端点仅限于超级用户。
*   **认证:** 必需 (超级用户)。
*   **路径参数:**
    *   `user_id` (string, UUID): 要更新的用户的 ID。
*   **查询参数:**
    *   `tier` (string, enum: `free`, `basic_paid`, `advanced_paid`): 用户的新等级。
*   **响应:** `schemas.User` (更新后的用户详细信息)
*   **状态码:**
    *   `200 OK`: 用户等级更新成功。
    *   `401 Unauthorized`: 需要认证或不是超级用户。
    *   `404 Not Found`: 未找到用户。

## 2. 任务管理 (`/api/v1/tasks`)

### 2.1. 提交转换任务

*   **端点:** `POST /api/v1/tasks/convert`
*   **描述:** 提交新的文档转换任务。客户端可以直接上传文件，也可以提供已在**服务器配置的源 S3 存储桶**中的文件的 S3 对象键 (`source_s3_key`)。
*   **认证:** **可选**。
*   如果提供有效的 `Authorization: Bearer <token>`，将识别用户并应用其等级限制（文件大小、月度转换次数）和功能权限。任务将与该用户关联。
*   如果未提供令牌或令牌无效，则按匿名/免费用户处理。
*   **请求体:** `multipart/form-data`
*   `file` (可选, `UploadFile`): 要转换的文件。
*   `source_s3_key` (可选, string): 源文件的 S3 对象键 (在服务器配置的源存储桶中)。**如果提供了此项，则忽略 `file`。**
*   `output_filename` (可选, string):期望的输出文件名 (不含扩展名)。
*   `webhook_url` (可选, string, HttpUrl): 任务完成/失败时调用的 URL。
*   `enable_ocr` (可选, boolean): 覆盖用户的默认 OCR 设置。
    *   `enable_vlm` (可选, boolean): 覆盖用户的默认 VLM 设置。
    *   `enable_summarize` (可选, boolean): 覆盖用户的默认摘要设置。
    *   `enable_grammar_correction` (可选, boolean): 覆盖用户的默认语法校正设置。
    *   `image_mode_preference` (可选, string): 图片导出模式：'embedded' 或 'referenced'。默认为 'embedded'。
    *   `processing_mode` (可选, string): 特殊处理模式。例如，设置为 `"zip_images_to_markdown"` 以启用ZIP图片包转Markdown功能。
    *   `enable_image_recognition` (可选, boolean): 当 `processing_mode` 为 `"zip_images_to_markdown"` 时，是否启用图片内容识别（例如，转为Markdown）。默认为 `true`。
    *   `enable_image_description` (可选, boolean): 当 `processing_mode` 为 `"zip_images_to_markdown"` 时，是否为图片生成文本描述。默认为 `false`。
    *   `image_description_style` (可选, string): 当启用图片描述时，描述的风格（例如，`"concise"` 或 `"detailed"`）。默认为 `"concise"`。
*   **响应:** `schemas.TaskCreateResponse`
    ```json
    {
        "task_id": "task_uuid",
        "status": "QUEUED",
        "message": "转换任务已成功创建并加入队列。",
        "user_tier": "free", // 或用户的实际等级
        "features_enabled": {
            "ocr_enabled": false,
            "vlm_enabled": false,
            "auto_summarize": false,
            "grammar_correction": false
        }
    }
    ```
*   **状态码:**
    *   `202 Accepted`: 任务提交成功。
    *   `400 Bad Request`: 输入无效 (例如，不支持的文件类型，缺少 `file` 或 `source_s3_key`)。
    *   `403 Forbidden`: 用户已达到转换限制或请求的功能不适用于其等级。
    *   `413 Request Entity Too Large`: 文件大小超出用户等级的限制。
    *   `500 Internal Server Error`: 无法保存上传的文件、创建任务记录或服务器未配置源 S3 存储桶。

### 2.2. 获取任务状态

*   **端点:** `GET /api/v1/tasks/{task_id}/status`
*   **描述:** 查询特定转换任务的当前状态和进度。
*   **认证:** **不需要**。
*   **路径参数:**
    *   `task_id` (string, UUID): 任务的 ID。
*   **响应:** `schemas.TaskStatusResponse`
    ```json
    {
        "task_id": "task_uuid",
        "status": "PROCESSING", // 例如：QUEUED, PROCESSING, COMPLETED, FAILED
        "progress": 50, // 百分比 (0-100)
        "message": "转换进行中。",
        "created_at": "2025-05-11T15:30:00Z",
        "updated_at": "2025-05-11T15:31:00Z",
        "result_url": "/api/v1/tasks/task_uuid/result", // 仅当状态为 COMPLETED 时
        "error_message": null, // 如果状态为 FAILED，则为错误详细信息
        "original_filename": "document.docx",
        "output_filename_base": "converted_document",
        "user_tier": "free",
        "ocr_enabled": false,
        "vlm_enabled": false,
        "auto_summarize": false,
        "grammar_correction": false
    }
    ```
*   **状态码:**
    *   `200 OK`: 状态检索成功。
    *   `404 Not Found`: 未找到任务。

### 2.3. 获取任务结果

*   **端点:** `GET /api/v1/tasks/{task_id}/result`
*   **描述:** 检索成功完成的转换任务的结果。这通常会重定向到输出文件 (Markdown 或 ZIP) 的预签名 S3 URL。
*   **认证:** **不需要**。
*   **路径参数:**
    *   `task_id` (string, UUID): 任务的 ID。
*   **响应:**
    *   `302 Found`: 重定向到结果文件的预签名 S3 URL。
*   **状态码:**
    *   `302 Found`: 重定向到结果文件。
    *   `404 Not Found`: 未找到任务。
    *   `409 Conflict`: 任务尚未完成。
    *   `500 Internal Server Error`: 结果不可用或 S3 配置错误。
    *   `503 Service Unavailable`: 无法检索结果文件 (例如，预签名 URL 生成失败)。

## 3. 批量任务管理 (`/api/v1/batch`)

批量处理通常适用于高级付费用户。

### 3.1. 从 URL 创建批量任务

*   **端点:** `POST /api/v1/batch/`
*   **描述:** 从 S3 文件 URL 列表创建新的批量转换任务。
*   **认证:** 必需 (具有批量处理权限的用户)。
*   **请求体:** `schemas.BatchTaskCreateRequest`
    ```json
    {
        "file_urls": [
            "uploads/user_id/file1.docx", // 源存储桶中的相对 S3 密钥
            "uploads/user_id/image.png"
        ],
        "output_filename_base": "batch_conversion_results", // 输出 ZIP 的基本名称
        "webhook_url": "https://your-service.com/webhook/batch", // 可选
        "enable_ocr": false, // 可选
        "enable_vlm": false, // 可选
        "enable_summarize": false, // 可选
        "enable_grammar_correction": false // 可选
    }
    ```
*   **响应:** `schemas.BatchTaskCreateResponse`
    ```json
    {
        "batch_task_id": "batch_uuid",
        "status": "QUEUED",
        "message": "批量转换任务已创建并排队等待处理。",
        "user_tier": "advanced_paid",
        "features_enabled": {
            "ocr_enabled": false,
            "vlm_enabled": false,
            "auto_summarize": false,
            "grammar_correction": false
        },
        "total_files": 2
    }
    ```
*   **状态码:**
    *   `200 OK`: 批量任务创建成功。
    *   `400 Bad Request`: 输入无效 (例如，无文件 URL，文件过多)。
    *   `403 Forbidden`: 用户无权进行批量处理或已达到每月限制。

### 3.2. 从 ZIP 上传创建批量任务

*   **端点:** `POST /api/v1/batch/upload`
*   **描述:** 通过上传包含多个文档的 ZIP 文件来创建新的批量转换任务。
*   **认证:** 必需 (具有批量处理权限的用户)。
*   **请求体:** `multipart/form-data`
    *   `zip_file` (`UploadFile`): 包含要转换的文档的 ZIP 文件。
    *   `output_filename_base` (可选, string): 输出 ZIP 的基本名称。
    *   `webhook_url` (可选, string, HttpUrl): 用于完成/失败通知的 URL。
    *   `enable_ocr` (可选, boolean): 覆盖 OCR 设置。
    *   `enable_vlm` (可选, boolean): 覆盖 VLM 设置。
    *   `enable_summarize` (可选, boolean): 覆盖摘要设置。
    *   `enable_grammar_correction` (可选, boolean): 覆盖语法校正设置。
*   **响应:** `schemas.BatchTaskCreateResponse` (类似于从 URL 创建)
*   **状态码:**
    *   `200 OK`: 批量任务创建成功。
    *   `400 Bad Request`: 输入无效 (例如，不是 ZIP 文件，ZIP 中没有支持的文件，文件过多)。
    *   `403 Forbidden`: 用户无权进行批量处理或已达到每月限制。

### 3.3. 获取批量任务状态

*   **端点:** `GET /api/v1/batch/{batch_id}`
*   **描述:** 检索特定批量转换任务的状态，包括各个子任务的状态。
*   **认证:** 必需。
*   **路径参数:**
    *   `batch_id` (string, UUID): 批量任务的 ID。
*   **响应:** `schemas.BatchTaskStatusResponse`
    ```json
    {
        "batch_task_id": "batch_uuid",
        "status": "COMPLETED", // 例如：QUEUED, PROCESSING, COMPLETED, PARTIALLY_COMPLETED, FAILED
        "progress": 100,
        "message": "批量处理完成",
        "created_at": "2025-05-11T16:00:00Z",
        "updated_at": "2025-05-11T16:05:00Z",
        "total_files": 2,
        "completed_files": 2,
        "failed_files": 0,
        "result_url": "/api/v1/batch/batch_uuid/result", // 下载结果 ZIP 的 URL
        "individual_results": [
            {
                "task_id": "sub_task_uuid_1",
                "original_filename": "file1.docx",
                "status": "COMPLETED",
                "result_url": "/api/v1/tasks/sub_task_uuid_1/result",
                "result_content_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            },
            {
                "task_id": "sub_task_uuid_2",
                "original_filename": "image.png",
                "status": "COMPLETED",
                "result_url": "/api/v1/tasks/sub_task_uuid_2/result",
                "result_content_type": "image/png"
            }
        ],
        "user_tier": "advanced_paid",
        "ocr_enabled": false,
        "vlm_enabled": false,
        "auto_summarize": false,
        "grammar_correction": false
    }
    ```
*   **状态码:**
    *   `200 OK`: 批量状态已检索。
    *   `403 Forbidden`: 用户无权访问此批量任务。
    *   `404 Not Found`: 未找到批量任务。

### 3.4. 获取批量任务结果

*   **端点:** `GET /api/v1/batch/{batch_id}/result`
*   **描述:** 检索已完成批量任务的结果。这通常返回一个 JSON 对象，其中包含一个预签名的 S3 URL，用于下载包含所有已转换文档的 ZIP 文件。
*   **认证:** 必需。
*   **路径参数:**
    *   `batch_id` (string, UUID): 批量任务的 ID。
*   **响应:**
    ```json
    {
        "download_url": "s3_presigned_url_for_batch_result.zip"
    }
    ```
*   **状态码:**
    *   `200 OK`: 下载 URL 已提供。
    *   `403 Forbidden`: 用户无权访问此批量任务。
    *   `404 Not Found`: 未找到批量任务或没有结果文件。
    *   `500 Internal Server Error`: 生成下载 URL 失败。

## Schema 定义 (说明性)

实际的 schema 定义可以在 `app/schemas/` 中找到。

### `schemas.UserCreate`
```python
class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None
```

### `schemas.User`
```python
class User(UserBase): # 继承自 UserBase
    id: uuid.UUID
    is_active: bool = True
    is_superuser: bool = False
    tier: UserTierEnum = UserTierEnum.FREE
    # 特定等级的限制和使用情况
    monthly_conversion_limit: int
    max_file_size_bytes: int
    conversions_this_month: int
    last_conversion_reset: Optional[datetime]
```

### `schemas.TaskCreateResponse`
```python
class TaskCreateResponse(BaseModel):
    task_id: str # UUID 字符串
    status: TaskStatus
    message: str
    user_tier: str
    features_enabled: Dict[str, bool]
```

### `schemas.BatchTaskCreateRequest`
```python
class BatchTaskCreateRequest(BaseModel):
    file_urls: List[str] # S3 密钥列表
    output_filename_base: Optional[str] = None
    webhook_url: Optional[HttpUrl] = None
    enable_ocr: Optional[bool] = None
    # ... 其他功能标志
```

本文档应有助于前端开发人员与 API 集成。