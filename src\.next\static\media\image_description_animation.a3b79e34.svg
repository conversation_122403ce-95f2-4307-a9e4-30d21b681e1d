<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Image Only -->
  <g id="imageOnly" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="8" fill="#424242" text-anchor="middle">Image</text>

    <!-- Simple image representation: sun and mountain -->
    <circle cx="65" cy="45" r="10" fill="#BDBDBD"/>
    <polygon points="25,95 45,60 55,75 70,50 80,95" fill="#BDBDBD"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="1.5s" fill="freeze" />
  </g>

  <!-- Image with Description -->
  <g id="imageWithDescription" opacity="0" transform="translate(100, 0)">
    <!-- Overall label for the combined view -->
    <text x="50" y="18" font-size="7" fill="#424242" text-anchor="middle">Image + Description</text>

    <!-- Image part (left side) -->
    <g id="imagePart">
      <rect x="10" y="30" width="40" height="70" fill="#E0E0E0" rx="2" ry="2"/>
      <!-- Mini sun and mountain -->
      <circle cx="30" cy="50" r="5" fill="#BDBDBD"/> <!-- Adjusted: cx="10+20", cy="30+20" -->
      <polygon points="15,85 25,65 30,73 38,60 45,85" fill="#BDBDBD"/> <!-- Scaled and positioned within x=10-50, y=30-100 -->
    </g>

    <!-- Description icon part (right side) -->
    <g id="descriptionPart">
      <rect x="55" y="30" width="35" height="70" fill="#F5F5F5" rx="2" ry="2"/>
      <line x1="60" y1="45" x2="85" y2="45" stroke="#757575" stroke-width="2"/>
      <line x1="60" y1="55" x2="85" y2="55" stroke="#757575" stroke-width="2"/>
      <line x1="60" y1="65" x2="80" y2="65" stroke="#757575" stroke-width="2"/>
      <line x1="60" y1="75" x2="85" y2="75" stroke="#757575" stroke-width="2"/>
    </g>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="1.5s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="1.5s" dur="1.5s" fill="freeze" />
  </g>
</svg>
