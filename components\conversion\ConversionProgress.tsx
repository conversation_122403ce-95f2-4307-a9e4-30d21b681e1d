import React from 'react';

interface ConversionProgressProps {
  isConverting: boolean;
  progress: number;
  totalFiles: number;
  completedFiles: number;
}

const ConversionProgress: React.FC<ConversionProgressProps> = ({
  isConverting,
  progress,
  totalFiles,
  completedFiles,
}) => {
  if (!isConverting) {
    return null;
  }

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium leading-6 text-gray-900">
          Converting Files ({completedFiles}/{totalFiles})
        </h3>
        <div className="mt-4">
          <div className="relative pt-1">
            <div className="flex mb-2 items-center justify-between">
              <div>
                <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200">
                  Progress
                </span>
              </div>
              <div className="text-right">
                <span className="text-xs font-semibold inline-block text-blue-600">
                  {progress}%
                </span>
              </div>
            </div>
            <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200">
              <div
                style={{ width: `${progress}%` }}
                className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 transition-all duration-500 ease-in-out"
              ></div>
            </div>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            Please wait while your files are being converted to Markdown. This may take a few moments depending on the file size and complexity.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ConversionProgress;
