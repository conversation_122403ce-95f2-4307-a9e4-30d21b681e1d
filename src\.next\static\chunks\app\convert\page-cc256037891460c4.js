(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[718],{3973:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(5155),l=s(2115),r=s(1368),n=s(1218);let i=e=>{let{onFilesSelected:t,acceptedFileTypes:s=[".pdf",".doc",".docx",".xls",".xlsx",".html",".htm",".txt",".md",".markdown",".jpg",".jpeg",".png",".gif",".bmp",".rtf",".odt",".ods"],maxFileSize:r=0x1400000,multiple:n=!0,disabled:i=!1}=e,[o,c]=(0,l.useState)(!1),[d,m]=(0,l.useState)(null),u=(0,l.useRef)(null),g=(0,l.useCallback)(e=>{let t=[],a=[];for(let n of e){var l;if(n.size>r){a.push("".concat(n.name," exceeds the maximum file size of ").concat(r/1048576,"MB"));continue}let e=".".concat(null==(l=n.name.split(".").pop())?void 0:l.toLowerCase());if(!s.includes(e)&&s.length>0){a.push("".concat(n.name," is not a supported file type"));continue}t.push(n)}return a.length>0?m(a.join(". ")):m(null),t},[s,r]),h=(0,l.useCallback)(e=>{let s=e.target.files;if(!s)return;let a=g(Array.from(s));a.length>0&&t(a)},[t,g]),x=(0,l.useCallback)(e=>{e.preventDefault(),c(!0)},[]),b=(0,l.useCallback)(e=>{e.preventDefault(),c(!1)},[]),p=(0,l.useCallback)(e=>{e.preventDefault(),c(!1);let s=e.dataTransfer.files;if(!s)return;let a=g(Array.from(s));a.length>0&&t(a)},[t,g]),f=(0,l.useCallback)(()=>{var e;i||null==(e=u.current)||e.click()},[i]);return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(i?"bg-gray-100 border-gray-300 cursor-not-allowed":o?"border-blue-500 bg-blue-50 cursor-copy":"border-gray-300 hover:border-gray-400 cursor-pointer"),onDragOver:i?void 0:x,onDragLeave:i?void 0:b,onDrop:i?void 0:p,onClick:f,children:[(0,a.jsx)("input",{type:"file",ref:u,className:"hidden",onChange:h,accept:s.join(","),multiple:n,disabled:i}),(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48","aria-hidden":"true",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Drag and drop files here, or click to select files"}),(0,a.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Supported formats: ",s.join(", ")," (Max: ",r/1048576,"MB)"]})]}),d&&(0,a.jsx)("div",{className:"mt-2 text-sm text-red-600",children:d})]})},o=e=>{let{files:t,onRemoveFile:s,onRemoveAllFiles:l,onDownloadFile:r,onRetryFile:n,onPreviewFile:i}=e;if(0===t.length)return null;let o=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},c=e=>{switch(e){case"pending":return"Waiting";case"queued":return"Queued";case"uploading":return"Uploading";case"processing":return"Converting";case"completed":return"Completed";case"failed":return"Failed";default:return e}},d=e=>{switch(e){case"pending":default:return"bg-gray-200";case"queued":return"bg-indigo-200";case"uploading":return"bg-blue-200";case"processing":return"bg-yellow-200";case"completed":return"bg-green-200";case"failed":return"bg-red-200"}};return(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Files (",t.length,")"]}),(0,a.jsx)("button",{type:"button",onClick:l,className:"text-sm text-red-600 hover:text-red-800",children:"Remove All"})]}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:t.map(e=>(0,a.jsx)("li",{className:"py-4",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center min-w-0 flex-1",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-10 w-10 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,a.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.file.name}),(0,a.jsx)("p",{className:"ml-2 flex-shrink-0 text-sm text-gray-500",children:o(e.file.size)})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,a.jsx)("div",{className:"h-2.5 rounded-full ".concat(d(e.status)),style:{width:"".concat(e.progress,"%")}})}),(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-600",children:[e.progress,"%"]})]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[c(e.status),e.error&&": ".concat(e.error)]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:["completed"===e.status&&r&&(0,a.jsx)("button",{type:"button",onClick:()=>r(e.id),className:"text-xs text-blue-600 hover:text-blue-800",children:"Download"}),"completed"===e.status&&i&&(0,a.jsx)("button",{type:"button",onClick:()=>i(e.id),className:"text-xs text-blue-600 hover:text-blue-800",children:"Preview"}),"failed"===e.status&&n&&(0,a.jsx)("button",{type:"button",onClick:()=>n(e.id),className:"text-xs text-blue-600 hover:text-blue-800",children:"Retry"}),(0,a.jsx)("button",{type:"button",onClick:()=>s(e.id),className:"text-xs text-red-600 hover:text-red-800",children:"Remove"})]})]})]})]})]})})},e.id))})]})};var c=s(6766);let d={src:"/_next/static/media/summarize_animation.77fd8bb2.svg",height:120,width:100,blurWidth:0,blurHeight:0},m={src:"/_next/static/media/grammar_correction_animation.744ac9d0.svg",height:120,width:100,blurWidth:0,blurHeight:0},u={src:"/_next/static/media/llm_reformat_animation.6787e478.svg",height:120,width:100,blurWidth:0,blurHeight:0},g={src:"/_next/static/media/image_recognition_animation.25619a66.svg",height:120,width:100,blurWidth:0,blurHeight:0},h={src:"/_next/static/media/image_description_animation.220c0815.svg",height:120,width:100,blurWidth:0,blurHeight:0};var x=s(9731);let b=e=>{let{options:t,onChange:s,disabled:r=!1,isLoggedIn:i=!0}=e,{t:o}=(0,n.Bd)(["common","convert"]),b=!i,[p,f]=(0,l.useState)(null),_={enable_summarize:d,enable_grammar_correction:m,enable_llm_reformat:u,enable_image_recognition:g,enable_image_description:h},v=e=>{let{name:a,checked:l}=e.target;b&&("enable_summarize"===a||"enable_grammar_correction"===a||"enable_llm_reformat"===a||"enable_image_recognition"===a||"enable_image_description"===a)||s({...t,[a]:l})},y=e=>{let{name:a,value:l}=e.target;if(b&&"image_mode_preference"===a&&"referenced"!==l)return void s({...t,[a]:"referenced"});b&&"image_description_style"===a||("image_mode_preference"===a&&("embedded"===l||"referenced"===l)||"image_description_style"===a&&("concise"===l||"detailed"===l)?s({...t,[a]:l}):"image_description_attachment_mode"===a&&("keep_image"===l||"replace_image"===l)&&s({...t,[a]:l}))},j=l.useMemo(()=>{let e={...t,image_description_attachment_mode:t.image_description_attachment_mode||"keep_image"};return b?{...e,enable_summarize:!1,enable_grammar_correction:!1,enable_llm_reformat:!1,image_mode_preference:"referenced",enable_image_recognition:!1,enable_image_description:!1,image_description_style:"concise",enable_charts_to_mermaid:!1}:{...e,processing_mode:void 0}},[t,b]);return(0,a.jsx)("div",{className:"mt-6 bg-white shadow sm:rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(x.EN,{as:"div",defaultOpen:!0,children:e=>{let{open:t}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.EN.Button,{className:"flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75",children:[(0,a.jsx)("span",{children:o("convert:imageSettingsTitle","文档内部图片设置")}),(0,a.jsx)("svg",{className:"".concat(t?"transform rotate-180":""," w-5 h-5 text-gray-500"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),(0,a.jsxs)(x.EN.Panel,{className:"px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:o("convert:imageStorageMethod","图片存储方式:")}),(0,a.jsxs)("div",{className:"mt-2 space-y-4",children:[" ",(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"image_mode_referenced",name:"image_mode_preference",type:"radio",value:"referenced",checked:"referenced"===j.image_mode_preference,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"image_mode_referenced",className:"ml-2 block text-sm font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:imageModeReferenced","引用图片")})]}),(0,a.jsx)("p",{className:"ml-6 text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:imageExportModeDescReferencedFree","图片链接将被保留。"):o("convert:imageExportModeDescReferencedPaid","图片单独存储，Markdown 文件较小，但需要复制文件及图片目录才能完整查看。")})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"image_mode_embedded",name:"image_mode_preference",type:"radio",value:"embedded",checked:"embedded"===j.image_mode_preference,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"image_mode_embedded",className:"ml-2 block text-sm font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:imageModeEmbedded","内嵌图片")})]}),(0,a.jsx)("p",{className:"ml-6 text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:imageExportModeDescEmbeddedFree","此为高级功能。"):o("convert:imageExportModeDescEmbeddedPaid","图片数据将直接嵌入 Markdown 文件，文件较大，但所有内容都在一个文档中，方便分享和离线查看。")})]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_image_recognition",name:"enable_image_recognition",type:"checkbox",checked:j.enable_image_recognition||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm relative",children:[(0,a.jsxs)("label",{htmlFor:"enable_image_recognition",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:[o("convert:enableImageRecognition","识别图片中的文字"),(0,a.jsx)("span",{className:"ml-2 text-blue-500 cursor-pointer",onMouseEnter:()=>f("enable_image_recognition"),onMouseLeave:()=>f(null),children:"(?)"})]}),"enable_image_recognition"===p&&_.enable_image_recognition&&(0,a.jsx)("div",{className:"absolute z-10 -top-2 -right-2 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2",children:(0,a.jsx)(c.default,{src:_.enable_image_recognition,alt:"Image Recognition Animation",width:180,height:120})}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableImageRecognitionDescFree","此为高级功能。"):o("convert:enableImageRecognitionDesc","智能识别图片中的文本、代码和列表，并将其转换为保留结构的 Markdown 格式。")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_image_description",name:"enable_image_description",type:"checkbox",checked:j.enable_image_description||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm relative",children:[(0,a.jsxs)("label",{htmlFor:"enable_image_description",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:[o("convert:enableImageDescription","为图片生成描述性文字"),(0,a.jsx)("span",{className:"ml-2 text-blue-500 cursor-pointer",onMouseEnter:()=>f("enable_image_description"),onMouseLeave:()=>f(null),children:"(?)"})]}),"enable_image_description"===p&&_.enable_image_description&&(0,a.jsx)("div",{className:"absolute z-10 -top-2 -right-2 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2",children:(0,a.jsx)(c.default,{src:_.enable_image_description,alt:"Image Description Animation",width:180,height:120})}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableImageDescriptionDescFree","此为高级功能。"):o("convert:enableImageDescriptionDesc","为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片。")})]})]}),j.enable_image_description&&(0,a.jsxs)("div",{className:"mt-3 pl-8 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700",children:o("convert:imageDescriptionAttachmentMode","描述文字附加方式:")}),(0,a.jsxs)("div",{className:"mt-1 flex space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"attachment_mode_keep_image",name:"image_description_attachment_mode",type:"radio",value:"keep_image",checked:"keep_image"===j.image_description_attachment_mode,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"attachment_mode_keep_image",className:"ml-2 block text-xs ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:attachmentModeKeepImage","保留图片")})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"attachment_mode_replace_image",name:"image_description_attachment_mode",type:"radio",value:"replace_image",checked:"replace_image"===j.image_description_attachment_mode,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"attachment_mode_replace_image",className:"ml-2 block text-xs ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:attachmentModeReplaceImage","替换图片")})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-700",children:o("convert:imageDescriptionStyle","描述丰富度:")}),(0,a.jsxs)("div",{className:"mt-1 flex space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"desc_style_concise",name:"image_description_style",type:"radio",value:"concise",checked:"concise"===j.image_description_style,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"desc_style_concise",className:"ml-2 block text-xs ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:styleConcise","简略")})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"desc_style_detailed",name:"image_description_style",type:"radio",value:"detailed",checked:"detailed"===j.image_description_style,onChange:y,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"}),(0,a.jsx)("label",{htmlFor:"desc_style_detailed",className:"ml-2 block text-xs ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:styleDetailed","丰富")})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_charts_to_mermaid",name:"enable_charts_to_mermaid",type:"checkbox",checked:j.enable_charts_to_mermaid||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm",children:[(0,a.jsx)("label",{htmlFor:"enable_charts_to_mermaid",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:o("convert:enableChartsToMermaid","尝试将图表类图片转换为 Mermaid 代码")}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableChartsToMermaidDescFree","此为高级功能。"):o("convert:enableChartsToMermaidDesc","将图片中的流程图、序列图等转换为可编辑的 Mermaid 文本。")})]})]})]})]})}}),(0,a.jsx)(x.EN,{as:"div",defaultOpen:!0,children:e=>{let{open:t}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.EN.Button,{className:"flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75",children:[(0,a.jsx)("span",{children:o("convert:textSettingsTitle","文本与排版优化")}),(0,a.jsx)("svg",{className:"".concat(t?"transform rotate-180":""," w-5 h-5 text-gray-500"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),(0,a.jsxs)(x.EN.Panel,{className:"px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_llm_reformat",name:"enable_llm_reformat",type:"checkbox",checked:j.enable_llm_reformat||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm relative",children:[(0,a.jsxs)("label",{htmlFor:"enable_llm_reformat",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:[o("convert:enableLLMReformat","智能处理换行与段落"),(0,a.jsx)("span",{className:"ml-2 text-blue-500 cursor-pointer",onMouseEnter:()=>f("enable_llm_reformat"),onMouseLeave:()=>f(null),children:"(?)"})]}),"enable_llm_reformat"===p&&_.enable_llm_reformat&&(0,a.jsx)("div",{className:"absolute z-10 -top-2 -right-2 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2",children:(0,a.jsx)(c.default,{src:_.enable_llm_reformat,alt:"LLM Reformat Animation",width:180,height:120})}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableLLMReformatDescFree","此为高级功能。"):o("convert:enableLLMReformatDesc","利用大语言模型优化文本的换行和分段，提升阅读体验。")})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_grammar_correction",name:"enable_grammar_correction",type:"checkbox",checked:j.enable_grammar_correction||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm relative",children:[(0,a.jsxs)("label",{htmlFor:"enable_grammar_correction",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:[o("convert:enableGrammarCorrection","自动校对语法与错别字"),(0,a.jsx)("span",{className:"ml-2 text-blue-500 cursor-pointer",onMouseEnter:()=>f("enable_grammar_correction"),onMouseLeave:()=>f(null),children:"(?)"})]}),"enable_grammar_correction"===p&&_.enable_grammar_correction&&(0,a.jsx)("div",{className:"absolute z-10 -top-2 -right-2 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2",children:(0,a.jsx)(c.default,{src:_.enable_grammar_correction,alt:"Grammar Correction Animation",width:180,height:120})}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableGrammarCorrectionDescFree","此为高级功能。"):o("convert:enableGrammarCorrectionDesc","自动检测并修正文档中的语法错误和拼写错误。")})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex items-center h-5",children:(0,a.jsx)("input",{id:"enable_summarize",name:"enable_summarize",type:"checkbox",checked:j.enable_summarize||!1,onChange:v,disabled:r||b,className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"})}),(0,a.jsxs)("div",{className:"ml-3 text-sm relative",children:[(0,a.jsxs)("label",{htmlFor:"enable_summarize",className:"font-medium ".concat(b?"text-gray-400":"text-gray-700"),children:[o("convert:enableSummarize","生成文档摘要"),(0,a.jsx)("span",{className:"ml-2 text-blue-500 cursor-pointer",onMouseEnter:()=>f("enable_summarize"),onMouseLeave:()=>f(null),children:"(?)"})]}),"enable_summarize"===p&&_.enable_summarize&&(0,a.jsx)("div",{className:"absolute z-10 -top-2 -right-2 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2",children:(0,a.jsx)(c.default,{src:_.enable_summarize,alt:"Summarize Animation",width:180,height:120})}),(0,a.jsx)("p",{className:"text-xs ".concat(b?"text-gray-400":"text-gray-500"),children:b?o("convert:enableSummarizeDescFree","此为高级功能。"):o("convert:enableSummarizeDesc","自动为转换后的文档生成内容摘要。")})]})]})]})]})}})]})})})},p=e=>{let{isConverting:t,progress:s,totalFiles:l,completedFiles:r}=e;if(!t)return null;let n=s<.05?.05:s;return(0,a.jsx)("div",{className:"mt-6 bg-white shadow sm:rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:["Converting Files (",r,"/",l,")"]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"relative pt-1",children:[(0,a.jsxs)("div",{className:"flex mb-2 items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:"text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200",children:"Progress"})}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("span",{className:"text-xs font-semibold inline-block text-blue-600",children:[Math.round(100*n),"%"]})})]}),(0,a.jsx)("div",{className:"overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200",children:(0,a.jsx)("div",{style:{width:"".concat(100*n,"%")},className:"shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 transition-all duration-500 ease-in-out"})})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Please wait while your files are being converted to Markdown. This may take a few moments depending on the file size and complexity."})]})]})})},f=e=>{let{results:t,onDownloadFile:s,onDownloadAll:l,onRetryFile:r}=e,{t:i}=(0,n.Bd)(["common","convert"]),o=t.filter(e=>"completed"===e.status||"failed"===e.status),c=t.filter(e=>"completed"===e.status).length;return 0===o.length?null:(0,a.jsx)("div",{className:"mt-6 bg-white shadow sm:rounded-lg",children:(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:i("convert:conversionResultsTitle","Conversion Results")}),c>1&&(0,a.jsx)("button",{type:"button",onClick:l,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:i("convert:downloadAll")})]}),(0,a.jsxs)("div",{className:"mt-4",children:[c>0&&(0,a.jsx)("div",{className:"bg-green-50 border-l-4 border-green-400 p-4 mb-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-700",children:1===c?i("convert:successMessage"):i("convert:successMessagePlural",{count:c})})})]})}),(0,a.jsx)("ul",{className:"divide-y divide-gray-200",children:o.map(e=>(0,a.jsxs)("li",{className:"py-4 flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center min-w-0",children:["completed"===e.status?(0,a.jsx)("svg",{className:"h-6 w-6 text-green-500 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}):(0,a.jsx)("svg",{className:"h-6 w-6 text-red-500 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsxs)("div",{className:"ml-2 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 truncate",title:e.fileName,children:e.fileName}),"failed"===e.status&&e.error&&(0,a.jsx)("p",{className:"text-xs text-red-600 truncate",title:e.error,children:e.error})]})]}),(0,a.jsxs)("div",{className:"ml-2 flex-shrink-0 flex space-x-2",children:["completed"===e.status&&(0,a.jsx)("button",{type:"button",onClick:()=>s(e.id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:i("convert:download")}),"failed"===e.status&&r&&(0,a.jsx)("button",{type:"button",onClick:()=>r(e.id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500",children:i("convert:retry","Retry")})]})]},e.id))})]})]})})};var _=s(822);let v=e=>{let{markdownContent:t,fileName:s,onClose:r}=e,[n,i]=(0,l.useState)("");return((0,l.useEffect)(()=>{let e=t.replace(/^# (.*$)/gm,"<h1>$1</h1>").replace(/^## (.*$)/gm,"<h2>$1</h2>").replace(/^### (.*$)/gm,"<h3>$1</h3>").replace(/\*\*(.*)\*\*/gm,"<strong>$1</strong>").replace(/\*(.*)\*/gm,"<em>$1</em>").replace(/\n/gm,"<br>");i(_.A.sanitize(e,{USE_PROFILES:{html:!0},ALLOWED_TAGS:["h1","h2","h3","h4","h5","h6","p","br","hr","ul","ol","li","blockquote","pre","code","em","strong","del","a","img","table","thead","tbody","tr","th","td"],ALLOWED_ATTR:["href","src","alt","title"]}))},[t]),t)?(0,a.jsx)("div",{className:"mt-6 bg-white shadow sm:rounded-lg fixed inset-0 z-50 overflow-auto flex items-center justify-center p-4 bg-black bg-opacity-50",children:(0,a.jsxs)("div",{className:"bg-white shadow-xl sm:rounded-lg w-full max-w-3xl max-h-[90vh] flex flex-col",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:p-6 border-b border-gray-200 flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:["Preview: ",s]}),r&&(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600","aria-label":"Close preview",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsx)("div",{className:"p-4 sm:p-6 overflow-y-auto",children:(0,a.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:n}})})})]})}):null};var y=s(4236);let j="http://localhost:8000";function w(){let{t:e}=(0,n.Bd)(["common","convert"]),{user:t,isLoading:s}=(0,y.A)(),[c,d]=(0,l.useState)([]),[m,u]=(0,l.useState)(!1),[g,h]=(0,l.useState)(0),[x,_]=(0,l.useState)(null),[w,N]=(0,l.useState)({enable_summarize:!1,enable_grammar_correction:!1,enable_llm_reformat:!1,image_mode_preference:"referenced",enable_image_recognition:!1,enable_image_description:!1,image_description_style:"concise",enable_charts_to_mermaid:!1});(0,l.useEffect)(()=>{s||(t?N(e=>({...e,enable_summarize:!0,enable_grammar_correction:!0,enable_llm_reformat:!0,image_mode_preference:"embedded",enable_image_recognition:!0,enable_image_description:!0,enable_charts_to_mermaid:!0})):N({enable_summarize:!1,enable_grammar_correction:!1,enable_llm_reformat:!1,image_mode_preference:"referenced",enable_image_recognition:!1,enable_image_description:!1,image_description_style:"concise",enable_charts_to_mermaid:!1}))},[t,s]);let k=(0,l.useCallback)((e,t)=>{d(s=>s.map(s=>s.id===e?{...s,...t}:s))},[]),C=(0,l.useCallback)(async(t,s)=>{console.log("[".concat(new Date().toISOString(),"] Polling status for task ").concat(s,", file ").concat(t,". Current isConverting: ").concat(m));let a="".concat(j,"/api/v1/tasks/").concat(s,"/status");try{let l=await fetch(a);if(!l.ok){let e=await l.json().catch(()=>({detail:"Failed to get task status"}));k(t,{status:"failed",error:e.detail||"Error ".concat(l.status),progress:1});return}let r=await l.json(),n=r.status.toLowerCase();if(k(t,{status:n,progress:r.progress,error:"failed"===n?r.error_message:void 0}),"completed"===n){k(t,{resultUrl:r.result_url}),console.log("DEBUG: data.result_url from /status endpoint:",r.result_url);let s="".concat(j).concat(r.result_url);console.log("DEBUG: Constructed contentUrl for fetch:",s);try{let e=await fetch(s);if(e.ok){let s=await e.text();k(t,{markdownContent:s})}else console.warn("Failed to fetch content from ".concat(s,": ").concat(e.status))}catch(a){console.error("Error fetching content from ".concat(s,":"),a),k(t,{error:e("convert:errorFetchingResult")})}}else"failed"===n||("queued"===n||"processing"===n?(console.log("[".concat(new Date().toISOString(),"] Task ").concat(s," is ").concat(n,". Scheduling next poll. Current isConverting: ").concat(m)),setTimeout(()=>C(t,s),3e3)):console.log("[".concat(new Date().toISOString(),"] Task ").concat(s," has unhandled status: ").concat(n,". Stopping poll. Current isConverting: ").concat(m)))}catch(s){console.error("Error polling task status:",s),k(t,{status:"failed",error:e("convert:errorPollingStatus"),progress:1})}},[k,e,m]),A=(0,l.useCallback)(async()=>{let s=c.filter(e=>"pending"===e.status||"failed"===e.status);if(0!==s.length&&!m)for(let a of(u(!0),h(0),d(e=>e.map(e=>"failed"===e.status&&s.some(t=>t.id===e.id)?{...e,progress:0,error:void 0,status:"pending"}:e)),s)){let s;k(a.id,{status:"uploading",progress:.05});let l=new FormData;l.append("file",a.file);let r=(0,y.B)(),n={};t&&r&&(n.Authorization="Bearer ".concat(r)),void 0!==(s=t?{...w}:{enable_summarize:!1,enable_grammar_correction:!1,enable_llm_reformat:!1,image_mode_preference:"referenced",enable_image_recognition:!1,enable_image_description:!1,image_description_style:"concise",enable_charts_to_mermaid:!1}).enable_summarize&&l.append("enable_summarize",String(s.enable_summarize)),void 0!==s.enable_grammar_correction&&l.append("enable_grammar_correction",String(s.enable_grammar_correction)),void 0!==s.enable_llm_reformat&&l.append("enable_llm_reformat",String(s.enable_llm_reformat)),s.image_mode_preference&&l.append("image_mode_preference",s.image_mode_preference),void 0!==s.enable_image_recognition&&l.append("enable_image_recognition",String(s.enable_image_recognition)),void 0!==s.enable_image_description&&l.append("enable_image_description",String(s.enable_image_description)),s.image_description_style&&s.enable_image_description&&l.append("image_description_style",s.image_description_style);try{let e="".concat(j,"/api/v1/tasks/convert");console.log("Attempting to call convert API at:",e);let t=await fetch(e,{method:"POST",headers:n,body:l});if(202===t.status){let e=await t.json();k(a.id,{taskId:e.task_id,status:e.status.toLowerCase()||"queued",progress:.1}),C(a.id,e.task_id)}else{let e=await t.json().catch(()=>({detail:"Conversion request failed"}));k(a.id,{status:"failed",error:e.detail||"Error ".concat(t.status),progress:0})}}catch(t){console.error("Error starting conversion for file:",a.file.name,t),k(a.id,{status:"failed",error:e("convert:errorStartingConversion"),progress:0})}}},[c,m,k,C,e]);(0,l.useEffect)(()=>{if(!c.some(e=>"uploading"===e.status||"processing"===e.status||"queued"===e.status)&&m){let e=c.filter(e=>e.taskId);e.every(e=>"completed"===e.status||"failed"===e.status)&&e.length>0?u(!1):0===e.length&&c.length>0&&u(!1)}if(c.length>0){let e=c.reduce((e,t)=>e+t.progress,0);h(c.length>0?e/c.length:0)}else h(0)},[c,m]);let S=(0,l.useCallback)(e=>{let t=e.map(e=>({file:e,id:(0,r.A)(),progress:0,status:"pending"}));d(e=>[...e,...t])},[]),M=(0,l.useCallback)(e=>{d(t=>t.filter(t=>t.id!==e)),x===e&&_(null)},[x]),z=(0,l.useCallback)(()=>{d([]),_(null)},[]),L=(0,l.useCallback)(async t=>{let s=c.find(e=>e.id===t);if(s)if(s.markdownContent&&"completed"===s.status){let e=new Blob([s.markdownContent],{type:"text/markdown;charset=utf-8"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="".concat(s.file.name.split(".")[0]||"converted",".md"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(t)}else if(s.resultUrl&&"completed"===s.status){let e=s.resultUrl.startsWith("http")?s.resultUrl:"".concat(j).concat(s.resultUrl);window.open(e,"_blank")}else alert(e("convert:noResultToDownload"))},[c,e]),D=(0,l.useCallback)(async()=>{let t=c.filter(e=>"completed"===e.status);if(0===t.length)return void alert(e("convert:noCompletedFilesToDownload"));for(let e of t)await L(e.id)},[c,L,e]),E=(0,l.useCallback)(t=>{let s=c.find(e=>e.id===t);s&&s.markdownContent&&"completed"===s.status?_(t):s&&"completed"===s.status&&!s.markdownContent?alert(e("convert:previewNotAvailableNonMarkdown")):s&&"completed"!==s.status&&alert(e("convert:previewNotReady"))},[c,e]),I=(0,l.useCallback)(e=>{let t=c.find(t=>t.id===e);t&&"failed"===t.status&&k(e,{status:"pending",progress:0,error:void 0,taskId:void 0,resultUrl:void 0,markdownContent:void 0})},[c,k]),F=c.filter(e=>"completed"===e.status).length,R=x?c.find(e=>e.id===x):null;return(0,a.jsx)("div",{className:"py-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:e("convert:title")}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:e("convert:description")}),(0,a.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(0,a.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"PDF"}),(0,a.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"Word (DOC/DOCX)"}),(0,a.jsx)("span",{className:"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10",children:"HTML"})]}),(0,a.jsx)("div",{className:"mt-4 bg-blue-50 border-l-4 border-blue-400 p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-blue-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:e("convert:aiTip")})})]})}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)(i,{onFilesSelected:S,disabled:m}),(0,a.jsx)(o,{files:c,onRemoveFile:M,onRemoveAllFiles:z,onDownloadFile:L,onRetryFile:I,onPreviewFile:E,isConverting:m}),(0,a.jsx)(b,{options:w,onChange:N,disabled:m||!t||s,isLoggedIn:!!t}),c.length>0&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{type:"button",onClick:A,disabled:m||c.every(e=>"completed"===e.status||"processing"===e.status||"uploading"===e.status||"queued"===e.status),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed",children:m?e("convert:converting"):e("convert:startConversion")})}),(0,a.jsx)(p,{isConverting:m,progress:g,totalFiles:c.length,completedFiles:F}),c.some(e=>"completed"===e.status||"failed"===e.status)&&(0,a.jsx)(f,{results:c.map(e=>({id:e.id,fileName:e.file.name,status:e.status,resultUrl:e.resultUrl,error:e.error})),onDownloadFile:L,onDownloadAll:D,onRetryFile:I}),R&&R.markdownContent&&"completed"===R.status&&(0,a.jsx)(v,{markdownContent:R.markdownContent,fileName:R.file.name,onClose:()=>_(null)})]})]})})}},4236:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,B:()=>o});var a=s(2115),l=s(4983);let r="local_jwt_token";async function n(e){try{console.log("[Auth Debug] syncWithBackend started with token:",e.substring(0,10)+"...");let t="".concat("http://localhost:8000","/api/v1/users/auth/supabase/callback");console.log("[Auth Debug] Calling backend API at:",t);let s=Date.now(),a=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({access_token:e})}),l=Date.now()-s;if(console.log("[Auth Debug] Backend API call completed in ".concat(l,"ms with status:"),a.status),a.ok){let e=await a.json();if(e.access_token)return localStorage.setItem(r,e.access_token),console.log("Local JWT stored successfully."),e.access_token;return console.error("Backend callback response missing access_token:",e),localStorage.removeItem(r),null}{let e;try{e=await a.json()}catch(t){e={message:a.statusText}}return console.error("Error syncing with backend:",a.status,e),localStorage.removeItem(r),null}}catch(e){return console.error("Network error or other issue syncing with backend:",e),localStorage.removeItem(r),null}}function i(){let[e,t]=(0,a.useState)({user:null,session:null,isLoading:!0});return(0,a.useEffect)(()=>{console.log("[Auth Debug] useAuth hook initialized");let e=(0,l.A)();console.log("[Auth Debug] Supabase client obtained:",!!e),(async()=>{console.log("useAuth: initializeAuth started");try{let{data:{session:s},error:a}=await e.auth.getSession();if(a){console.error("useAuth: initializeAuth - error fetching session:",a),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth failed due to session error.");return}if(console.log("useAuth: initializeAuth - initialSession fetched:",s?"exists":"null"),s&&s.access_token){console.log("useAuth: initializeAuth - session exists");let e=s.user;console.log("useAuth: initializeAuth - user from session:",e?e.id:"null"),e?(console.log("useAuth: initializeAuth - user exists, syncing with backend..."),await n(s.access_token),console.log("useAuth: initializeAuth - backend sync complete."),t({user:e,session:s,isLoading:!1}),console.log("useAuth: initializeAuth completed with session and user.")):(console.warn("useAuth: initializeAuth - session exists but user is null in session."),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth completed with session but no user."))}else localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth completed without session.")}catch(e){console.error("Error initializing auth:",e),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth failed in catch block.")}})();let{data:{subscription:s}}=e.auth.onAuthStateChange(async(e,s)=>{console.log("useAuth: onAuthStateChange event:",e,"Session:",s?"exists":"null");try{if(s&&s.access_token){let e=s.user;if(console.log("useAuth: onAuthStateChange - user from session:",e?e.id:"null"),e){console.log("useAuth: onAuthStateChange - User object exists. Attempting to sync with backend...");try{await n(s.access_token),console.log("useAuth: onAuthStateChange - backend sync complete.")}catch(e){console.error("useAuth: onAuthStateChange - EXCEPTION syncing with backend:",e)}t({user:e,session:s,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state with session.")}else console.warn("useAuth: onAuthStateChange - User is null in session."),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state to no user.")}else localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state without session (SIGNED_OUT or null session).")}catch(e){console.error("useAuth: Error in onAuthStateChange callback:",e),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange failed in catch block, set isLoading to false.")}});return()=>{s.unsubscribe()}},[]),e}function o(){return localStorage.getItem(r)}},4983:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(9724);let l=null,r=()=>{if(l)return l;let e="https://rpzceoedurujspnnyvkm.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJwemNlb2VkdXJ1anNwbm55dmttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1OTkwNDgsImV4cCI6MjA2MjE3NTA0OH0._zuv48OJiram1Q_QXndbl6exkL3P8qrgI_MfybQzCEo";if(!e)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");if(!t)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");return l=(0,a.UU)(e,t,{auth:{debug:!0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{fetch:async(e,t)=>{console.log("[Supabase Debug] Fetch request to: ".concat(e)),console.log("[Supabase Debug] Fetch options:",t);try{let s=await fetch(e,t);return console.log("[Supabase Debug] Response status: ".concat(s.status)),s}catch(e){throw console.error("[Supabase Debug] Fetch error:",e),e}}}}),console.log("[Supabase Debug] Client initialized with URL:",e),l}},6631:(e,t,s)=>{Promise.resolve().then(s.bind(s,3973))}},e=>{var t=t=>e(e.s=t);e.O(0,[218,897,766,553,441,684,358],()=>t(6631)),_N_E=e.O()}]);