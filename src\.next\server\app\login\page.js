(()=>{var e={};e.id=520,e.ids=[520],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4839:(e,t,s)=>{Promise.resolve().then(s.bind(s,6387))},5103:(e,t,s)=>{Promise.resolve().then(s.bind(s,6501))},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6387:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\github\\\\alltomarkdown_frontend\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\login\\page.tsx","default")},6501:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(687),a=s(3210),i=s(5814),n=s.n(i),l=s(7163),o=s(6457),d=s(8262);function u(){let{t:e,i18n:t}=(0,o.Bd)(["auth","common"]),s=(0,d.A)(t.language,["auth","common"]),[i,u]=(0,a.useState)(""),[c,m]=(0,a.useState)(""),[x,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)(null),f=async e=>{e.preventDefault(),p(!0),g(null);let t=(0,l.A)();try{let{error:e}=await t.auth.signInWithPassword({email:i,password:c});if(e)throw e;window.location.href="/convert"}catch(e){console.error("Login attempt failed:",e),e instanceof Error?g(e.message):g(s.t("auth:loginError"))}finally{p(!1)}};return(0,r.jsxs)("div",{className:"flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("h2",{className:"mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900",children:e("auth:loginTitle")})}),(0,r.jsxs)("div",{className:"mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]",children:[(0,r.jsxs)("div",{className:"bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12",children:[(0,r.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[h&&(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:h})})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:emailLabel")}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:e=>u(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:passwordLabel")}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:c,onChange:e=>m(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"}),(0,r.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm leading-6 text-gray-900",children:e("auth:rememberMe",{ns:"auth",defaultValue:"Remember me"})})]}),(0,r.jsx)("div",{className:"text-sm leading-6",children:(0,r.jsx)("a",{href:"#",className:"font-semibold text-blue-600 hover:text-blue-500",children:e("auth:forgotPassword")})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:x,className:"flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:x?e("auth:loggingIn",{ns:"auth",defaultValue:"Signing in..."}):e("auth:loginButton")})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"relative mt-10",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center","aria-hidden":"true",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm font-medium leading-6",children:(0,r.jsx)("span",{className:"bg-white px-6 text-gray-900",children:e("auth:orContinueWith")})})]}),(0,r.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[(0,r.jsxs)("a",{href:"#",className:"flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent",children:[(0,r.jsxs)("svg",{className:"h-5 w-5","aria-hidden":"true",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M12.0003 4.75C13.7703 4.75 15.3553 5.36002 16.6053 6.54998L20.0303 3.125C17.9502 1.19 15.2353 0 12.0003 0C7.31028 0 3.25527 2.69 1.28027 6.60998L5.27028 9.70498C6.21525 6.86002 8.87028 4.75 12.0003 4.75Z",fill:"#EA4335"}),(0,r.jsx)("path",{d:"M23.49 12.275C23.49 11.49 23.415 10.73 23.3 10H12V14.51H18.47C18.18 15.99 17.34 17.25 16.08 18.1L19.945 21.1C22.2 19.01 23.49 15.92 23.49 12.275Z",fill:"#4285F4"}),(0,r.jsx)("path",{d:"M5.26498 14.2949C5.02498 13.5699 4.88501 12.7999 4.88501 11.9999C4.88501 11.1999 5.01998 10.4299 5.26498 9.7049L1.275 6.60986C0.46 8.22986 0 10.0599 0 11.9999C0 13.9399 0.46 15.7699 1.28 17.3899L5.26498 14.2949Z",fill:"#FBBC05"}),(0,r.jsx)("path",{d:"M12.0004 24.0001C15.2404 24.0001 17.9654 22.935 19.9454 21.095L16.0804 18.095C15.0054 18.82 13.6204 19.245 12.0004 19.245C8.8704 19.245 6.21537 17.135 5.2654 14.29L1.27539 17.385C3.25539 21.31 7.3104 24.0001 12.0004 24.0001Z",fill:"#34A853"})]}),(0,r.jsx)("span",{className:"text-sm font-semibold leading-6",children:e("auth:google")})]}),(0,r.jsxs)("a",{href:"#",className:"flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent",children:[(0,r.jsx)("svg",{className:"h-5 w-5 fill-[#24292F]","aria-hidden":"true",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"text-sm font-semibold leading-6",children:e("auth:github")})]})]})]})]}),(0,r.jsxs)("p",{className:"mt-10 text-center text-sm text-gray-500",children:[e("auth:noAccount")," ",(0,r.jsx)(n(),{href:"/register",className:"font-semibold leading-6 text-blue-600 hover:text-blue-500",children:e("auth:signUpLink")})]})]})]})}},7237:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6387)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\login\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7910:e=>{"use strict";e.exports=require("stream")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,373,658,522],()=>s(7237));module.exports=r})();