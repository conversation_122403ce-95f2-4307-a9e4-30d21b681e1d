'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from 'react-i18next';
import FileUploader from '../../components/conversion/FileUploader';
import FileListManager, { FileStatus } from '../../components/conversion/FileListManager';
import ConversionOptions, { ConversionOptionsType } from '../../components/conversion/ConversionOptions';
import ConversionProgress from '../../components/conversion/ConversionProgress';
import ResultDisplay from '../../components/conversion/ResultDisplay';
import MarkdownPreviewer from '../../components/conversion/MarkdownPreviewer';
import { useAuth, getLocalAuthToken } from '../../hooks/useAuth'; // Import useAuth

interface FileItem {
  file: File;
  id: string;
  progress: number;
  status: FileStatus;
  error?: string;
  resultUrl?: string; // This might be the S3 URL or a link to our /result endpoint
  markdownContent?: string;
  taskId?: string; // To store the backend task ID
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export default function ConvertPage() {
  const { t } = useTranslation(['common', 'convert']);
  const { user, isLoading: authLoading } = useAuth(); // Get user and auth loading state
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [selectedPreviewId, setSelectedPreviewId] = useState<string | null>(null);
  const [conversionOptions, setConversionOptions] = useState<ConversionOptionsType>({
    enable_summarize: false,
    enable_grammar_correction: false,
    enable_llm_reformat: false,
    image_mode_preference: 'referenced', // Default for free tier, 'embedded' for logged in
    enable_image_recognition: false,
    enable_image_description: false,
    image_description_style: 'concise',
    enable_charts_to_mermaid: false, // New option
  });

  // Update conversion options based on user login status
  useEffect(() => {
    if (!authLoading) {
      if (user) {
        // Logged-in user, set potentially premium defaults
        setConversionOptions(prev => ({
          ...prev,
          enable_summarize: true,
          enable_grammar_correction: true,
          enable_llm_reformat: true,
          image_mode_preference: 'embedded',
          enable_image_recognition: true, // Assuming these are premium
          enable_image_description: true,
          enable_charts_to_mermaid: true, // Assuming this is premium
        }));
      } else {
        // Non-logged-in user, enforce free tier options
        setConversionOptions({
          enable_summarize: false,
          enable_grammar_correction: false,
          enable_llm_reformat: false,
          image_mode_preference: 'referenced',
          enable_image_recognition: false,
          enable_image_description: false,
          image_description_style: 'concise',
          enable_charts_to_mermaid: false,
        });
      }
    }
  }, [user, authLoading]);


  const updateFileState = useCallback((id: string, updates: Partial<FileItem>) => {
    setFiles(prevFiles =>
      prevFiles.map(f => (f.id === id ? { ...f, ...updates } : f))
    );
  }, []);


  const pollTaskStatus = useCallback(async (fileItemId: string, taskId: string) => {
    console.log(`[${new Date().toISOString()}] Polling status for task ${taskId}, file ${fileItemId}. Current isConverting: ${isConverting}`);
    const statusUrl = `${API_BASE_URL}/api/v1/tasks/${taskId}/status`;
    try {
      const response = await fetch(statusUrl);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Failed to get task status' }));
        updateFileState(fileItemId, { status: 'failed', error: errorData.detail || `Error ${response.status}`, progress: 1 });
        return;
      }

      const data = await response.json();
      const currentStatus = data.status.toLowerCase(); // Convert to lowercase for consistent comparison

      updateFileState(fileItemId, {
        status: currentStatus as FileStatus,
        progress: data.progress,
        error: currentStatus === 'failed' ? data.error_message : undefined, // Compare with lowercase
      });

      if (currentStatus === 'completed') { // Compare with lowercase
        updateFileState(fileItemId, { resultUrl: data.result_url }); // Store the relative result URL
        console.log("DEBUG: data.result_url from /status endpoint:", data.result_url); // Log the raw result_url
        // Now fetch the actual content from the result_url
        const contentUrl = `${API_BASE_URL}${data.result_url}`; // Construct full URL
        console.log("DEBUG: Constructed contentUrl for fetch:", contentUrl); // Log the constructed URL
        try {
          const contentResponse = await fetch(contentUrl);
          if (contentResponse.ok) {
            // Assuming the result is markdown text for preview
            const markdown = await contentResponse.text();
            updateFileState(fileItemId, { markdownContent: markdown });
          } else {
            console.warn(`Failed to fetch content from ${contentUrl}: ${contentResponse.status}`);
            // If it's not text, the user will use the download button which will hit the resultUrl directly
          }
        } catch (contentError) {
          console.error(`Error fetching content from ${contentUrl}:`, contentError);
          updateFileState(fileItemId, { error: t('convert:errorFetchingResult') });
        }
      } else if (currentStatus === 'failed') { // Compare with lowercase
        // Error already set from above (or will be by updateFileState)
      } else if (currentStatus === 'queued' || currentStatus === 'processing') { // Compare with lowercase
        console.log(`[${new Date().toISOString()}] Task ${taskId} is ${currentStatus}. Scheduling next poll. Current isConverting: ${isConverting}`);
        setTimeout(() => pollTaskStatus(fileItemId, taskId), 3000); // Poll again after 3 seconds
      } else {
        console.log(`[${new Date().toISOString()}] Task ${taskId} has unhandled status: ${currentStatus}. Stopping poll. Current isConverting: ${isConverting}`);
      }
    } catch (error) {
      console.error('Error polling task status:', error);
      updateFileState(fileItemId, { status: 'failed', error: t('convert:errorPollingStatus'), progress: 1 });
    }
  }, [updateFileState, t, isConverting]);


  const handleStartConversion = useCallback(async () => {
    const pendingFiles = files.filter(file => file.status === 'pending' || file.status === 'failed');
    if (pendingFiles.length === 0 || isConverting) return;

    setIsConverting(true);
    setOverallProgress(0);

    // Reset progress for failed files being retried
    setFiles(prevFiles =>
        prevFiles.map(f =>
            (f.status === 'failed' && pendingFiles.some(pf => pf.id === f.id))
                ? { ...f, progress: 0, error: undefined, status: 'pending' }
                : f
        )
    );


    for (const fileItem of pendingFiles) {
      updateFileState(fileItem.id, { status: 'uploading', progress: 0.05 });

      const formData = new FormData();
      formData.append('file', fileItem.file);
      const token = getLocalAuthToken();
      const headers: HeadersInit = {};
      if (user && token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Get the effective options based on login status for sending to backend
      let effectiveSendOptions: ConversionOptionsType;
      if (!user) {
        effectiveSendOptions = { // Free tier options for backend
          enable_summarize: false,
          enable_grammar_correction: false,
          enable_llm_reformat: false,
          image_mode_preference: 'referenced',
          enable_image_recognition: false,
          enable_image_description: false,
          image_description_style: 'concise',
          enable_charts_to_mermaid: false, // Free tier default
        };
      } else {
        effectiveSendOptions = { ...conversionOptions }; // Logged-in user's selected options
      }

      // Append conversion options to formData
      if (effectiveSendOptions.enable_summarize !== undefined) {
        formData.append('enable_summarize', String(effectiveSendOptions.enable_summarize));
      }
      if (effectiveSendOptions.enable_grammar_correction !== undefined) {
        formData.append('enable_grammar_correction', String(effectiveSendOptions.enable_grammar_correction));
      }
      if (effectiveSendOptions.enable_llm_reformat !== undefined) {
        formData.append('enable_llm_reformat', String(effectiveSendOptions.enable_llm_reformat));
      }
      if (effectiveSendOptions.image_mode_preference) {
        formData.append('image_mode_preference', effectiveSendOptions.image_mode_preference);
      }
      // processing_mode is removed
      if (effectiveSendOptions.enable_image_recognition !== undefined) {
        formData.append('enable_image_recognition', String(effectiveSendOptions.enable_image_recognition));
      }
      if (effectiveSendOptions.enable_image_description !== undefined) {
        formData.append('enable_image_description', String(effectiveSendOptions.enable_image_description));
      }
      if (effectiveSendOptions.image_description_style && effectiveSendOptions.enable_image_description) { // Only send style if description is enabled
        formData.append('image_description_style', effectiveSendOptions.image_description_style);
      }

      try {
        const convertUrl = `${API_BASE_URL}/api/v1/tasks/convert`;
        console.log('Attempting to call convert API at:', convertUrl); //诊断日志
        const response = await fetch(convertUrl, {
          method: 'POST',
          headers,
          body: formData,
        });

        if (response.status === 202) { // Accepted
          const data = await response.json();
          updateFileState(fileItem.id, {
            taskId: data.task_id,
            status: data.status.toLowerCase() as FileStatus || 'queued', // Use status from response
            progress: 0.1, // Initial progress after acceptance
          });
          pollTaskStatus(fileItem.id, data.task_id);
        } else {
          const errorData = await response.json().catch(() => ({ detail: 'Conversion request failed' }));
          updateFileState(fileItem.id, { status: 'failed', error: errorData.detail || `Error ${response.status}`, progress: 0 });
        }
      } catch (error) {
        console.error('Error starting conversion for file:', fileItem.file.name, error);
        updateFileState(fileItem.id, { status: 'failed', error: t('convert:errorStartingConversion'), progress: 0 });
      }
    }
    // setIsConverting will be set to false once all pollTaskStatus calls complete or fail.
    // For now, we can set it based on whether any tasks are still in progress.
    // This part needs refinement based on how pollTaskStatus updates global state.
  }, [files, isConverting, updateFileState, pollTaskStatus, t]);


  useEffect(() => {
    const anyFileProcessing = files.some(f => f.status === 'uploading' || f.status === 'processing' || f.status === 'queued');
    if (!anyFileProcessing && isConverting) {
        // Check if all files that were attempted have a final state (completed or failed)
        const attemptedFiles = files.filter(f => f.taskId); // Files for which conversion was started
        const allAttemptedFilesDone = attemptedFiles.every(f => f.status === 'completed' || f.status === 'failed');
        if (allAttemptedFilesDone && attemptedFiles.length > 0) {
            setIsConverting(false);
        } else if (attemptedFiles.length === 0 && files.length > 0) {
            // No tasks were even started, but conversion was triggered
            setIsConverting(false);
        }
    }

    // Calculate overall progress
    if (files.length > 0) {
        const totalProgress = files.reduce((sum, file) => sum + file.progress, 0);
        setOverallProgress(files.length > 0 ? totalProgress / files.length : 0);
    } else {
        setOverallProgress(0);
    }
  }, [files, isConverting]);


  const handleFilesSelected = useCallback((selectedFiles: File[]) => {
    const newFileItems = selectedFiles.map(file => ({
      file,
      id: uuidv4(),
      progress: 0,
      status: 'pending' as FileStatus,
    }));
    setFiles(prevFiles => [...prevFiles, ...newFileItems]);
  }, []);

  const handleRemoveFile = useCallback((id: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.id !== id));
    if (selectedPreviewId === id) {
      setSelectedPreviewId(null);
    }
  }, [selectedPreviewId]);

  const handleRemoveAllFiles = useCallback(() => {
    setFiles([]);
    setSelectedPreviewId(null);
  }, []);

  const handleDownloadFile = useCallback(async (id: string) => {
    const fileItem = files.find(file => file.id === id);
    if (!fileItem) return;

    if (fileItem.markdownContent && fileItem.status === 'completed') {
      const blob = new Blob([fileItem.markdownContent], { type: 'text/markdown;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${fileItem.file.name.split('.')[0] || 'converted'}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } else if (fileItem.resultUrl && fileItem.status === 'completed') {
      // This means the content wasn't fetched as markdown, or it's not markdown.
      // The resultUrl from the backend should lead to a direct download or a page that initiates download.
      // For pre-signed S3 URLs, this would directly start the download.
      // If it's our own /api/v1/tasks/{task_id}/result, that endpoint must handle Content-Disposition.
      const downloadUrl = fileItem.resultUrl.startsWith('http') ? fileItem.resultUrl : `${API_BASE_URL}${fileItem.resultUrl}`;
      window.open(downloadUrl, '_blank');
    } else {
      alert(t('convert:noResultToDownload'));
    }
  }, [files, t]);

  const handleDownloadAll = useCallback(async () => {
    // This would ideally call a backend endpoint that zips all completed files for this user/session.
    // For now, let's just try to download all completed files individually.
    const completed = files.filter(f => f.status === 'completed');
    if (completed.length === 0) {
      alert(t('convert:noCompletedFilesToDownload'));
      return;
    }
    for (const fileItem of completed) {
      await handleDownloadFile(fileItem.id); // Add await if downloads need to be sequential for some reason
    }
  }, [files, handleDownloadFile, t]);

  const handlePreviewFile = useCallback((id: string) => {
    const fileItem = files.find(f => f.id === id);
    if (fileItem && fileItem.markdownContent && fileItem.status === 'completed') {
      setSelectedPreviewId(id);
    } else if (fileItem && fileItem.status === 'completed' && !fileItem.markdownContent) {
        alert(t('convert:previewNotAvailableNonMarkdown'));
    } else if (fileItem && fileItem.status !== 'completed') {
        alert(t('convert:previewNotReady'));
    }
  }, [files, t]);

  const handleRetryFile = useCallback((id: string) => {
    const fileToRetry = files.find(f => f.id === id);
    if (fileToRetry && fileToRetry.status === 'failed') {
        updateFileState(id, { status: 'pending', progress: 0, error: undefined, taskId: undefined, resultUrl: undefined, markdownContent: undefined });
        // Optionally, trigger handleStartConversion if not already converting
        // For simplicity, user can click "Start Conversion" again.
    }
  }, [files, updateFileState]);


  const completedFilesCount = files.filter(file => file.status === 'completed').length;
  const selectedPreviewFile = selectedPreviewId
    ? files.find(file => file.id === selectedPreviewId)
    : null;

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">{t('convert:title')}</h1>
        <p className="mt-2 text-sm text-gray-500">
          {t('convert:description')}
        </p>
        {/* Supported formats display can be dynamic based on backend capabilities if needed */}
        <div className="mt-2 flex flex-wrap gap-2">
          <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">PDF</span>
          <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Word (DOC/DOCX)</span>
          <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">HTML</span>
          {/* Add more as supported */}
        </div>

        <div className="mt-4 bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                {t('convert:aiTip')}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <FileUploader onFilesSelected={handleFilesSelected} disabled={isConverting} />

          <FileListManager
            files={files}
            onRemoveFile={handleRemoveFile}
            onRemoveAllFiles={handleRemoveAllFiles}
            onDownloadFile={handleDownloadFile}
            onRetryFile={handleRetryFile}
            onPreviewFile={handlePreviewFile}
            isConverting={isConverting}
          />

          <ConversionOptions
            options={conversionOptions}
            onChange={setConversionOptions}
            disabled={isConverting || !user || authLoading} // Disable if not logged in, or auth is loading
            isLoggedIn={!!user} // Pass login status to potentially disable specific options internally
          />

          {files.length > 0 && (
            <div className="mt-6">
              <button
                type="button"
                onClick={handleStartConversion}
                disabled={isConverting || files.every(f => f.status === 'completed' || f.status === 'processing' || f.status === 'uploading' || f.status === 'queued')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isConverting ? t('convert:converting') : t('convert:startConversion')}
              </button>
            </div>
          )}

          <ConversionProgress
            isConverting={isConverting}
            progress={overallProgress}
            totalFiles={files.length}
            completedFiles={completedFilesCount}
          />

          {/* ResultDisplay might need adjustment if we are not showing individual results before all are done */}
          {files.some(f => f.status === 'completed' || f.status === 'failed') && (
            <ResultDisplay
              results={files.map(file => ({
                id: file.id,
                fileName: file.file.name,
                status: file.status,
                resultUrl: file.resultUrl, // This is the API result URL, not necessarily direct download
                error: file.error,
              }))}
              onDownloadFile={handleDownloadFile}
              onDownloadAll={handleDownloadAll}
              onRetryFile={handleRetryFile}
            />
          )}

          {selectedPreviewFile && selectedPreviewFile.markdownContent && selectedPreviewFile.status === 'completed' && (
            <MarkdownPreviewer
              markdownContent={selectedPreviewFile.markdownContent}
              fileName={selectedPreviewFile.file.name}
              onClose={() => setSelectedPreviewId(null)}
            />
          )}
        </div>
      </div>
    </div>
  );
}
