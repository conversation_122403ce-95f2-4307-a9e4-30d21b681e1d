import React from 'react';

export type FileStatus = 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';

interface FileItem {
  file: File;
  id: string;
  progress: number;
  status: FileStatus;
  error?: string;
  resultUrl?: string;
}

interface FileListManagerProps {
  files: FileItem[];
  onRemoveFile: (id: string) => void;
  onRemoveAllFiles: () => void;
  onDownloadFile?: (id: string) => void;
  onRetryFile?: (id: string) => void;
}

const FileListManager: React.FC<FileListManagerProps> = ({
  files,
  onRemoveFile,
  onRemoveAllFiles,
  onDownloadFile,
  onRetryFile,
}) => {
  if (files.length === 0) {
    return null;
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusText = (status: FileStatus): string => {
    switch (status) {
      case 'pending': return 'Waiting';
      case 'uploading': return 'Uploading';
      case 'processing': return 'Converting';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      default: return status;
    }
  };

  const getStatusColor = (status: FileStatus): string => {
    switch (status) {
      case 'pending': return 'bg-gray-200';
      case 'uploading': return 'bg-blue-200';
      case 'processing': return 'bg-yellow-200';
      case 'completed': return 'bg-green-200';
      case 'failed': return 'bg-red-200';
      default: return 'bg-gray-200';
    }
  };

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Files ({files.length})</h3>
        <button
          type="button"
          onClick={onRemoveAllFiles}
          className="text-sm text-red-600 hover:text-red-800"
        >
          Remove All
        </button>
      </div>
      <ul className="divide-y divide-gray-200">
        {files.map((fileItem) => (
          <li key={fileItem.id} className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center min-w-0 flex-1">
                <div className="flex-shrink-0">
                  <svg
                    className="h-10 w-10 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div className="ml-4 min-w-0 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {fileItem.file.name}
                    </p>
                    <p className="ml-2 flex-shrink-0 text-sm text-gray-500">
                      {formatFileSize(fileItem.file.size)}
                    </p>
                  </div>
                  <div className="mt-2">
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${getStatusColor(fileItem.status)}`}
                          style={{ width: `${fileItem.progress}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">
                        {fileItem.progress}%
                      </span>
                    </div>
                    <div className="mt-1 flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {getStatusText(fileItem.status)}
                        {fileItem.error && `: ${fileItem.error}`}
                      </span>
                      <div className="flex space-x-2">
                        {fileItem.status === 'completed' && onDownloadFile && (
                          <button
                            type="button"
                            onClick={() => onDownloadFile(fileItem.id)}
                            className="text-xs text-blue-600 hover:text-blue-800"
                          >
                            Download
                          </button>
                        )}
                        {fileItem.status === 'failed' && onRetryFile && (
                          <button
                            type="button"
                            onClick={() => onRetryFile(fileItem.id)}
                            className="text-xs text-blue-600 hover:text-blue-800"
                          >
                            Retry
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => onRemoveFile(fileItem.id)}
                          className="text-xs text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FileListManager;
