{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/FileUploader.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\n\ninterface FileUploaderProps {\n  onFilesSelected: (files: File[]) => void;\n  acceptedFileTypes?: string[];\n  maxFileSize?: number; // in bytes\n  multiple?: boolean;\n  disabled?: boolean;\n}\n\nconst FileUploader: React.FC<FileUploaderProps> = ({\n  onFilesSelected,\n  acceptedFileTypes = [\n    '.pdf', '.doc', '.docx',\n    '.xls', '.xlsx',\n    '.html', '.htm',\n    '.txt', '.md', '.markdown',\n    '.jpg', '.jpeg', '.png', '.gif', '.bmp',\n    '.rtf', '.odt', '.ods'\n  ],\n  maxFileSize = 20 * 1024 * 1024, // 20MB default\n  multiple = true,\n  disabled = false,\n}) => {\n  const [isDragging, setIsDragging] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateFiles = useCallback((files: File[]): File[] => {\n    const validFiles: File[] = [];\n    const errors: string[] = [];\n\n    for (const file of files) {\n      // Check file size\n      if (file.size > maxFileSize) {\n        errors.push(`${file.name} exceeds the maximum file size of ${maxFileSize / (1024 * 1024)}MB`);\n        continue;\n      }\n\n      // Check file type\n      const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;\n      if (!acceptedFileTypes.includes(fileExtension) && acceptedFileTypes.length > 0) {\n        errors.push(`${file.name} is not a supported file type`);\n        continue;\n      }\n\n      validFiles.push(file);\n    }\n\n    if (errors.length > 0) {\n      setError(errors.join('. '));\n    } else {\n      setError(null);\n    }\n\n    return validFiles;\n  }, [acceptedFileTypes, maxFileSize]);\n\n  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {\n    const fileList = event.target.files;\n    if (!fileList) return;\n\n    const filesArray = Array.from(fileList);\n    const validFiles = validateFiles(filesArray);\n\n    if (validFiles.length > 0) {\n      onFilesSelected(validFiles);\n    }\n  }, [onFilesSelected, validateFiles]);\n\n  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault();\n    setIsDragging(false);\n\n    const fileList = event.dataTransfer.files;\n    if (!fileList) return;\n\n    const filesArray = Array.from(fileList);\n    const validFiles = validateFiles(filesArray);\n\n    if (validFiles.length > 0) {\n      onFilesSelected(validFiles);\n    }\n  }, [onFilesSelected, validateFiles]);\n\n  const handleButtonClick = useCallback(() => {\n    if (disabled) return;\n    fileInputRef.current?.click();\n  }, [disabled]);\n\n  return (\n    <div className=\"w-full\">\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n          disabled\n            ? 'bg-gray-100 border-gray-300 cursor-not-allowed'\n            : isDragging\n            ? 'border-blue-500 bg-blue-50 cursor-copy'\n            : 'border-gray-300 hover:border-gray-400 cursor-pointer'\n        }`}\n        onDragOver={disabled ? undefined : handleDragOver}\n        onDragLeave={disabled ? undefined : handleDragLeave}\n        onDrop={disabled ? undefined : handleDrop}\n        onClick={handleButtonClick} // Already checks disabled\n      >\n        <input\n          type=\"file\"\n          ref={fileInputRef}\n          className=\"hidden\"\n          onChange={handleFileChange}\n          accept={acceptedFileTypes.join(',')}\n          multiple={multiple}\n          disabled={disabled}\n        />\n        <svg\n          className=\"mx-auto h-12 w-12 text-gray-400\"\n          stroke=\"currentColor\"\n          fill=\"none\"\n          viewBox=\"0 0 48 48\"\n          aria-hidden=\"true\"\n        >\n          <path\n            d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\"\n            strokeWidth={2}\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          />\n        </svg>\n        <p className=\"mt-2 text-sm text-gray-600\">\n          Drag and drop files here, or click to select files\n        </p>\n        <p className=\"mt-1 text-xs text-gray-500\">\n          Supported formats: {acceptedFileTypes.join(', ')} (Max: {maxFileSize / (1024 * 1024)}MB)\n        </p>\n      </div>\n      {error && (\n        <div className=\"mt-2 text-sm text-red-600\">\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUploader;\n"], "names": [], "mappings": ";;;;AAAA;;;;AAUA,MAAM,eAA4C,CAAC,EACjD,eAAe,EACf,oBAAoB;IAClB;IAAQ;IAAQ;IAChB;IAAQ;IACR;IAAS;IACT;IAAQ;IAAO;IACf;IAAQ;IAAS;IAAQ;IAAQ;IACjC;IAAQ;IAAQ;CACjB,EACD,cAAc,KAAK,OAAO,IAAI,EAC9B,WAAW,IAAI,EACf,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,MAAM,aAAqB,EAAE;YAC7B,MAAM,SAAmB,EAAE;YAE3B,KAAK,MAAM,QAAQ,MAAO;gBACxB,kBAAkB;gBAClB,IAAI,KAAK,IAAI,GAAG,aAAa;oBAC3B,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,kCAAkC,EAAE,cAAc,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;oBAC5F;gBACF;gBAEA,kBAAkB;gBAClB,MAAM,gBAAgB,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,eAAe;gBACrE,IAAI,CAAC,kBAAkB,QAAQ,CAAC,kBAAkB,kBAAkB,MAAM,GAAG,GAAG;oBAC9E,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,6BAA6B,CAAC;oBACvD;gBACF;gBAEA,WAAW,IAAI,CAAC;YAClB;YAEA,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,SAAS,OAAO,IAAI,CAAC;YACvB,OAAO;gBACL,SAAS;YACX;YAEA,OAAO;QACT;kDAAG;QAAC;QAAmB;KAAY;IAEnC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACpC,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK;YACnC,IAAI,CAAC,UAAU;YAEf,MAAM,aAAa,MAAM,IAAI,CAAC;YAC9B,MAAM,aAAa,cAAc;YAEjC,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,gBAAgB;YAClB;QACF;qDAAG;QAAC;QAAiB;KAAc;IAEnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAClC,MAAM,cAAc;YACpB,cAAc;QAChB;mDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACnC,MAAM,cAAc;YACpB,cAAc;QAChB;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC9B,MAAM,cAAc;YACpB,cAAc;YAEd,MAAM,WAAW,MAAM,YAAY,CAAC,KAAK;YACzC,IAAI,CAAC,UAAU;YAEf,MAAM,aAAa,MAAM,IAAI,CAAC;YAC9B,MAAM,aAAa,cAAc;YAEjC,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,gBAAgB;YAClB;QACF;+CAAG;QAAC;QAAiB;KAAc;IAEnC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACpC,IAAI,UAAU;YACd,aAAa,OAAO,EAAE;QACxB;sDAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,WACI,mDACA,aACA,2CACA,wDACJ;gBACF,YAAY,WAAW,YAAY;gBACnC,aAAa,WAAW,YAAY;gBACpC,QAAQ,WAAW,YAAY;gBAC/B,SAAS;;kCAET,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;wBACV,UAAU;wBACV,QAAQ,kBAAkB,IAAI,CAAC;wBAC/B,UAAU;wBACV,UAAU;;;;;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,QAAO;wBACP,MAAK;wBACL,SAAQ;wBACR,eAAY;kCAEZ,cAAA,6LAAC;4BACC,GAAE;4BACF,aAAa;4BACb,eAAc;4BACd,gBAAe;;;;;;;;;;;kCAGnB,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;;4BAA6B;4BACpB,kBAAkB,IAAI,CAAC;4BAAM;4BAAQ,cAAc,CAAC,OAAO,IAAI;4BAAE;;;;;;;;;;;;;YAGxF,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;GA9IM;KAAA;uCAgJS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/FileListManager.tsx"], "sourcesContent": ["import React from 'react';\n\nexport type FileStatus = 'pending' | 'queued' | 'uploading' | 'processing' | 'completed' | 'failed';\n\ninterface FileItem {\n  file: File;\n  id: string;\n  progress: number;\n  status: FileStatus;\n  error?: string;\n  resultUrl?: string;\n}\n\ninterface FileListManagerProps {\n  files: FileItem[];\n  onRemoveFile: (id: string) => void;\n  onRemoveAllFiles: () => void;\n  onDownloadFile?: (id: string) => void;\n  onRetryFile?: (id: string) => void;\n  onPreviewFile?: (id: string) => void;\n  isConverting?: boolean;\n}\n\nconst FileListManager: React.FC<FileListManagerProps> = ({\n  files,\n  onRemoveFile,\n  onRemoveAllFiles,\n  onDownloadFile,\n  onRetryFile,\n  onPreviewFile,\n}) => {\n  if (files.length === 0) {\n    return null;\n  }\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getStatusText = (status: FileStatus): string => {\n    switch (status) {\n      case 'pending': return 'Waiting';\n      case 'queued': return 'Queued';\n      case 'uploading': return 'Uploading';\n      case 'processing': return 'Converting';\n      case 'completed': return 'Completed';\n      case 'failed': return 'Failed';\n      default: return status;\n    }\n  };\n\n  const getStatusColor = (status: FileStatus): string => {\n    switch (status) {\n      case 'pending': return 'bg-gray-200';\n      case 'queued': return 'bg-indigo-200'; // Added color for queued\n      case 'uploading': return 'bg-blue-200';\n      case 'processing': return 'bg-yellow-200';\n      case 'completed': return 'bg-green-200';\n      case 'failed': return 'bg-red-200';\n      default: return 'bg-gray-200';\n    }\n  };\n\n  return (\n    <div className=\"mt-6\">\n      <div className=\"flex justify-between items-center mb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Files ({files.length})</h3>\n        <button\n          type=\"button\"\n          onClick={onRemoveAllFiles}\n          className=\"text-sm text-red-600 hover:text-red-800\"\n        >\n          Remove All\n        </button>\n      </div>\n      <ul className=\"divide-y divide-gray-200\">\n        {files.map((fileItem) => (\n          <li key={fileItem.id} className=\"py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center min-w-0 flex-1\">\n                <div className=\"flex-shrink-0\">\n                  <svg\n                    className=\"h-10 w-10 text-gray-400\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"ml-4 min-w-0 flex-1\">\n                  <div className=\"flex items-center justify-between\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">\n                      {fileItem.file.name}\n                    </p>\n                    <p className=\"ml-2 flex-shrink-0 text-sm text-gray-500\">\n                      {formatFileSize(fileItem.file.size)}\n                    </p>\n                  </div>\n                  <div className=\"mt-2\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\n                        <div\n                          className={`h-2.5 rounded-full ${getStatusColor(fileItem.status)}`}\n                          style={{ width: `${fileItem.progress}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"ml-2 text-sm text-gray-600\">\n                        {fileItem.progress}%\n                      </span>\n                    </div>\n                    <div className=\"mt-1 flex items-center justify-between\">\n                      <span className=\"text-xs text-gray-500\">\n                        {getStatusText(fileItem.status)}\n                        {fileItem.error && `: ${fileItem.error}`}\n                      </span>\n                      <div className=\"flex space-x-2\">\n                        {fileItem.status === 'completed' && onDownloadFile && (\n                          <button\n                            type=\"button\"\n                            onClick={() => onDownloadFile(fileItem.id)}\n                            className=\"text-xs text-blue-600 hover:text-blue-800\"\n                          >\n                            Download\n                          </button>\n                        )}\n                        {fileItem.status === 'completed' && onPreviewFile && (\n                          <button\n                            type=\"button\"\n                            onClick={() => onPreviewFile(fileItem.id)}\n                            className=\"text-xs text-blue-600 hover:text-blue-800\"\n                          >\n                            Preview\n                          </button>\n                        )}\n                        {fileItem.status === 'failed' && onRetryFile && (\n                          <button\n                            type=\"button\"\n                            onClick={() => onRetryFile(fileItem.id)}\n                            className=\"text-xs text-blue-600 hover:text-blue-800\"\n                          >\n                            Retry\n                          </button>\n                        )}\n                        <button\n                          type=\"button\"\n                          onClick={() => onRemoveFile(fileItem.id)}\n                          className=\"text-xs text-red-600 hover:text-red-800\"\n                        >\n                          Remove\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n};\n\nexport default FileListManager;\n"], "names": [], "mappings": ";;;;;AAuBA,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,aAAa,EACd;IACC,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO,iBAAiB,yBAAyB;YAChE,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAoC;4BAAQ,MAAM,MAAM;4BAAC;;;;;;;kCACvE,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAIH,6LAAC;gBAAG,WAAU;0BACX,MAAM,GAAG,CAAC,CAAC,yBACV,6LAAC;wBAAqB,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI,CAAC,IAAI;;;;;;kEAErB,6LAAC;wDAAE,WAAU;kEACV,eAAe,SAAS,IAAI,CAAC,IAAI;;;;;;;;;;;;0DAGtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAW,CAAC,mBAAmB,EAAE,eAAe,SAAS,MAAM,GAAG;oEAClE,OAAO;wEAAE,OAAO,GAAG,SAAS,QAAQ,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAG5C,6LAAC;gEAAK,WAAU;;oEACb,SAAS,QAAQ;oEAAC;;;;;;;;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,cAAc,SAAS,MAAM;oEAC7B,SAAS,KAAK,IAAI,CAAC,EAAE,EAAE,SAAS,KAAK,EAAE;;;;;;;0EAE1C,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,MAAM,KAAK,eAAe,gCAClC,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,eAAe,SAAS,EAAE;wEACzC,WAAU;kFACX;;;;;;oEAIF,SAAS,MAAM,KAAK,eAAe,+BAClC,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,cAAc,SAAS,EAAE;wEACxC,WAAU;kFACX;;;;;;oEAIF,SAAS,MAAM,KAAK,YAAY,6BAC/B,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,YAAY,SAAS,EAAE;wEACtC,WAAU;kFACX;;;;;;kFAIH,6LAAC;wEACC,MAAK;wEACL,SAAS,IAAM,aAAa,SAAS,EAAE;wEACvC,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA5EN,SAAS,EAAE;;;;;;;;;;;;;;;;AA0F9B;KApJM;uCAsJS", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/public/summarize_animation.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 240, height: 120, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/public/grammar_correction_animation.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 240, height: 120, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/public/llm_reformat_animation.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 240, height: 120, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/public/image_recognition_animation.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 240, height: 120, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/public/image_description_animation.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 240, height: 120, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,8HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/ConversionOptions.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Image from 'next/image';\r\n\r\n// Import SVGs. Adjust paths if they are different or if you prefer to use them as components.\r\nimport SummarizeAnimation from '../../public/summarize_animation.svg';\r\nimport GrammarCorrectionAnimation from '../../public/grammar_correction_animation.svg';\r\nimport LlmReformatAnimation from '../../public/llm_reformat_animation.svg';\r\nimport ImageRecognitionAnimation from '../../public/image_recognition_animation.svg';\r\nimport ImageDescriptionAnimation from '../../public/image_description_animation.svg';\r\nimport { Disclosure } from '@headlessui/react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nexport interface ConversionOptionsType {\r\n  enable_summarize?: boolean;\r\n  enable_grammar_correction?: boolean;\r\n  enable_llm_reformat?: boolean; // Corresponds to \"智能处理换行与段落\"\r\n  image_mode_preference?: 'embedded' | 'referenced'; // Corresponds to \"图片存储方式\"\r\n  enable_image_recognition?: boolean; // Corresponds to \"识别图片中的文字 (OCR)\"\r\n  enable_image_description?: boolean; // Corresponds to \"为图片生成描述文本\"\r\n  image_description_style?: 'concise' | 'detailed'; // Corresponds to \"描述丰富度\"\r\n  image_description_attachment_mode?: 'keep_image' | 'replace_image'; // Corresponds to \"描述文字附加方式\"\r\n  enable_charts_to_mermaid?: boolean; // Corresponds to \"尝试将图表类图片转换为 Mermaid 代码\"\r\n  // processing_mode is removed from UI based on new design\r\n}\r\n\r\ninterface ConversionOptionsProps {\r\n  options: ConversionOptionsType;\r\n  onChange: (options: ConversionOptionsType) => void;\r\n  disabled?: boolean;\r\n  isLoggedIn?: boolean;\r\n}\r\n\r\nconst ConversionOptions: React.FC<ConversionOptionsProps> = ({ options, onChange, disabled = false, isLoggedIn = true }) => {\r\n  const { t } = useTranslation(['common', 'convert']);\r\n  const isFreeTier = !isLoggedIn;\r\n  const [hoveredOption, setHoveredOption] = useState<string | null>(null);\r\n\r\n  const SvgAnimations: { [key: string]: string | import('next/image').StaticImageData } = {\r\n    enable_summarize: SummarizeAnimation,\r\n    enable_grammar_correction: GrammarCorrectionAnimation,\r\n    enable_llm_reformat: LlmReformatAnimation,\r\n    enable_image_recognition: ImageRecognitionAnimation,\r\n    enable_image_description: ImageDescriptionAnimation,\r\n  };\r\n\r\n  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, checked } = e.target;\r\n    if (isFreeTier && (name === 'enable_summarize' || name === 'enable_grammar_correction' || name === 'enable_llm_reformat' || name === 'enable_image_recognition' || name === 'enable_image_description')) {\r\n      // Potentially disallow for free tier or handle based on specific feature tiering\r\n      return;\r\n    }\r\n    onChange({\r\n      ...options,\r\n      [name]: checked,\r\n    });\r\n  };\r\n\r\n  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    const optionName = name as keyof ConversionOptionsType;\r\n\r\n    if (isFreeTier && optionName === 'image_mode_preference' && value !== 'referenced') {\r\n        onChange({ ...options, [optionName]: 'referenced' });\r\n        return;\r\n    }\r\n    if (isFreeTier && optionName === 'image_description_style'){\r\n        return;\r\n    }\r\n    // Ensure value matches the type expected by ConversionOptionsType for these specific radio button groups\r\n    if (optionName === 'image_mode_preference' && (value === 'embedded' || value === 'referenced')) {\r\n      onChange({ ...options, [optionName]: value });\r\n    } else if (optionName === 'image_description_style' && (value === 'concise' || value === 'detailed')) {\r\n      onChange({ ...options, [optionName]: value });\r\n    } else if (optionName === 'image_description_attachment_mode' && (value === 'keep_image' || value === 'replace_image')) {\r\n      onChange({ ...options, [optionName]: value });\r\n    }\r\n  };\r\n\r\n  // Determine effective options based on login state and new structure\r\n  const effectiveOptions = React.useMemo(() => {\r\n    // Set a default for image_description_attachment_mode if not already present in options\r\n    const baseOptions = {\r\n      ...options,\r\n      image_description_attachment_mode: options.image_description_attachment_mode || 'keep_image',\r\n    };\r\n\r\n    if (isFreeTier) {\r\n      return {\r\n        ...baseOptions, // Use baseOptions which includes the default\r\n        enable_summarize: false,\r\n        enable_grammar_correction: false,\r\n        enable_llm_reformat: false,\r\n        image_mode_preference: 'referenced' as const,\r\n        enable_image_recognition: false,\r\n        enable_image_description: false,\r\n        image_description_style: 'concise' as const,\r\n        // image_description_attachment_mode is now correctly defaulted in baseOptions for free tier as well\r\n        enable_charts_to_mermaid: false,\r\n        // processing_mode is effectively removed by not being in effectiveOptions for free tier\r\n      };\r\n    }\r\n    // For paid tier, ensure processing_mode is not used and use baseOptions for defaults\r\n    return { ...baseOptions, processing_mode: undefined };\r\n  }, [options, isFreeTier]);\r\n\r\n  return (\r\n    <div className=\"mt-6 bg-white shadow sm:rounded-lg\">\r\n      <div className=\"px-4 py-5 sm:p-6\">\r\n        {/* <h3 className=\"text-lg font-medium leading-6 text-gray-900\">{t('convert:options')}</h3> */}\r\n\r\n        <div className=\"space-y-8\">\r\n          {/* Section 1: 图片处理设置 */}\r\n          <Disclosure as=\"div\" defaultOpen>\r\n            {({ open }) => (\r\n              <>\r\n                <Disclosure.Button className=\"flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75\">\r\n                  <span>{t('convert:imageSettingsTitle', '文档内部图片设置')}</span>\r\n                  <svg className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </Disclosure.Button>\r\n                <Disclosure.Panel className=\"px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700\">\r\n                  {/* 图片存储方式 */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700\">{t('convert:imageStorageMethod', '图片存储方式:')}</label>\r\n                    <div className=\"mt-2 space-y-4\"> {/* Changed to space-y-4 for vertical spacing */}\r\n                      <div>\r\n                        <div className=\"flex items-center\">\r\n                          <input\r\n                            id=\"image_mode_referenced\"\r\n                            name=\"image_mode_preference\"\r\n                            type=\"radio\"\r\n                            value=\"referenced\"\r\n                            checked={effectiveOptions.image_mode_preference === 'referenced'}\r\n                            onChange={handleRadioChange}\r\n                            disabled={disabled || isFreeTier}\r\n                            className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                          />\r\n                          <label htmlFor=\"image_mode_referenced\" className={`ml-2 block text-sm font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                            {t('convert:imageModeReferenced', '引用图片')}\r\n                          </label>\r\n                        </div>\r\n                        <p className={`ml-6 text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                          {isFreeTier\r\n                            ? t('convert:imageExportModeDescReferencedFree', '图片链接将被保留。')\r\n                            : t('convert:imageExportModeDescReferencedPaid', '图片单独存储，Markdown 文件较小，但需要复制文件及图片目录才能完整查看。')}\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div>\r\n                        <div className=\"flex items-center\">\r\n                          <input\r\n                            id=\"image_mode_embedded\"\r\n                            name=\"image_mode_preference\"\r\n                            type=\"radio\"\r\n                            value=\"embedded\"\r\n                            checked={effectiveOptions.image_mode_preference === 'embedded'}\r\n                            onChange={handleRadioChange}\r\n                            disabled={disabled || isFreeTier}\r\n                            className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                          />\r\n                          <label htmlFor=\"image_mode_embedded\" className={`ml-2 block text-sm font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                            {t('convert:imageModeEmbedded', '内嵌图片')}\r\n                          </label>\r\n                        </div>\r\n                        <p className={`ml-6 text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                          {isFreeTier\r\n                            ? t('convert:imageExportModeDescEmbeddedFree', '此为高级功能。')\r\n                            : t('convert:imageExportModeDescEmbeddedPaid', '图片数据将直接嵌入 Markdown 文件，文件较大，但所有内容都在一个文档中，方便分享和离线查看。')}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 识别图片中的文字 (OCR) */}\r\n                  <div className=\"flex items-start\">\r\n                    <div className=\"flex items-center h-5\">\r\n                      <input\r\n                        id=\"enable_image_recognition\"\r\n                        name=\"enable_image_recognition\"\r\n                        type=\"checkbox\"\r\n                        checked={effectiveOptions.enable_image_recognition || false}\r\n                        onChange={handleCheckboxChange}\r\n                        disabled={disabled || isFreeTier}\r\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"ml-3 text-sm relative\">\r\n                      <label htmlFor=\"enable_image_recognition\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                        {t('convert:enableImageRecognition', '识别图片中的文字')}\r\n                        <span\r\n                          className=\"ml-2 text-blue-500 cursor-pointer\"\r\n                          onMouseEnter={() => setHoveredOption('enable_image_recognition')}\r\n                          onMouseLeave={() => setHoveredOption(null)}\r\n                        >\r\n                          (?)\r\n                        </span>\r\n                      </label>\r\n                      {hoveredOption === 'enable_image_recognition' && SvgAnimations['enable_image_recognition'] && (\r\n                        <div className=\"absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none\">\r\n                          <Image src={SvgAnimations['enable_image_recognition']} alt=\"Image Recognition Animation\" width={180} height={120} />\r\n                        </div>\r\n                      )}\r\n                       <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                        {isFreeTier ? t('convert:enableImageRecognitionDescFree', '此为高级功能。') : t('convert:enableImageRecognitionDesc', '智能识别图片中的文本、代码和列表，并将其转换为保留结构的 Markdown 格式。')}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 为图片生成描述文本 */}\r\n                  <div>\r\n                    <div className=\"flex items-start\">\r\n                      <div className=\"flex items-center h-5\">\r\n                        <input\r\n                          id=\"enable_image_description\"\r\n                          name=\"enable_image_description\"\r\n                          type=\"checkbox\"\r\n                          checked={effectiveOptions.enable_image_description || false}\r\n                          onChange={handleCheckboxChange}\r\n                          disabled={disabled || isFreeTier}\r\n                          className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"ml-3 text-sm relative\">\r\n                        <label htmlFor=\"enable_image_description\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                          {t('convert:enableImageDescription', '为图片生成描述性文字')}\r\n                          <span\r\n                            className=\"ml-2 text-blue-500 cursor-pointer\"\r\n                            onMouseEnter={() => setHoveredOption('enable_image_description')}\r\n                            onMouseLeave={() => setHoveredOption(null)}\r\n                          >\r\n                            (?)\r\n                          </span>\r\n                        </label>\r\n                        {hoveredOption === 'enable_image_description' && SvgAnimations['enable_image_description'] && (\r\n                          <div className=\"absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none\">\r\n                            <Image src={SvgAnimations['enable_image_description']} alt=\"Image Description Animation\" width={180} height={120} />\r\n                          </div>\r\n                        )}\r\n                         <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                            {isFreeTier ? t('convert:enableImageDescriptionDescFree', '此为高级功能。') : t('convert:enableImageDescriptionDesc', '为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片。')}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    {/* 条件渲染：描述文字附加方式 和 描述丰富度 */}\r\n                    {effectiveOptions.enable_image_description && (\r\n                      <div className=\"mt-3 pl-8 space-y-4\">\r\n                        {/* 描述文字附加方式 */}\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700\">{t('convert:imageDescriptionAttachmentMode', '描述文字附加方式:')}</label>\r\n                          <div className=\"mt-1 flex space-x-6\">\r\n                            <div className=\"flex items-center\">\r\n                              <input\r\n                                id=\"attachment_mode_keep_image\"\r\n                                name=\"image_description_attachment_mode\"\r\n                                type=\"radio\"\r\n                                value=\"keep_image\"\r\n                                checked={effectiveOptions.image_description_attachment_mode === 'keep_image'}\r\n                                onChange={handleRadioChange}\r\n                                disabled={disabled || isFreeTier}\r\n                                className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                              />\r\n                              <label htmlFor=\"attachment_mode_keep_image\" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                                {t('convert:attachmentModeKeepImage', '保留图片')}\r\n                              </label>\r\n                            </div>\r\n                            <div className=\"flex items-center\">\r\n                              <input\r\n                                id=\"attachment_mode_replace_image\"\r\n                                name=\"image_description_attachment_mode\"\r\n                                type=\"radio\"\r\n                                value=\"replace_image\"\r\n                                checked={effectiveOptions.image_description_attachment_mode === 'replace_image'}\r\n                                onChange={handleRadioChange}\r\n                                disabled={disabled || isFreeTier}\r\n                                className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                              />\r\n                              <label htmlFor=\"attachment_mode_replace_image\" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                                {t('convert:attachmentModeReplaceImage', '替换图片')}\r\n                              </label>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* 描述丰富度 */}\r\n                        <div>\r\n                          <label className=\"block text-xs font-medium text-gray-700\">{t('convert:imageDescriptionStyle', '描述丰富度:')}</label>\r\n                          <div className=\"mt-1 flex space-x-6\">\r\n                              <div className=\"flex items-center\">\r\n                                  <input\r\n                                  id=\"desc_style_concise\"\r\n                                  name=\"image_description_style\"\r\n                                  type=\"radio\"\r\n                                  value=\"concise\"\r\n                                  checked={effectiveOptions.image_description_style === 'concise'}\r\n                                  onChange={handleRadioChange}\r\n                                  disabled={disabled || isFreeTier}\r\n                                  className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                                  />\r\n                                  <label htmlFor=\"desc_style_concise\" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                                  {t('convert:styleConcise', '简略')}\r\n                                  </label>\r\n                              </div>\r\n                              <div className=\"flex items-center\">\r\n                                  <input\r\n                                  id=\"desc_style_detailed\"\r\n                                  name=\"image_description_style\"\r\n                                  type=\"radio\"\r\n                                  value=\"detailed\"\r\n                                  checked={effectiveOptions.image_description_style === 'detailed'}\r\n                                  onChange={handleRadioChange}\r\n                                  disabled={disabled || isFreeTier}\r\n                                  className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                                  />\r\n                                  <label htmlFor=\"desc_style_detailed\" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                                  {t('convert:styleDetailed', '丰富')}\r\n                                  </label>\r\n                              </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* 尝试将图表类图片转换为 Mermaid 代码 */}\r\n                  <div className=\"flex items-start\">\r\n                    <div className=\"flex items-center h-5\">\r\n                      <input\r\n                        id=\"enable_charts_to_mermaid\"\r\n                        name=\"enable_charts_to_mermaid\"\r\n                        type=\"checkbox\"\r\n                        checked={effectiveOptions.enable_charts_to_mermaid || false}\r\n                        onChange={handleCheckboxChange}\r\n                        disabled={disabled || isFreeTier} // This is a new feature, likely premium\r\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"ml-3 text-sm\">\r\n                      <label htmlFor=\"enable_charts_to_mermaid\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                        {t('convert:enableChartsToMermaid', '尝试将图表类图片转换为 Mermaid 代码')}\r\n                      </label>\r\n                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                        {isFreeTier ? t('convert:enableChartsToMermaidDescFree', '此为高级功能。') : t('convert:enableChartsToMermaidDesc', '将图片中的流程图、序列图等转换为可编辑的 Mermaid 文本。')}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </Disclosure.Panel>\r\n              </>\r\n            )}\r\n          </Disclosure>\r\n\r\n          {/* Section 2: 文本与排版优化 */}\r\n          <Disclosure as=\"div\" defaultOpen>\r\n            {({ open }) => (\r\n              <>\r\n                <Disclosure.Button className=\"flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75\">\r\n                  <span>{t('convert:textSettingsTitle', '文本与排版优化')}</span>\r\n                   <svg className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`} fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </Disclosure.Button>\r\n                <Disclosure.Panel className=\"px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700\">\r\n                  {/* 智能处理换行与段落 */}\r\n                  <div className=\"flex items-start\">\r\n                    <div className=\"flex items-center h-5\">\r\n                      <input\r\n                        id=\"enable_llm_reformat\"\r\n                        name=\"enable_llm_reformat\"\r\n                        type=\"checkbox\"\r\n                        checked={effectiveOptions.enable_llm_reformat || false}\r\n                        onChange={handleCheckboxChange}\r\n                        disabled={disabled || isFreeTier}\r\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"ml-3 text-sm relative\">\r\n                      <label htmlFor=\"enable_llm_reformat\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                        {t('convert:enableLLMReformat', '智能处理换行与段落')}\r\n                        <span\r\n                          className=\"ml-2 text-blue-500 cursor-pointer\"\r\n                          onMouseEnter={() => setHoveredOption('enable_llm_reformat')}\r\n                          onMouseLeave={() => setHoveredOption(null)}\r\n                        >\r\n                          (?)\r\n                        </span>\r\n                      </label>\r\n                      {hoveredOption === 'enable_llm_reformat' && SvgAnimations['enable_llm_reformat'] && (\r\n                        <div className=\"absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none\">\r\n                          <Image src={SvgAnimations['enable_llm_reformat']} alt=\"LLM Reformat Animation\" width={180} height={120} />\r\n                        </div>\r\n                      )}\r\n                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                        {isFreeTier ? t('convert:enableLLMReformatDescFree', '此为高级功能。') : t('convert:enableLLMReformatDesc', '利用大语言模型优化文本的换行和分段，提升阅读体验。')}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 自动校对语法与错别字 */}\r\n                  <div className=\"flex items-start\">\r\n                    <div className=\"flex items-center h-5\">\r\n                      <input\r\n                        id=\"enable_grammar_correction\"\r\n                        name=\"enable_grammar_correction\"\r\n                        type=\"checkbox\"\r\n                        checked={effectiveOptions.enable_grammar_correction || false}\r\n                        onChange={handleCheckboxChange}\r\n                        disabled={disabled || isFreeTier}\r\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"ml-3 text-sm relative\">\r\n                      <label htmlFor=\"enable_grammar_correction\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                        {t('convert:enableGrammarCorrection', '自动校对语法与错别字')}\r\n                        <span\r\n                          className=\"ml-2 text-blue-500 cursor-pointer\"\r\n                          onMouseEnter={() => setHoveredOption('enable_grammar_correction')}\r\n                          onMouseLeave={() => setHoveredOption(null)}\r\n                        >\r\n                          (?)\r\n                        </span>\r\n                      </label>\r\n                      {hoveredOption === 'enable_grammar_correction' && SvgAnimations['enable_grammar_correction'] && (\r\n                        <div className=\"absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none\">\r\n                          <Image src={SvgAnimations['enable_grammar_correction']} alt=\"Grammar Correction Animation\" width={180} height={120} />\r\n                        </div>\r\n                      )}\r\n                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                        {isFreeTier ? t('convert:enableGrammarCorrectionDescFree', '此为高级功能。') : t('convert:enableGrammarCorrectionDesc', '自动检测并修正文档中的语法错误和拼写错误。')}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* 生成文档摘要 */}\r\n                  <div className=\"flex items-start\">\r\n                    <div className=\"flex items-center h-5\">\r\n                      <input\r\n                        id=\"enable_summarize\"\r\n                        name=\"enable_summarize\"\r\n                        type=\"checkbox\"\r\n                        checked={effectiveOptions.enable_summarize || false}\r\n                        onChange={handleCheckboxChange}\r\n                        disabled={disabled || isFreeTier}\r\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"ml-3 text-sm relative\">\r\n                      <label htmlFor=\"enable_summarize\" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>\r\n                        {t('convert:enableSummarize', '生成文档摘要')}\r\n                        <span\r\n                          className=\"ml-2 text-blue-500 cursor-pointer\"\r\n                          onMouseEnter={() => setHoveredOption('enable_summarize')}\r\n                          onMouseLeave={() => setHoveredOption(null)}\r\n                        >\r\n                          (?)\r\n                        </span>\r\n                      </label>\r\n                      {hoveredOption === 'enable_summarize' && SvgAnimations['enable_summarize'] && (\r\n                        <div className=\"absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none\">\r\n                          <Image src={SvgAnimations['enable_summarize']} alt=\"Summarize Animation\" width={180} height={120} />\r\n                        </div>\r\n                      )}\r\n                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>\r\n                        {isFreeTier ? t('convert:enableSummarizeDescFree', '此为高级功能。') : t('convert:enableSummarizeDesc', '自动为转换后的文档生成内容摘要。')}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </Disclosure.Panel>\r\n              </>\r\n            )}\r\n          </Disclosure>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConversionOptions;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA,8FAA8F;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;AAsBA,MAAM,oBAAsD,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,aAAa,IAAI,EAAE;;IACrH,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAU;IAClD,MAAM,aAAa,CAAC;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,gBAAkF;QACtF,kBAAkB,6RAAA,CAAA,UAAkB;QACpC,2BAA2B,+SAAA,CAAA,UAA0B;QACrD,qBAAqB,mSAAA,CAAA,UAAoB;QACzC,0BAA0B,6SAAA,CAAA,UAAyB;QACnD,0BAA0B,6SAAA,CAAA,UAAyB;IACrD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAClC,IAAI,cAAc,CAAC,SAAS,sBAAsB,SAAS,+BAA+B,SAAS,yBAAyB,SAAS,8BAA8B,SAAS,0BAA0B,GAAG;YACvM,iFAAiF;YACjF;QACF;QACA,SAAS;YACP,GAAG,OAAO;YACV,CAAC,KAAK,EAAE;QACV;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,MAAM,aAAa;QAEnB,IAAI,cAAc,eAAe,2BAA2B,UAAU,cAAc;YAChF,SAAS;gBAAE,GAAG,OAAO;gBAAE,CAAC,WAAW,EAAE;YAAa;YAClD;QACJ;QACA,IAAI,cAAc,eAAe,2BAA0B;YACvD;QACJ;QACA,yGAAyG;QACzG,IAAI,eAAe,2BAA2B,CAAC,UAAU,cAAc,UAAU,YAAY,GAAG;YAC9F,SAAS;gBAAE,GAAG,OAAO;gBAAE,CAAC,WAAW,EAAE;YAAM;QAC7C,OAAO,IAAI,eAAe,6BAA6B,CAAC,UAAU,aAAa,UAAU,UAAU,GAAG;YACpG,SAAS;gBAAE,GAAG,OAAO;gBAAE,CAAC,WAAW,EAAE;YAAM;QAC7C,OAAO,IAAI,eAAe,uCAAuC,CAAC,UAAU,gBAAgB,UAAU,eAAe,GAAG;YACtH,SAAS;gBAAE,GAAG,OAAO;gBAAE,CAAC,WAAW,EAAE;YAAM;QAC7C;IACF;IAEA,qEAAqE;IACrE,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;uDAAC;YACrC,wFAAwF;YACxF,MAAM,cAAc;gBAClB,GAAG,OAAO;gBACV,mCAAmC,QAAQ,iCAAiC,IAAI;YAClF;YAEA,IAAI,YAAY;gBACd,OAAO;oBACL,GAAG,WAAW;oBACd,kBAAkB;oBAClB,2BAA2B;oBAC3B,qBAAqB;oBACrB,uBAAuB;oBACvB,0BAA0B;oBAC1B,0BAA0B;oBAC1B,yBAAyB;oBACzB,oGAAoG;oBACpG,0BAA0B;gBAE5B;YACF;YACA,qFAAqF;YACrF,OAAO;gBAAE,GAAG,WAAW;gBAAE,iBAAiB;YAAU;QACtD;sDAAG;QAAC;QAAS;KAAW;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAGb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0LAAA,CAAA,aAAU;wBAAC,IAAG;wBAAM,WAAW;kCAC7B,CAAC,EAAE,IAAI,EAAE,iBACR;;kDACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,MAAM;wCAAC,WAAU;;0DAC3B,6LAAC;0DAAM,EAAE,8BAA8B;;;;;;0DACvC,6LAAC;gDAAI,WAAW,GAAG,OAAO,yBAAyB,GAAG,sBAAsB,CAAC;gDAAE,MAAK;gDAAe,SAAQ;0DACzG,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;kDAG7J,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;;0DAE1B,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA2C,EAAE,8BAA8B;;;;;;kEAC5F,6LAAC;wDAAI,WAAU;;4DAAiB;0EAC9B,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,IAAG;gFACH,MAAK;gFACL,MAAK;gFACL,OAAM;gFACN,SAAS,iBAAiB,qBAAqB,KAAK;gFACpD,UAAU;gFACV,UAAU,YAAY;gFACtB,WAAU;;;;;;0FAEZ,6LAAC;gFAAM,SAAQ;gFAAwB,WAAW,CAAC,+BAA+B,EAAE,aAAa,kBAAkB,iBAAiB;0FACjI,EAAE,+BAA+B;;;;;;;;;;;;kFAGtC,6LAAC;wEAAE,WAAW,CAAC,aAAa,EAAE,aAAa,kBAAkB,iBAAiB;kFAC3E,aACG,EAAE,6CAA6C,eAC/C,EAAE,6CAA6C;;;;;;;;;;;;0EAIvD,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFACC,IAAG;gFACH,MAAK;gFACL,MAAK;gFACL,OAAM;gFACN,SAAS,iBAAiB,qBAAqB,KAAK;gFACpD,UAAU;gFACV,UAAU,YAAY;gFACtB,WAAU;;;;;;0FAEZ,6LAAC;gFAAM,SAAQ;gFAAsB,WAAW,CAAC,+BAA+B,EAAE,aAAa,kBAAkB,iBAAiB;0FAC/H,EAAE,6BAA6B;;;;;;;;;;;;kFAGpC,6LAAC;wEAAE,WAAW,CAAC,aAAa,EAAE,aAAa,kBAAkB,iBAAiB;kFAC3E,aACG,EAAE,2CAA2C,aAC7C,EAAE,2CAA2C;;;;;;;;;;;;;;;;;;;;;;;;0DAOzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,SAAS,iBAAiB,wBAAwB,IAAI;4DACtD,UAAU;4DACV,UAAU,YAAY;4DACtB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,SAAQ;gEAA2B,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;;oEACjH,EAAE,kCAAkC;kFACrC,6LAAC;wEACC,WAAU;wEACV,cAAc,IAAM,iBAAiB;wEACrC,cAAc,IAAM,iBAAiB;kFACtC;;;;;;;;;;;;4DAIF,kBAAkB,8BAA8B,aAAa,CAAC,2BAA2B,kBACxF,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,CAAC,2BAA2B;oEAAE,KAAI;oEAA8B,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAGhH,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0EACvE,aAAa,EAAE,0CAA0C,aAAa,EAAE,sCAAsC;;;;;;;;;;;;;;;;;;0DAMrH,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,SAAS,iBAAiB,wBAAwB,IAAI;oEACtD,UAAU;oEACV,UAAU,YAAY;oEACtB,WAAU;;;;;;;;;;;0EAGd,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,SAAQ;wEAA2B,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;;4EACjH,EAAE,kCAAkC;0FACrC,6LAAC;gFACC,WAAU;gFACV,cAAc,IAAM,iBAAiB;gFACrC,cAAc,IAAM,iBAAiB;0FACtC;;;;;;;;;;;;oEAIF,kBAAkB,8BAA8B,aAAa,CAAC,2BAA2B,kBACxF,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EAAC,KAAK,aAAa,CAAC,2BAA2B;4EAAE,KAAI;4EAA8B,OAAO;4EAAK,QAAQ;;;;;;;;;;;kFAGhH,6LAAC;wEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;kFACrE,aAAa,EAAE,0CAA0C,aAAa,EAAE,sCAAsC;;;;;;;;;;;;;;;;;;oDAKtH,iBAAiB,wBAAwB,kBACxC,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA2C,EAAE,0CAA0C;;;;;;kFACxG,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,IAAG;wFACH,MAAK;wFACL,MAAK;wFACL,OAAM;wFACN,SAAS,iBAAiB,iCAAiC,KAAK;wFAChE,UAAU;wFACV,UAAU,YAAY;wFACtB,WAAU;;;;;;kGAEZ,6LAAC;wFAAM,SAAQ;wFAA6B,WAAW,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,iBAAiB;kGAC1H,EAAE,mCAAmC;;;;;;;;;;;;0FAG1C,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,IAAG;wFACH,MAAK;wFACL,MAAK;wFACL,OAAM;wFACN,SAAS,iBAAiB,iCAAiC,KAAK;wFAChE,UAAU;wFACV,UAAU,YAAY;wFACtB,WAAU;;;;;;kGAEZ,6LAAC;wFAAM,SAAQ;wFAAgC,WAAW,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,iBAAiB;kGAC7H,EAAE,sCAAsC;;;;;;;;;;;;;;;;;;;;;;;;0EAOjD,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA2C,EAAE,iCAAiC;;;;;;kFAC/F,6LAAC;wEAAI,WAAU;;0FACX,6LAAC;gFAAI,WAAU;;kGACX,6LAAC;wFACD,IAAG;wFACH,MAAK;wFACL,MAAK;wFACL,OAAM;wFACN,SAAS,iBAAiB,uBAAuB,KAAK;wFACtD,UAAU;wFACV,UAAU,YAAY;wFACtB,WAAU;;;;;;kGAEV,6LAAC;wFAAM,SAAQ;wFAAqB,WAAW,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,iBAAiB;kGACpH,EAAE,wBAAwB;;;;;;;;;;;;0FAG/B,6LAAC;gFAAI,WAAU;;kGACX,6LAAC;wFACD,IAAG;wFACH,MAAK;wFACL,MAAK;wFACL,OAAM;wFACN,SAAS,iBAAiB,uBAAuB,KAAK;wFACtD,UAAU;wFACV,UAAU,YAAY;wFACtB,WAAU;;;;;;kGAEV,6LAAC;wFAAM,SAAQ;wFAAsB,WAAW,CAAC,mBAAmB,EAAE,aAAa,kBAAkB,iBAAiB;kGACrH,EAAE,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAU5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,SAAS,iBAAiB,wBAAwB,IAAI;4DACtD,UAAU;4DACV,UAAU,YAAY;4DACtB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,SAAQ;gEAA2B,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;0EACjH,EAAE,iCAAiC;;;;;;0EAEtC,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0EACtE,aAAa,EAAE,yCAAyC,aAAa,EAAE,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU3H,6LAAC,0LAAA,CAAA,aAAU;wBAAC,IAAG;wBAAM,WAAW;kCAC7B,CAAC,EAAE,IAAI,EAAE,iBACR;;kDACE,6LAAC,0LAAA,CAAA,aAAU,CAAC,MAAM;wCAAC,WAAU;;0DAC3B,6LAAC;0DAAM,EAAE,6BAA6B;;;;;;0DACrC,6LAAC;gDAAI,WAAW,GAAG,OAAO,yBAAyB,GAAG,sBAAsB,CAAC;gDAAE,MAAK;gDAAe,SAAQ;0DAC1G,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;kDAG7J,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;;0DAE1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,SAAS,iBAAiB,mBAAmB,IAAI;4DACjD,UAAU;4DACV,UAAU,YAAY;4DACtB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,SAAQ;gEAAsB,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;;oEAC5G,EAAE,6BAA6B;kFAChC,6LAAC;wEACC,WAAU;wEACV,cAAc,IAAM,iBAAiB;wEACrC,cAAc,IAAM,iBAAiB;kFACtC;;;;;;;;;;;;4DAIF,kBAAkB,yBAAyB,aAAa,CAAC,sBAAsB,kBAC9E,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,CAAC,sBAAsB;oEAAE,KAAI;oEAAyB,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAGvG,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0EACtE,aAAa,EAAE,qCAAqC,aAAa,EAAE,iCAAiC;;;;;;;;;;;;;;;;;;0DAM3G,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,SAAS,iBAAiB,yBAAyB,IAAI;4DACvD,UAAU;4DACV,UAAU,YAAY;4DACtB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,SAAQ;gEAA4B,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;;oEAClH,EAAE,mCAAmC;kFACtC,6LAAC;wEACC,WAAU;wEACV,cAAc,IAAM,iBAAiB;wEACrC,cAAc,IAAM,iBAAiB;kFACtC;;;;;;;;;;;;4DAIF,kBAAkB,+BAA+B,aAAa,CAAC,4BAA4B,kBAC1F,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,CAAC,4BAA4B;oEAAE,KAAI;oEAA+B,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAGnH,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0EACtE,aAAa,EAAE,2CAA2C,aAAa,EAAE,uCAAuC;;;;;;;;;;;;;;;;;;0DAMvH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,SAAS,iBAAiB,gBAAgB,IAAI;4DAC9C,UAAU;4DACV,UAAU,YAAY;4DACtB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,SAAQ;gEAAmB,WAAW,CAAC,YAAY,EAAE,aAAa,kBAAkB,iBAAiB;;oEACzG,EAAE,2BAA2B;kFAC9B,6LAAC;wEACC,WAAU;wEACV,cAAc,IAAM,iBAAiB;wEACrC,cAAc,IAAM,iBAAiB;kFACtC;;;;;;;;;;;;4DAIF,kBAAkB,sBAAsB,aAAa,CAAC,mBAAmB,kBACxE,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEAAC,KAAK,aAAa,CAAC,mBAAmB;oEAAE,KAAI;oEAAsB,OAAO;oEAAK,QAAQ;;;;;;;;;;;0EAGjG,6LAAC;gEAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,iBAAiB;0EACtE,aAAa,EAAE,mCAAmC,aAAa,EAAE,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzH;GA1bM;;QACU,mKAAA,CAAA,iBAAc;;;KADxB;uCA4bS", "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/ConversionProgress.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ConversionProgressProps {\n  isConverting: boolean;\n  progress: number;\n  totalFiles: number;\n  completedFiles: number;\n}\n\nconst ConversionProgress: React.FC<ConversionProgressProps> = ({\n  isConverting,\n  progress,\n  totalFiles,\n  completedFiles,\n}) => {\n  if (!isConverting) {\n    return null;\n  }\n\n  const displayProgress = progress < 0.05 ? 0.05 : progress;\n\n  return (\n    <div className=\"mt-6 bg-white shadow sm:rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg font-medium leading-6 text-gray-900\">\n          Converting Files ({completedFiles}/{totalFiles})\n        </h3>\n        <div className=\"mt-4\">\n          <div className=\"relative pt-1\">\n            <div className=\"flex mb-2 items-center justify-between\">\n              <div>\n                <span className=\"text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200\">\n                  Progress\n                </span>\n              </div>\n              <div className=\"text-right\">\n                <span className=\"text-xs font-semibold inline-block text-blue-600\">\n                  {Math.round(displayProgress * 100)}%\n                </span>\n              </div>\n            </div>\n            <div className=\"overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200\">\n              <div\n                style={{ width: `${displayProgress * 100}%` }}\n                className=\"shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 transition-all duration-500 ease-in-out\"\n              ></div>\n            </div>\n          </div>\n          <p className=\"mt-2 text-sm text-gray-500\">\n            Please wait while your files are being converted to Markdown. This may take a few moments depending on the file size and complexity.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConversionProgress;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,qBAAwD,CAAC,EAC7D,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,cAAc,EACf;IACC,IAAI,CAAC,cAAc;QACjB,OAAO;IACT;IAEA,MAAM,kBAAkB,WAAW,OAAO,OAAO;IAEjD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;;wBAA8C;wBACvC;wBAAe;wBAAE;wBAAW;;;;;;;8BAEjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;sDAIlH,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDACb,KAAK,KAAK,CAAC,kBAAkB;oDAAK;;;;;;;;;;;;;;;;;;8CAIzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAO;4CAAE,OAAO,GAAG,kBAAkB,IAAI,CAAC,CAAC;wCAAC;wCAC5C,WAAU;;;;;;;;;;;;;;;;;sCAIhB,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD;KA9CM;uCAgDS", "debugId": null}}, {"offset": {"line": 1868, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/ResultDisplay.tsx"], "sourcesContent": ["import React from 'react';\nimport { FileStatus } from './FileListManager';\nimport { useTranslation } from 'react-i18next';\n\ninterface ResultItem {\n  id: string;\n  fileName: string;\n  status: FileStatus;\n  resultUrl?: string;\n  error?: string;\n}\n\ninterface ResultDisplayProps {\n  results: ResultItem[];\n  onDownloadFile: (id: string) => void;\n  onDownloadAll: () => void;\n  onRetryFile?: (id: string) => void;\n}\n\nconst ResultDisplay: React.FC<ResultDisplayProps> = ({\n  results,\n  onDownloadFile,\n  onDownloadAll,\n  onRetryFile,\n}) => {\n  const { t } = useTranslation(['common', 'convert']);\n  const finalResults = results.filter(result => result.status === 'completed' || result.status === 'failed');\n  const completedResultsCount = results.filter(result => result.status === 'completed').length;\n\n\n  if (finalResults.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"mt-6 bg-white shadow sm:rounded-lg\">\n      <div className=\"px-4 py-5 sm:p-6\">\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-lg font-medium leading-6 text-gray-900\">\n            {t('convert:conversionResultsTitle', 'Conversion Results')}\n          </h3>\n          {completedResultsCount > 1 && (\n            <button\n              type=\"button\"\n              onClick={onDownloadAll}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              {t('convert:downloadAll')}\n            </button>\n          )}\n        </div>\n        <div className=\"mt-4\">\n          {completedResultsCount > 0 && (\n            <div className=\"bg-green-50 border-l-4 border-green-400 p-4 mb-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg\n                    className=\"h-5 w-5 text-green-400\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    viewBox=\"0 0 20 20\"\n                    fill=\"currentColor\"\n                    aria-hidden=\"true\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm text-green-700\">\n                    {completedResultsCount === 1\n                      ? t('convert:successMessage')\n                      : t('convert:successMessagePlural', { count: completedResultsCount })}\n                  </p>\n                  {/* <p className=\"text-xs text-green-600 mt-1\">{t('convert:aiSuccessTip')}</p> */}\n                </div>\n              </div>\n            </div>\n          )}\n\n          <ul className=\"divide-y divide-gray-200\">\n            {finalResults.map((result) => (\n              <li key={result.id} className=\"py-4 flex justify-between items-center\">\n                <div className=\"flex items-center min-w-0\">\n                  {result.status === 'completed' ? (\n                    <svg\n                      className=\"h-6 w-6 text-green-500 flex-shrink-0\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                      />\n                    </svg>\n                  ) : ( // Failed\n                    <svg\n                      className=\"h-6 w-6 text-red-500 flex-shrink-0\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  )}\n                  <div className=\"ml-2 min-w-0\">\n                    <p className=\"text-sm text-gray-900 truncate\" title={result.fileName}>{result.fileName}</p>\n                    {result.status === 'failed' && result.error && (\n                        <p className=\"text-xs text-red-600 truncate\" title={result.error}>{result.error}</p>\n                    )}\n                  </div>\n                </div>\n                <div className=\"ml-2 flex-shrink-0 flex space-x-2\">\n                  {result.status === 'completed' && (\n                    <button\n                      type=\"button\"\n                      onClick={() => onDownloadFile(result.id)}\n                      className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                    >\n                      {t('convert:download')}\n                    </button>\n                  )}\n                  {result.status === 'failed' && onRetryFile && (\n                     <button\n                        type=\"button\"\n                        onClick={() => onRetryFile(result.id)}\n                        className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500\"\n                    >\n                        {t('convert:retry', 'Retry')}\n                    </button>\n                  )}\n                </div>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResultDisplay;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;;AAiBA,MAAM,gBAA8C,CAAC,EACnD,OAAO,EACP,cAAc,EACd,aAAa,EACb,WAAW,EACZ;;IACC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAU;IAClD,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,eAAe,OAAO,MAAM,KAAK;IACjG,MAAM,wBAAwB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,aAAa,MAAM;IAG5F,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE,kCAAkC;;;;;;wBAEtC,wBAAwB,mBACvB,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAET,EAAE;;;;;;;;;;;;8BAIT,6LAAC;oBAAI,WAAU;;wBACZ,wBAAwB,mBACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAM;4CACN,SAAQ;4CACR,MAAK;4CACL,eAAY;sDAEZ,cAAA,6LAAC;gDACC,UAAS;gDACT,GAAE;gDACF,UAAS;;;;;;;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDACV,0BAA0B,IACvB,EAAE,4BACF,EAAE,gCAAgC;gDAAE,OAAO;4CAAsB;;;;;;;;;;;;;;;;;;;;;;sCAQ/E,6LAAC;4BAAG,WAAU;sCACX,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;oCAAmB,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,MAAM,KAAK,4BACjB,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;yEAIN,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAEN,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAG3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;4DAAiC,OAAO,OAAO,QAAQ;sEAAG,OAAO,QAAQ;;;;;;wDACrF,OAAO,MAAM,KAAK,YAAY,OAAO,KAAK,kBACvC,6LAAC;4DAAE,WAAU;4DAAgC,OAAO,OAAO,KAAK;sEAAG,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAIvF,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,MAAM,KAAK,6BACjB,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,eAAe,OAAO,EAAE;oDACvC,WAAU;8DAET,EAAE;;;;;;gDAGN,OAAO,MAAM,KAAK,YAAY,6BAC5B,6LAAC;oDACE,MAAK;oDACL,SAAS,IAAM,YAAY,OAAO,EAAE;oDACpC,WAAU;8DAET,EAAE,iBAAiB;;;;;;;;;;;;;mCAjDrB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DhC;GA7HM;;QAMU,mKAAA,CAAA,iBAAc;;;KANxB;uCA+HS", "debugId": null}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/conversion/MarkdownPreviewer.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport DOMPurify from 'dompurify';\n\ninterface MarkdownPreviewerProps {\n  markdownContent: string;\n  fileName: string;\n  onClose?: () => void;\n}\n\nconst MarkdownPreviewer: React.FC<MarkdownPreviewerProps> = ({\n  markdownContent,\n  fileName,\n  onClose,\n}) => {\n  const [htmlContent, setHtmlContent] = useState<string>('');\n\n  useEffect(() => {\n    // In a real implementation, we would use a Markdown parser library\n    // like marked or markdown-it to convert Markdown to HTML\n    // For this example, we'll simulate it with a simple conversion\n    const simulateMarkdownToHtml = (markdown: string): string => {\n      // This is a very simplified example\n      // In a real app, use a proper Markdown parser\n      const html = markdown\n        .replace(/^# (.*$)/gm, '<h1>$1</h1>')\n        .replace(/^## (.*$)/gm, '<h2>$1</h2>')\n        .replace(/^### (.*$)/gm, '<h3>$1</h3>')\n        .replace(/\\*\\*(.*)\\*\\*/gm, '<strong>$1</strong>')\n        .replace(/\\*(.*)\\*/gm, '<em>$1</em>')\n        .replace(/\\n/gm, '<br>');\n      \n      return html;\n    };\n\n    // Convert markdown to HTML\n    const rawHtml = simulateMarkdownToHtml(markdownContent);\n    \n    // Sanitize HTML to prevent XSS attacks\n    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {\n      USE_PROFILES: { html: true },\n      ALLOWED_TAGS: [\n        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'hr',\n        'ul', 'ol', 'li', 'blockquote', 'pre', 'code',\n        'em', 'strong', 'del', 'a', 'img', 'table',\n        'thead', 'tbody', 'tr', 'th', 'td'\n      ],\n      ALLOWED_ATTR: ['href', 'src', 'alt', 'title']\n    });\n\n    setHtmlContent(sanitizedHtml);\n  }, [markdownContent]);\n\n  if (!markdownContent) {\n    return null;\n  }\n\n  return (\n    <div className=\"mt-6 bg-white shadow sm:rounded-lg fixed inset-0 z-50 overflow-auto flex items-center justify-center p-4 bg-black bg-opacity-50\">\n      <div className=\"bg-white shadow-xl sm:rounded-lg w-full max-w-3xl max-h-[90vh] flex flex-col\">\n        <div className=\"px-4 py-5 sm:p-6 border-b border-gray-200 flex justify-between items-center\">\n          <h3 className=\"text-lg font-medium leading-6 text-gray-900\">\n            Preview: {fileName}\n          </h3>\n          {onClose && (\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n              aria-label=\"Close preview\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n        </div>\n        <div className=\"p-4 sm:p-6 overflow-y-auto\">\n          <div className=\"prose prose-sm max-w-none\">\n            <div dangerouslySetInnerHTML={{ __html: htmlContent }} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MarkdownPreviewer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,oBAAsD,CAAC,EAC3D,eAAe,EACf,QAAQ,EACR,OAAO,EACR;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,mEAAmE;YACnE,yDAAyD;YACzD,+DAA+D;YAC/D,MAAM;sEAAyB,CAAC;oBAC9B,oCAAoC;oBACpC,8CAA8C;oBAC9C,MAAM,OAAO,SACV,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,kBAAkB,uBAC1B,OAAO,CAAC,cAAc,eACtB,OAAO,CAAC,QAAQ;oBAEnB,OAAO;gBACT;;YAEA,2BAA2B;YAC3B,MAAM,UAAU,uBAAuB;YAEvC,uCAAuC;YACvC,MAAM,gBAAgB,qJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,SAAS;gBAChD,cAAc;oBAAE,MAAM;gBAAK;gBAC3B,cAAc;oBACZ;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAK;oBAAM;oBAC/C;oBAAM;oBAAM;oBAAM;oBAAc;oBAAO;oBACvC;oBAAM;oBAAU;oBAAO;oBAAK;oBAAO;oBACnC;oBAAS;oBAAS;oBAAM;oBAAM;iBAC/B;gBACD,cAAc;oBAAC;oBAAQ;oBAAO;oBAAO;iBAAQ;YAC/C;YAEA,eAAe;QACjB;sCAAG;QAAC;KAAgB;IAEpB,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAA8C;gCAChD;;;;;;;wBAEX,yBACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK7E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,yBAAyB;gCAAE,QAAQ;4BAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;GA1EM;KAAA;uCA4ES", "debugId": null}}, {"offset": {"line": 2318, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/app/convert/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useCallback, useEffect } from 'react';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { useTranslation } from 'react-i18next';\r\nimport FileUploader from '../../components/conversion/FileUploader';\r\nimport FileListManager, { FileStatus } from '../../components/conversion/FileListManager';\r\nimport ConversionOptions, { ConversionOptionsType } from '../../components/conversion/ConversionOptions';\r\nimport ConversionProgress from '../../components/conversion/ConversionProgress';\r\nimport ResultDisplay from '../../components/conversion/ResultDisplay';\r\nimport MarkdownPreviewer from '../../components/conversion/MarkdownPreviewer';\r\nimport { useAuth, getLocalAuthToken } from '../../hooks/useAuth'; // Import useAuth\r\n\r\ninterface FileItem {\r\n  file: File;\r\n  id: string;\r\n  progress: number;\r\n  status: FileStatus;\r\n  error?: string;\r\n  resultUrl?: string; // This might be the S3 URL or a link to our /result endpoint\r\n  markdownContent?: string;\r\n  taskId?: string; // To store the backend task ID\r\n}\r\n\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n\r\nexport default function ConvertPage() {\r\n  const { t } = useTranslation(['common', 'convert']);\r\n  const { user, isLoading: authLoading } = useAuth(); // Get user and auth loading state\r\n  const [files, setFiles] = useState<FileItem[]>([]);\r\n  const [isConverting, setIsConverting] = useState(false);\r\n  const [overallProgress, setOverallProgress] = useState(0);\r\n  const [selectedPreviewId, setSelectedPreviewId] = useState<string | null>(null);\r\n  const [conversionOptions, setConversionOptions] = useState<ConversionOptionsType>({\r\n    enable_summarize: false,\r\n    enable_grammar_correction: false,\r\n    enable_llm_reformat: false,\r\n    image_mode_preference: 'referenced', // Default for free tier, 'embedded' for logged in\r\n    enable_image_recognition: false,\r\n    enable_image_description: false,\r\n    image_description_style: 'concise',\r\n    enable_charts_to_mermaid: false, // New option\r\n  });\r\n\r\n  // Update conversion options based on user login status\r\n  useEffect(() => {\r\n    if (!authLoading) {\r\n      if (user) {\r\n        // Logged-in user, set potentially premium defaults\r\n        setConversionOptions(prev => ({\r\n          ...prev,\r\n          enable_summarize: true,\r\n          enable_grammar_correction: true,\r\n          enable_llm_reformat: true,\r\n          image_mode_preference: 'embedded',\r\n          enable_image_recognition: true, // Assuming these are premium\r\n          enable_image_description: true,\r\n          enable_charts_to_mermaid: true, // Assuming this is premium\r\n        }));\r\n      } else {\r\n        // Non-logged-in user, enforce free tier options\r\n        setConversionOptions({\r\n          enable_summarize: false,\r\n          enable_grammar_correction: false,\r\n          enable_llm_reformat: false,\r\n          image_mode_preference: 'referenced',\r\n          enable_image_recognition: false,\r\n          enable_image_description: false,\r\n          image_description_style: 'concise',\r\n          enable_charts_to_mermaid: false,\r\n        });\r\n      }\r\n    }\r\n  }, [user, authLoading]);\r\n\r\n\r\n  const updateFileState = useCallback((id: string, updates: Partial<FileItem>) => {\r\n    setFiles(prevFiles =>\r\n      prevFiles.map(f => (f.id === id ? { ...f, ...updates } : f))\r\n    );\r\n  }, []);\r\n\r\n\r\n  const pollTaskStatus = useCallback(async (fileItemId: string, taskId: string) => {\r\n    console.log(`[${new Date().toISOString()}] Polling status for task ${taskId}, file ${fileItemId}. Current isConverting: ${isConverting}`);\r\n    const statusUrl = `${API_BASE_URL}/api/v1/tasks/${taskId}/status`;\r\n    try {\r\n      const response = await fetch(statusUrl);\r\n      if (!response.ok) {\r\n        const errorData = await response.json().catch(() => ({ detail: 'Failed to get task status' }));\r\n        updateFileState(fileItemId, { status: 'failed', error: errorData.detail || `Error ${response.status}`, progress: 1 });\r\n        return;\r\n      }\r\n\r\n      const data = await response.json();\r\n      const currentStatus = data.status.toLowerCase(); // Convert to lowercase for consistent comparison\r\n\r\n      updateFileState(fileItemId, {\r\n        status: currentStatus as FileStatus,\r\n        progress: data.progress,\r\n        error: currentStatus === 'failed' ? data.error_message : undefined, // Compare with lowercase\r\n      });\r\n\r\n      if (currentStatus === 'completed') { // Compare with lowercase\r\n        updateFileState(fileItemId, { resultUrl: data.result_url }); // Store the relative result URL\r\n        console.log(\"DEBUG: data.result_url from /status endpoint:\", data.result_url); // Log the raw result_url\r\n        // Now fetch the actual content from the result_url\r\n        const contentUrl = `${API_BASE_URL}${data.result_url}`; // Construct full URL\r\n        console.log(\"DEBUG: Constructed contentUrl for fetch:\", contentUrl); // Log the constructed URL\r\n        try {\r\n          const contentResponse = await fetch(contentUrl);\r\n          if (contentResponse.ok) {\r\n            // Assuming the result is markdown text for preview\r\n            const markdown = await contentResponse.text();\r\n            updateFileState(fileItemId, { markdownContent: markdown });\r\n          } else {\r\n            console.warn(`Failed to fetch content from ${contentUrl}: ${contentResponse.status}`);\r\n            // If it's not text, the user will use the download button which will hit the resultUrl directly\r\n          }\r\n        } catch (contentError) {\r\n          console.error(`Error fetching content from ${contentUrl}:`, contentError);\r\n          updateFileState(fileItemId, { error: t('convert:errorFetchingResult') });\r\n        }\r\n      } else if (currentStatus === 'failed') { // Compare with lowercase\r\n        // Error already set from above (or will be by updateFileState)\r\n      } else if (currentStatus === 'queued' || currentStatus === 'processing') { // Compare with lowercase\r\n        console.log(`[${new Date().toISOString()}] Task ${taskId} is ${currentStatus}. Scheduling next poll. Current isConverting: ${isConverting}`);\r\n        setTimeout(() => pollTaskStatus(fileItemId, taskId), 3000); // Poll again after 3 seconds\r\n      } else {\r\n        console.log(`[${new Date().toISOString()}] Task ${taskId} has unhandled status: ${currentStatus}. Stopping poll. Current isConverting: ${isConverting}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error polling task status:', error);\r\n      updateFileState(fileItemId, { status: 'failed', error: t('convert:errorPollingStatus'), progress: 1 });\r\n    }\r\n  }, [updateFileState, t, isConverting]);\r\n\r\n\r\n  const handleStartConversion = useCallback(async () => {\r\n    const pendingFiles = files.filter(file => file.status === 'pending' || file.status === 'failed');\r\n    if (pendingFiles.length === 0 || isConverting) return;\r\n\r\n    setIsConverting(true);\r\n    setOverallProgress(0);\r\n\r\n    // Reset progress for failed files being retried\r\n    setFiles(prevFiles =>\r\n        prevFiles.map(f =>\r\n            (f.status === 'failed' && pendingFiles.some(pf => pf.id === f.id))\r\n                ? { ...f, progress: 0, error: undefined, status: 'pending' }\r\n                : f\r\n        )\r\n    );\r\n\r\n\r\n    for (const fileItem of pendingFiles) {\r\n      updateFileState(fileItem.id, { status: 'uploading', progress: 0.05 });\r\n\r\n      const formData = new FormData();\r\n      formData.append('file', fileItem.file);\r\n      const token = getLocalAuthToken();\r\n      const headers: HeadersInit = {};\r\n      if (user && token) {\r\n        headers['Authorization'] = `Bearer ${token}`;\r\n      }\r\n\r\n      // Get the effective options based on login status for sending to backend\r\n      let effectiveSendOptions: ConversionOptionsType;\r\n      if (!user) {\r\n        effectiveSendOptions = { // Free tier options for backend\r\n          enable_summarize: false,\r\n          enable_grammar_correction: false,\r\n          enable_llm_reformat: false,\r\n          image_mode_preference: 'referenced',\r\n          enable_image_recognition: false,\r\n          enable_image_description: false,\r\n          image_description_style: 'concise',\r\n          enable_charts_to_mermaid: false, // Free tier default\r\n        };\r\n      } else {\r\n        effectiveSendOptions = { ...conversionOptions }; // Logged-in user's selected options\r\n      }\r\n\r\n      // Append conversion options to formData\r\n      if (effectiveSendOptions.enable_summarize !== undefined) {\r\n        formData.append('enable_summarize', String(effectiveSendOptions.enable_summarize));\r\n      }\r\n      if (effectiveSendOptions.enable_grammar_correction !== undefined) {\r\n        formData.append('enable_grammar_correction', String(effectiveSendOptions.enable_grammar_correction));\r\n      }\r\n      if (effectiveSendOptions.enable_llm_reformat !== undefined) {\r\n        formData.append('enable_llm_reformat', String(effectiveSendOptions.enable_llm_reformat));\r\n      }\r\n      if (effectiveSendOptions.image_mode_preference) {\r\n        formData.append('image_mode_preference', effectiveSendOptions.image_mode_preference);\r\n      }\r\n      // processing_mode is removed\r\n      if (effectiveSendOptions.enable_image_recognition !== undefined) {\r\n        formData.append('enable_image_recognition', String(effectiveSendOptions.enable_image_recognition));\r\n      }\r\n      if (effectiveSendOptions.enable_image_description !== undefined) {\r\n        formData.append('enable_image_description', String(effectiveSendOptions.enable_image_description));\r\n      }\r\n      if (effectiveSendOptions.image_description_style && effectiveSendOptions.enable_image_description) { // Only send style if description is enabled\r\n        formData.append('image_description_style', effectiveSendOptions.image_description_style);\r\n      }\r\n\r\n      try {\r\n        const convertUrl = `${API_BASE_URL}/api/v1/tasks/convert`;\r\n        console.log('Attempting to call convert API at:', convertUrl); //诊断日志\r\n        const response = await fetch(convertUrl, {\r\n          method: 'POST',\r\n          headers,\r\n          body: formData,\r\n        });\r\n\r\n        if (response.status === 202) { // Accepted\r\n          const data = await response.json();\r\n          updateFileState(fileItem.id, {\r\n            taskId: data.task_id,\r\n            status: data.status.toLowerCase() as FileStatus || 'queued', // Use status from response\r\n            progress: 0.1, // Initial progress after acceptance\r\n          });\r\n          pollTaskStatus(fileItem.id, data.task_id);\r\n        } else {\r\n          const errorData = await response.json().catch(() => ({ detail: 'Conversion request failed' }));\r\n          updateFileState(fileItem.id, { status: 'failed', error: errorData.detail || `Error ${response.status}`, progress: 0 });\r\n        }\r\n      } catch (error) {\r\n        console.error('Error starting conversion for file:', fileItem.file.name, error);\r\n        updateFileState(fileItem.id, { status: 'failed', error: t('convert:errorStartingConversion'), progress: 0 });\r\n      }\r\n    }\r\n    // setIsConverting will be set to false once all pollTaskStatus calls complete or fail.\r\n    // For now, we can set it based on whether any tasks are still in progress.\r\n    // This part needs refinement based on how pollTaskStatus updates global state.\r\n  }, [files, isConverting, updateFileState, pollTaskStatus, t]);\r\n\r\n\r\n  useEffect(() => {\r\n    const anyFileProcessing = files.some(f => f.status === 'uploading' || f.status === 'processing' || f.status === 'queued');\r\n    if (!anyFileProcessing && isConverting) {\r\n        // Check if all files that were attempted have a final state (completed or failed)\r\n        const attemptedFiles = files.filter(f => f.taskId); // Files for which conversion was started\r\n        const allAttemptedFilesDone = attemptedFiles.every(f => f.status === 'completed' || f.status === 'failed');\r\n        if (allAttemptedFilesDone && attemptedFiles.length > 0) {\r\n            setIsConverting(false);\r\n        } else if (attemptedFiles.length === 0 && files.length > 0) {\r\n            // No tasks were even started, but conversion was triggered\r\n            setIsConverting(false);\r\n        }\r\n    }\r\n\r\n    // Calculate overall progress\r\n    if (files.length > 0) {\r\n        const totalProgress = files.reduce((sum, file) => sum + file.progress, 0);\r\n        setOverallProgress(files.length > 0 ? totalProgress / files.length : 0);\r\n    } else {\r\n        setOverallProgress(0);\r\n    }\r\n  }, [files, isConverting]);\r\n\r\n\r\n  const handleFilesSelected = useCallback((selectedFiles: File[]) => {\r\n    const newFileItems = selectedFiles.map(file => ({\r\n      file,\r\n      id: uuidv4(),\r\n      progress: 0,\r\n      status: 'pending' as FileStatus,\r\n    }));\r\n    setFiles(prevFiles => [...prevFiles, ...newFileItems]);\r\n  }, []);\r\n\r\n  const handleRemoveFile = useCallback((id: string) => {\r\n    setFiles(prevFiles => prevFiles.filter(file => file.id !== id));\r\n    if (selectedPreviewId === id) {\r\n      setSelectedPreviewId(null);\r\n    }\r\n  }, [selectedPreviewId]);\r\n\r\n  const handleRemoveAllFiles = useCallback(() => {\r\n    setFiles([]);\r\n    setSelectedPreviewId(null);\r\n  }, []);\r\n\r\n  const handleDownloadFile = useCallback(async (id: string) => {\r\n    const fileItem = files.find(file => file.id === id);\r\n    if (!fileItem) return;\r\n\r\n    if (fileItem.markdownContent && fileItem.status === 'completed') {\r\n      const blob = new Blob([fileItem.markdownContent], { type: 'text/markdown;charset=utf-8' });\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${fileItem.file.name.split('.')[0] || 'converted'}.md`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      URL.revokeObjectURL(url);\r\n    } else if (fileItem.resultUrl && fileItem.status === 'completed') {\r\n      // This means the content wasn't fetched as markdown, or it's not markdown.\r\n      // The resultUrl from the backend should lead to a direct download or a page that initiates download.\r\n      // For pre-signed S3 URLs, this would directly start the download.\r\n      // If it's our own /api/v1/tasks/{task_id}/result, that endpoint must handle Content-Disposition.\r\n      const downloadUrl = fileItem.resultUrl.startsWith('http') ? fileItem.resultUrl : `${API_BASE_URL}${fileItem.resultUrl}`;\r\n      window.open(downloadUrl, '_blank');\r\n    } else {\r\n      alert(t('convert:noResultToDownload'));\r\n    }\r\n  }, [files, t]);\r\n\r\n  const handleDownloadAll = useCallback(async () => {\r\n    // This would ideally call a backend endpoint that zips all completed files for this user/session.\r\n    // For now, let's just try to download all completed files individually.\r\n    const completed = files.filter(f => f.status === 'completed');\r\n    if (completed.length === 0) {\r\n      alert(t('convert:noCompletedFilesToDownload'));\r\n      return;\r\n    }\r\n    for (const fileItem of completed) {\r\n      await handleDownloadFile(fileItem.id); // Add await if downloads need to be sequential for some reason\r\n    }\r\n  }, [files, handleDownloadFile, t]);\r\n\r\n  const handlePreviewFile = useCallback((id: string) => {\r\n    const fileItem = files.find(f => f.id === id);\r\n    if (fileItem && fileItem.markdownContent && fileItem.status === 'completed') {\r\n      setSelectedPreviewId(id);\r\n    } else if (fileItem && fileItem.status === 'completed' && !fileItem.markdownContent) {\r\n        alert(t('convert:previewNotAvailableNonMarkdown'));\r\n    } else if (fileItem && fileItem.status !== 'completed') {\r\n        alert(t('convert:previewNotReady'));\r\n    }\r\n  }, [files, t]);\r\n\r\n  const handleRetryFile = useCallback((id: string) => {\r\n    const fileToRetry = files.find(f => f.id === id);\r\n    if (fileToRetry && fileToRetry.status === 'failed') {\r\n        updateFileState(id, { status: 'pending', progress: 0, error: undefined, taskId: undefined, resultUrl: undefined, markdownContent: undefined });\r\n        // Optionally, trigger handleStartConversion if not already converting\r\n        // For simplicity, user can click \"Start Conversion\" again.\r\n    }\r\n  }, [files, updateFileState]);\r\n\r\n\r\n  const completedFilesCount = files.filter(file => file.status === 'completed').length;\r\n  const selectedPreviewFile = selectedPreviewId\r\n    ? files.find(file => file.id === selectedPreviewId)\r\n    : null;\r\n\r\n  return (\r\n    <div className=\"py-6\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <h1 className=\"text-2xl font-semibold text-gray-900\">{t('convert:title')}</h1>\r\n        <p className=\"mt-2 text-sm text-gray-500\">\r\n          {t('convert:description')}\r\n        </p>\r\n        {/* Supported formats display can be dynamic based on backend capabilities if needed */}\r\n        <div className=\"mt-2 flex flex-wrap gap-2\">\r\n          <span className=\"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">PDF</span>\r\n          <span className=\"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">Word (DOC/DOCX)</span>\r\n          <span className=\"inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">HTML</span>\r\n          {/* Add more as supported */}\r\n        </div>\r\n\r\n        <div className=\"mt-4 bg-blue-50 border-l-4 border-blue-400 p-4\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-blue-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-blue-700\">\r\n                {t('convert:aiTip')}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-6\">\r\n          <FileUploader onFilesSelected={handleFilesSelected} disabled={isConverting} />\r\n\r\n          <FileListManager\r\n            files={files}\r\n            onRemoveFile={handleRemoveFile}\r\n            onRemoveAllFiles={handleRemoveAllFiles}\r\n            onDownloadFile={handleDownloadFile}\r\n            onRetryFile={handleRetryFile}\r\n            onPreviewFile={handlePreviewFile}\r\n            isConverting={isConverting}\r\n          />\r\n\r\n          <ConversionOptions\r\n            options={conversionOptions}\r\n            onChange={setConversionOptions}\r\n            disabled={isConverting || !user || authLoading} // Disable if not logged in, or auth is loading\r\n            isLoggedIn={!!user} // Pass login status to potentially disable specific options internally\r\n          />\r\n\r\n          {files.length > 0 && (\r\n            <div className=\"mt-6\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleStartConversion}\r\n                disabled={isConverting || files.every(f => f.status === 'completed' || f.status === 'processing' || f.status === 'uploading' || f.status === 'queued')}\r\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed\"\r\n              >\r\n                {isConverting ? t('convert:converting') : t('convert:startConversion')}\r\n              </button>\r\n            </div>\r\n          )}\r\n\r\n          <ConversionProgress\r\n            isConverting={isConverting}\r\n            progress={overallProgress}\r\n            totalFiles={files.length}\r\n            completedFiles={completedFilesCount}\r\n          />\r\n\r\n          {/* ResultDisplay might need adjustment if we are not showing individual results before all are done */}\r\n          {files.some(f => f.status === 'completed' || f.status === 'failed') && (\r\n            <ResultDisplay\r\n              results={files.map(file => ({\r\n                id: file.id,\r\n                fileName: file.file.name,\r\n                status: file.status,\r\n                resultUrl: file.resultUrl, // This is the API result URL, not necessarily direct download\r\n                error: file.error,\r\n              }))}\r\n              onDownloadFile={handleDownloadFile}\r\n              onDownloadAll={handleDownloadAll}\r\n              onRetryFile={handleRetryFile}\r\n            />\r\n          )}\r\n\r\n          {selectedPreviewFile && selectedPreviewFile.markdownContent && selectedPreviewFile.status === 'completed' && (\r\n            <MarkdownPreviewer\r\n              markdownContent={selectedPreviewFile.markdownContent}\r\n              fileName={selectedPreviewFile.file.name}\r\n              onClose={() => setSelectedPreviewId(null)}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAwBqB;;AAtBrB;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2MAAkE,iBAAiB;;;AAXnF;;;;;;;;;;;AAwBA,MAAM,eAAe,6DAAwC;AAE9C,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE;QAAC;QAAU;KAAU;IAClD,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD,KAAK,kCAAkC;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;QAChF,kBAAkB;QAClB,2BAA2B;QAC3B,qBAAqB;QACrB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,0BAA0B;IAC5B;IAEA,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,aAAa;gBAChB,IAAI,MAAM;oBACR,mDAAmD;oBACnD;iDAAqB,CAAA,OAAQ,CAAC;gCAC5B,GAAG,IAAI;gCACP,kBAAkB;gCAClB,2BAA2B;gCAC3B,qBAAqB;gCACrB,uBAAuB;gCACvB,0BAA0B;gCAC1B,0BAA0B;gCAC1B,0BAA0B;4BAC5B,CAAC;;gBACH,OAAO;oBACL,gDAAgD;oBAChD,qBAAqB;wBACnB,kBAAkB;wBAClB,2BAA2B;wBAC3B,qBAAqB;wBACrB,uBAAuB;wBACvB,0BAA0B;wBAC1B,0BAA0B;wBAC1B,yBAAyB;wBACzB,0BAA0B;oBAC5B;gBACF;YACF;QACF;gCAAG;QAAC;QAAM;KAAY;IAGtB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,IAAY;YAC/C;4DAAS,CAAA,YACP,UAAU,GAAG;oEAAC,CAAA,IAAM,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;;;QAE7D;mDAAG,EAAE;IAGL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO,YAAoB;YAC5D,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,0BAA0B,EAAE,OAAO,OAAO,EAAE,WAAW,wBAAwB,EAAE,cAAc;YACxI,MAAM,YAAY,GAAG,aAAa,cAAc,EAAE,OAAO,OAAO,CAAC;YACjE,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK;mEAAC,IAAM,CAAC;gCAAE,QAAQ;4BAA4B,CAAC;;oBAC5F,gBAAgB,YAAY;wBAAE,QAAQ;wBAAU,OAAO,UAAU,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;wBAAE,UAAU;oBAAE;oBACnH;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,gBAAgB,KAAK,MAAM,CAAC,WAAW,IAAI,iDAAiD;gBAElG,gBAAgB,YAAY;oBAC1B,QAAQ;oBACR,UAAU,KAAK,QAAQ;oBACvB,OAAO,kBAAkB,WAAW,KAAK,aAAa,GAAG;gBAC3D;gBAEA,IAAI,kBAAkB,aAAa;oBACjC,gBAAgB,YAAY;wBAAE,WAAW,KAAK,UAAU;oBAAC,IAAI,gCAAgC;oBAC7F,QAAQ,GAAG,CAAC,iDAAiD,KAAK,UAAU,GAAG,yBAAyB;oBACxG,mDAAmD;oBACnD,MAAM,aAAa,GAAG,eAAe,KAAK,UAAU,EAAE,EAAE,qBAAqB;oBAC7E,QAAQ,GAAG,CAAC,4CAA4C,aAAa,0BAA0B;oBAC/F,IAAI;wBACF,MAAM,kBAAkB,MAAM,MAAM;wBACpC,IAAI,gBAAgB,EAAE,EAAE;4BACtB,mDAAmD;4BACnD,MAAM,WAAW,MAAM,gBAAgB,IAAI;4BAC3C,gBAAgB,YAAY;gCAAE,iBAAiB;4BAAS;wBAC1D,OAAO;4BACL,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,WAAW,EAAE,EAAE,gBAAgB,MAAM,EAAE;wBACpF,gGAAgG;wBAClG;oBACF,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC,EAAE;wBAC5D,gBAAgB,YAAY;4BAAE,OAAO,EAAE;wBAA+B;oBACxE;gBACF,OAAO,IAAI,kBAAkB,UAAU;gBACrC,+DAA+D;gBACjE,OAAO,IAAI,kBAAkB,YAAY,kBAAkB,cAAc;oBACvE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,OAAO,EAAE,OAAO,IAAI,EAAE,cAAc,8CAA8C,EAAE,cAAc;oBAC3I;mEAAW,IAAM,eAAe,YAAY;kEAAS,OAAO,6BAA6B;gBAC3F,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,OAAO,EAAE,OAAO,uBAAuB,EAAE,cAAc,uCAAuC,EAAE,cAAc;gBACzJ;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,gBAAgB,YAAY;oBAAE,QAAQ;oBAAU,OAAO,EAAE;oBAA+B,UAAU;gBAAE;YACtG;QACF;kDAAG;QAAC;QAAiB;QAAG;KAAa;IAGrC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACxC,MAAM,eAAe,MAAM,MAAM;+EAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;;YACvF,IAAI,aAAa,MAAM,KAAK,KAAK,cAAc;YAE/C,gBAAgB;YAChB,mBAAmB;YAEnB,gDAAgD;YAChD;kEAAS,CAAA,YACL,UAAU,GAAG;0EAAC,CAAA,IACV,AAAC,EAAE,MAAM,KAAK,YAAY,aAAa,IAAI;kFAAC,CAAA,KAAM,GAAG,EAAE,KAAK,EAAE,EAAE;mFAC1D;gCAAE,GAAG,CAAC;gCAAE,UAAU;gCAAG,OAAO;gCAAW,QAAQ;4BAAU,IACzD;;;YAKd,KAAK,MAAM,YAAY,aAAc;gBACnC,gBAAgB,SAAS,EAAE,EAAE;oBAAE,QAAQ;oBAAa,UAAU;gBAAK;gBAEnE,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ,SAAS,IAAI;gBACrC,MAAM,QAAQ,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD;gBAC9B,MAAM,UAAuB,CAAC;gBAC9B,IAAI,QAAQ,OAAO;oBACjB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;gBAC9C;gBAEA,yEAAyE;gBACzE,IAAI;gBACJ,IAAI,CAAC,MAAM;oBACT,uBAAuB;wBACrB,kBAAkB;wBAClB,2BAA2B;wBAC3B,qBAAqB;wBACrB,uBAAuB;wBACvB,0BAA0B;wBAC1B,0BAA0B;wBAC1B,yBAAyB;wBACzB,0BAA0B;oBAC5B;gBACF,OAAO;oBACL,uBAAuB;wBAAE,GAAG,iBAAiB;oBAAC,GAAG,oCAAoC;gBACvF;gBAEA,wCAAwC;gBACxC,IAAI,qBAAqB,gBAAgB,KAAK,WAAW;oBACvD,SAAS,MAAM,CAAC,oBAAoB,OAAO,qBAAqB,gBAAgB;gBAClF;gBACA,IAAI,qBAAqB,yBAAyB,KAAK,WAAW;oBAChE,SAAS,MAAM,CAAC,6BAA6B,OAAO,qBAAqB,yBAAyB;gBACpG;gBACA,IAAI,qBAAqB,mBAAmB,KAAK,WAAW;oBAC1D,SAAS,MAAM,CAAC,uBAAuB,OAAO,qBAAqB,mBAAmB;gBACxF;gBACA,IAAI,qBAAqB,qBAAqB,EAAE;oBAC9C,SAAS,MAAM,CAAC,yBAAyB,qBAAqB,qBAAqB;gBACrF;gBACA,6BAA6B;gBAC7B,IAAI,qBAAqB,wBAAwB,KAAK,WAAW;oBAC/D,SAAS,MAAM,CAAC,4BAA4B,OAAO,qBAAqB,wBAAwB;gBAClG;gBACA,IAAI,qBAAqB,wBAAwB,KAAK,WAAW;oBAC/D,SAAS,MAAM,CAAC,4BAA4B,OAAO,qBAAqB,wBAAwB;gBAClG;gBACA,IAAI,qBAAqB,uBAAuB,IAAI,qBAAqB,wBAAwB,EAAE;oBACjG,SAAS,MAAM,CAAC,2BAA2B,qBAAqB,uBAAuB;gBACzF;gBAEA,IAAI;oBACF,MAAM,aAAa,GAAG,aAAa,qBAAqB,CAAC;oBACzD,QAAQ,GAAG,CAAC,sCAAsC,aAAa,MAAM;oBACrE,MAAM,WAAW,MAAM,MAAM,YAAY;wBACvC,QAAQ;wBACR;wBACA,MAAM;oBACR;oBAEA,IAAI,SAAS,MAAM,KAAK,KAAK;wBAC3B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,gBAAgB,SAAS,EAAE,EAAE;4BAC3B,QAAQ,KAAK,OAAO;4BACpB,QAAQ,KAAK,MAAM,CAAC,WAAW,MAAoB;4BACnD,UAAU;wBACZ;wBACA,eAAe,SAAS,EAAE,EAAE,KAAK,OAAO;oBAC1C,OAAO;wBACL,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK;8EAAC,IAAM,CAAC;oCAAE,QAAQ;gCAA4B,CAAC;;wBAC5F,gBAAgB,SAAS,EAAE,EAAE;4BAAE,QAAQ;4BAAU,OAAO,UAAU,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;4BAAE,UAAU;wBAAE;oBACtH;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uCAAuC,SAAS,IAAI,CAAC,IAAI,EAAE;oBACzE,gBAAgB,SAAS,EAAE,EAAE;wBAAE,QAAQ;wBAAU,OAAO,EAAE;wBAAoC,UAAU;oBAAE;gBAC5G;YACF;QACA,uFAAuF;QACvF,2EAA2E;QAC3E,+EAA+E;QACjF;yDAAG;QAAC;QAAO;QAAc;QAAiB;QAAgB;KAAE;IAG5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,oBAAoB,MAAM,IAAI;2DAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,gBAAgB,EAAE,MAAM,KAAK;;YAChH,IAAI,CAAC,qBAAqB,cAAc;gBACpC,kFAAkF;gBAClF,MAAM,iBAAiB,MAAM,MAAM;4DAAC,CAAA,IAAK,EAAE,MAAM;4DAAG,yCAAyC;gBAC7F,MAAM,wBAAwB,eAAe,KAAK;mEAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK;;gBACjG,IAAI,yBAAyB,eAAe,MAAM,GAAG,GAAG;oBACpD,gBAAgB;gBACpB,OAAO,IAAI,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,GAAG;oBACxD,2DAA2D;oBAC3D,gBAAgB;gBACpB;YACJ;YAEA,6BAA6B;YAC7B,IAAI,MAAM,MAAM,GAAG,GAAG;gBAClB,MAAM,gBAAgB,MAAM,MAAM;2DAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ;0DAAE;gBACvE,mBAAmB,MAAM,MAAM,GAAG,IAAI,gBAAgB,MAAM,MAAM,GAAG;YACzE,OAAO;gBACH,mBAAmB;YACvB;QACF;gCAAG;QAAC;QAAO;KAAa;IAGxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACvC,MAAM,eAAe,cAAc,GAAG;6EAAC,CAAA,OAAQ,CAAC;wBAC9C;wBACA,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;wBACT,UAAU;wBACV,QAAQ;oBACV,CAAC;;YACD;gEAAS,CAAA,YAAa;2BAAI;2BAAc;qBAAa;;QACvD;uDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC;6DAAS,CAAA,YAAa,UAAU,MAAM;qEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;YAC3D,IAAI,sBAAsB,IAAI;gBAC5B,qBAAqB;YACvB;QACF;oDAAG;QAAC;KAAkB;IAEtB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACvC,SAAS,EAAE;YACX,qBAAqB;QACvB;wDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YAC5C,MAAM,WAAW,MAAM,IAAI;wEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;YAChD,IAAI,CAAC,UAAU;YAEf,IAAI,SAAS,eAAe,IAAI,SAAS,MAAM,KAAK,aAAa;gBAC/D,MAAM,OAAO,IAAI,KAAK;oBAAC,SAAS,eAAe;iBAAC,EAAE;oBAAE,MAAM;gBAA8B;gBACxF,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,YAAY,GAAG,CAAC;gBACvE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,IAAI,eAAe,CAAC;YACtB,OAAO,IAAI,SAAS,SAAS,IAAI,SAAS,MAAM,KAAK,aAAa;gBAChE,2EAA2E;gBAC3E,qGAAqG;gBACrG,kEAAkE;gBAClE,iGAAiG;gBACjG,MAAM,cAAc,SAAS,SAAS,CAAC,UAAU,CAAC,UAAU,SAAS,SAAS,GAAG,GAAG,eAAe,SAAS,SAAS,EAAE;gBACvH,OAAO,IAAI,CAAC,aAAa;YAC3B,OAAO;gBACL,MAAM,EAAE;YACV;QACF;sDAAG;QAAC;QAAO;KAAE;IAEb,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,kGAAkG;YAClG,wEAAwE;YACxE,MAAM,YAAY,MAAM,MAAM;wEAAC,CAAA,IAAK,EAAE,MAAM,KAAK;;YACjD,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,MAAM,EAAE;gBACR;YACF;YACA,KAAK,MAAM,YAAY,UAAW;gBAChC,MAAM,mBAAmB,SAAS,EAAE,GAAG,+DAA+D;YACxG;QACF;qDAAG;QAAC;QAAO;QAAoB;KAAE;IAEjC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACrC,MAAM,WAAW,MAAM,IAAI;uEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YAC1C,IAAI,YAAY,SAAS,eAAe,IAAI,SAAS,MAAM,KAAK,aAAa;gBAC3E,qBAAqB;YACvB,OAAO,IAAI,YAAY,SAAS,MAAM,KAAK,eAAe,CAAC,SAAS,eAAe,EAAE;gBACjF,MAAM,EAAE;YACZ,OAAO,IAAI,YAAY,SAAS,MAAM,KAAK,aAAa;gBACpD,MAAM,EAAE;YACZ;QACF;qDAAG;QAAC;QAAO;KAAE;IAEb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACnC,MAAM,cAAc,MAAM,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YAC7C,IAAI,eAAe,YAAY,MAAM,KAAK,UAAU;gBAChD,gBAAgB,IAAI;oBAAE,QAAQ;oBAAW,UAAU;oBAAG,OAAO;oBAAW,QAAQ;oBAAW,WAAW;oBAAW,iBAAiB;gBAAU;YAC5I,sEAAsE;YACtE,2DAA2D;YAC/D;QACF;mDAAG;QAAC;QAAO;KAAgB;IAG3B,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;IACpF,MAAM,sBAAsB,oBACxB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,qBAC/B;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC,EAAE;;;;;;8BACxD,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAGL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAgI;;;;;;sCAChJ,6LAAC;4BAAK,WAAU;sCAAgI;;;;;;sCAChJ,6LAAC;4BAAK,WAAU;sCAAgI;;;;;;;;;;;;8BAIlJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwB,OAAM;oCAA6B,SAAQ;oCAAY,MAAK;8CACjG,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA0O,UAAS;;;;;;;;;;;;;;;;0CAGlR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;8BAMX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4IAAA,CAAA,UAAY;4BAAC,iBAAiB;4BAAqB,UAAU;;;;;;sCAE9D,6LAAC,+IAAA,CAAA,UAAe;4BACd,OAAO;4BACP,cAAc;4BACd,kBAAkB;4BAClB,gBAAgB;4BAChB,aAAa;4BACb,eAAe;4BACf,cAAc;;;;;;sCAGhB,6LAAC,iJAAA,CAAA,UAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,UAAU,gBAAgB,CAAC,QAAQ;4BACnC,YAAY,CAAC,CAAC;;;;;;wBAGf,MAAM,MAAM,GAAG,mBACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,gBAAgB,MAAM,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,gBAAgB,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK;gCAC7I,WAAU;0CAET,eAAe,EAAE,wBAAwB,EAAE;;;;;;;;;;;sCAKlD,6LAAC,kJAAA,CAAA,UAAkB;4BACjB,cAAc;4BACd,UAAU;4BACV,YAAY,MAAM,MAAM;4BACxB,gBAAgB;;;;;;wBAIjB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,2BACxD,6LAAC,6IAAA,CAAA,UAAa;4BACZ,SAAS,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oCAC1B,IAAI,KAAK,EAAE;oCACX,UAAU,KAAK,IAAI,CAAC,IAAI;oCACxB,QAAQ,KAAK,MAAM;oCACnB,WAAW,KAAK,SAAS;oCACzB,OAAO,KAAK,KAAK;gCACnB,CAAC;4BACD,gBAAgB;4BAChB,eAAe;4BACf,aAAa;;;;;;wBAIhB,uBAAuB,oBAAoB,eAAe,IAAI,oBAAoB,MAAM,KAAK,6BAC5F,6LAAC,iJAAA,CAAA,UAAiB;4BAChB,iBAAiB,oBAAoB,eAAe;4BACpD,UAAU,oBAAoB,IAAI,CAAC,IAAI;4BACvC,SAAS,IAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;AAOlD;GArawB;;QACR,mKAAA,CAAA,iBAAc;QACa,mHAAA,CAAA,UAAO;;;KAF1B", "debugId": null}}]}