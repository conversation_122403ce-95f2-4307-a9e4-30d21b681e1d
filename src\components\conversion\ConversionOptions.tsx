import React, { useState } from 'react';
import Image from 'next/image';

// Import SVGs. Adjust paths if they are different or if you prefer to use them as components.
import SummarizeAnimation from '../../public/summarize_animation.svg';
import GrammarCorrectionAnimation from '../../public/grammar_correction_animation.svg';
import LlmReformatAnimation from '../../public/llm_reformat_animation.svg';
import ImageRecognitionAnimation from '../../public/image_recognition_animation.svg';
import ImageDescriptionAnimation from '../../public/image_description_animation.svg';
import { Disclosure } from '@headlessui/react';
import { useTranslation } from 'react-i18next';

export interface ConversionOptionsType {
  enable_summarize?: boolean;
  enable_grammar_correction?: boolean;
  enable_llm_reformat?: boolean; // Corresponds to "智能处理换行与段落"
  image_mode_preference?: 'embedded' | 'referenced'; // Corresponds to "图片存储方式"
  enable_image_recognition?: boolean; // Corresponds to "识别图片中的文字 (OCR)"
  enable_image_description?: boolean; // Corresponds to "为图片生成描述文本"
  image_description_style?: 'concise' | 'detailed'; // Corresponds to "描述丰富度"
  image_description_attachment_mode?: 'keep_image' | 'replace_image'; // Corresponds to "描述文字附加方式"
  enable_charts_to_mermaid?: boolean; // Corresponds to "尝试将图表类图片转换为 Mermaid 代码"
  // processing_mode is removed from UI based on new design
}

interface ConversionOptionsProps {
  options: ConversionOptionsType;
  onChange: (options: ConversionOptionsType) => void;
  disabled?: boolean;
  isLoggedIn?: boolean;
}

const ConversionOptions: React.FC<ConversionOptionsProps> = ({ options, onChange, disabled = false, isLoggedIn = true }) => {
  const { t } = useTranslation(['common', 'convert']);
  const isFreeTier = !isLoggedIn;
  const [hoveredOption, setHoveredOption] = useState<string | null>(null);

  const SvgAnimations: { [key: string]: string | import('next/image').StaticImageData } = {
    enable_summarize: SummarizeAnimation,
    enable_grammar_correction: GrammarCorrectionAnimation,
    enable_llm_reformat: LlmReformatAnimation,
    enable_image_recognition: ImageRecognitionAnimation,
    enable_image_description: ImageDescriptionAnimation,
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    if (isFreeTier && (name === 'enable_summarize' || name === 'enable_grammar_correction' || name === 'enable_llm_reformat' || name === 'enable_image_recognition' || name === 'enable_image_description')) {
      // Potentially disallow for free tier or handle based on specific feature tiering
      return;
    }
    onChange({
      ...options,
      [name]: checked,
    });
  };

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const optionName = name as keyof ConversionOptionsType;

    if (isFreeTier && optionName === 'image_mode_preference' && value !== 'referenced') {
        onChange({ ...options, [optionName]: 'referenced' });
        return;
    }
    if (isFreeTier && optionName === 'image_description_style'){
        return;
    }
    // Ensure value matches the type expected by ConversionOptionsType for these specific radio button groups
    if (optionName === 'image_mode_preference' && (value === 'embedded' || value === 'referenced')) {
      onChange({ ...options, [optionName]: value });
    } else if (optionName === 'image_description_style' && (value === 'concise' || value === 'detailed')) {
      onChange({ ...options, [optionName]: value });
    } else if (optionName === 'image_description_attachment_mode' && (value === 'keep_image' || value === 'replace_image')) {
      onChange({ ...options, [optionName]: value });
    }
  };

  // Determine effective options based on login state and new structure
  const effectiveOptions = React.useMemo(() => {
    // Set a default for image_description_attachment_mode if not already present in options
    const baseOptions = {
      ...options,
      image_description_attachment_mode: options.image_description_attachment_mode || 'keep_image',
    };

    if (isFreeTier) {
      return {
        ...baseOptions, // Use baseOptions which includes the default
        enable_summarize: false,
        enable_grammar_correction: false,
        enable_llm_reformat: false,
        image_mode_preference: 'referenced' as const,
        enable_image_recognition: false,
        enable_image_description: false,
        image_description_style: 'concise' as const,
        // image_description_attachment_mode is now correctly defaulted in baseOptions for free tier as well
        enable_charts_to_mermaid: false,
        // processing_mode is effectively removed by not being in effectiveOptions for free tier
      };
    }
    // For paid tier, ensure processing_mode is not used and use baseOptions for defaults
    return { ...baseOptions, processing_mode: undefined };
  }, [options, isFreeTier]);

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        {/* <h3 className="text-lg font-medium leading-6 text-gray-900">{t('convert:options')}</h3> */}

        <div className="space-y-8">
          {/* Section 1: 图片处理设置 */}
          <Disclosure as="div" defaultOpen>
            {({ open }) => (
              <>
                <Disclosure.Button className="flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75">
                  <span>{t('convert:imageSettingsTitle', '文档内部图片设置')}</span>
                  <svg className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </Disclosure.Button>
                <Disclosure.Panel className="px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700">
                  {/* 图片存储方式 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">{t('convert:imageStorageMethod', '图片存储方式:')}</label>
                    <div className="mt-2 space-y-4"> {/* Changed to space-y-4 for vertical spacing */}
                      <div>
                        <div className="flex items-center">
                          <input
                            id="image_mode_referenced"
                            name="image_mode_preference"
                            type="radio"
                            value="referenced"
                            checked={effectiveOptions.image_mode_preference === 'referenced'}
                            onChange={handleRadioChange}
                            disabled={disabled || isFreeTier}
                            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                          />
                          <label htmlFor="image_mode_referenced" className={`ml-2 block text-sm font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                            {t('convert:imageModeReferenced', '引用图片')}
                          </label>
                        </div>
                        <p className={`ml-6 text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                          {isFreeTier
                            ? t('convert:imageExportModeDescReferencedFree', '图片链接将被保留。')
                            : t('convert:imageExportModeDescReferencedPaid', '图片单独存储，Markdown 文件较小，但需要复制文件及图片目录才能完整查看。')}
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center">
                          <input
                            id="image_mode_embedded"
                            name="image_mode_preference"
                            type="radio"
                            value="embedded"
                            checked={effectiveOptions.image_mode_preference === 'embedded'}
                            onChange={handleRadioChange}
                            disabled={disabled || isFreeTier}
                            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                          />
                          <label htmlFor="image_mode_embedded" className={`ml-2 block text-sm font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                            {t('convert:imageModeEmbedded', '内嵌图片')}
                          </label>
                        </div>
                        <p className={`ml-6 text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                          {isFreeTier
                            ? t('convert:imageExportModeDescEmbeddedFree', '此为高级功能。')
                            : t('convert:imageExportModeDescEmbeddedPaid', '图片数据将直接嵌入 Markdown 文件，文件较大，但所有内容都在一个文档中，方便分享和离线查看。')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 识别图片中的文字 (OCR) */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable_image_recognition"
                        name="enable_image_recognition"
                        type="checkbox"
                        checked={effectiveOptions.enable_image_recognition || false}
                        onChange={handleCheckboxChange}
                        disabled={disabled || isFreeTier}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="ml-3 text-sm relative">
                      <label htmlFor="enable_image_recognition" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                        {t('convert:enableImageRecognition', '识别图片中的文字')}
                        <span
                          className="ml-2 text-blue-500 cursor-pointer"
                          onMouseEnter={() => setHoveredOption('enable_image_recognition')}
                          onMouseLeave={() => setHoveredOption(null)}
                        >
                          (?)
                        </span>
                      </label>
                      {hoveredOption === 'enable_image_recognition' && SvgAnimations['enable_image_recognition'] && (
                        <div className="absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none">
                          <Image src={SvgAnimations['enable_image_recognition']} alt="Image Recognition Animation" width={180} height={120} />
                        </div>
                      )}
                       <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                        {isFreeTier ? t('convert:enableImageRecognitionDescFree', '此为高级功能。') : t('convert:enableImageRecognitionDesc', '智能识别图片中的文本、代码和列表，并将其转换为保留结构的 Markdown 格式。')}
                      </p>
                    </div>
                  </div>

                  {/* 为图片生成描述文本 */}
                  <div>
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="enable_image_description"
                          name="enable_image_description"
                          type="checkbox"
                          checked={effectiveOptions.enable_image_description || false}
                          onChange={handleCheckboxChange}
                          disabled={disabled || isFreeTier}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                        />
                      </div>
                      <div className="ml-3 text-sm relative">
                        <label htmlFor="enable_image_description" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                          {t('convert:enableImageDescription', '为图片生成描述性文字')}
                          <span
                            className="ml-2 text-blue-500 cursor-pointer"
                            onMouseEnter={() => setHoveredOption('enable_image_description')}
                            onMouseLeave={() => setHoveredOption(null)}
                          >
                            (?)
                          </span>
                        </label>
                        {hoveredOption === 'enable_image_description' && SvgAnimations['enable_image_description'] && (
                          <div className="absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none">
                            <Image src={SvgAnimations['enable_image_description']} alt="Image Description Animation" width={180} height={120} />
                          </div>
                        )}
                         <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                            {isFreeTier ? t('convert:enableImageDescriptionDescFree', '此为高级功能。') : t('convert:enableImageDescriptionDesc', '为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片。')}
                        </p>
                      </div>
                    </div>
                    {/* 条件渲染：描述文字附加方式 和 描述丰富度 */}
                    {effectiveOptions.enable_image_description && (
                      <div className="mt-3 pl-8 space-y-4">
                        {/* 描述文字附加方式 */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700">{t('convert:imageDescriptionAttachmentMode', '描述文字附加方式:')}</label>
                          <div className="mt-1 flex space-x-6">
                            <div className="flex items-center">
                              <input
                                id="attachment_mode_keep_image"
                                name="image_description_attachment_mode"
                                type="radio"
                                value="keep_image"
                                checked={effectiveOptions.image_description_attachment_mode === 'keep_image'}
                                onChange={handleRadioChange}
                                disabled={disabled || isFreeTier}
                                className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                              />
                              <label htmlFor="attachment_mode_keep_image" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                                {t('convert:attachmentModeKeepImage', '保留图片')}
                              </label>
                            </div>
                            <div className="flex items-center">
                              <input
                                id="attachment_mode_replace_image"
                                name="image_description_attachment_mode"
                                type="radio"
                                value="replace_image"
                                checked={effectiveOptions.image_description_attachment_mode === 'replace_image'}
                                onChange={handleRadioChange}
                                disabled={disabled || isFreeTier}
                                className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                              />
                              <label htmlFor="attachment_mode_replace_image" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                                {t('convert:attachmentModeReplaceImage', '替换图片')}
                              </label>
                            </div>
                          </div>
                        </div>

                        {/* 描述丰富度 */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700">{t('convert:imageDescriptionStyle', '描述丰富度:')}</label>
                          <div className="mt-1 flex space-x-6">
                              <div className="flex items-center">
                                  <input
                                  id="desc_style_concise"
                                  name="image_description_style"
                                  type="radio"
                                  value="concise"
                                  checked={effectiveOptions.image_description_style === 'concise'}
                                  onChange={handleRadioChange}
                                  disabled={disabled || isFreeTier}
                                  className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                                  />
                                  <label htmlFor="desc_style_concise" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                                  {t('convert:styleConcise', '简略')}
                                  </label>
                              </div>
                              <div className="flex items-center">
                                  <input
                                  id="desc_style_detailed"
                                  name="image_description_style"
                                  type="radio"
                                  value="detailed"
                                  checked={effectiveOptions.image_description_style === 'detailed'}
                                  onChange={handleRadioChange}
                                  disabled={disabled || isFreeTier}
                                  className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 disabled:bg-gray-200 disabled:cursor-not-allowed"
                                  />
                                  <label htmlFor="desc_style_detailed" className={`ml-2 block text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                                  {t('convert:styleDetailed', '丰富')}
                                  </label>
                              </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 尝试将图表类图片转换为 Mermaid 代码 */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable_charts_to_mermaid"
                        name="enable_charts_to_mermaid"
                        type="checkbox"
                        checked={effectiveOptions.enable_charts_to_mermaid || false}
                        onChange={handleCheckboxChange}
                        disabled={disabled || isFreeTier} // This is a new feature, likely premium
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="enable_charts_to_mermaid" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                        {t('convert:enableChartsToMermaid', '尝试将图表类图片转换为 Mermaid 代码')}
                      </label>
                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                        {isFreeTier ? t('convert:enableChartsToMermaidDescFree', '此为高级功能。') : t('convert:enableChartsToMermaidDesc', '将图片中的流程图、序列图等转换为可编辑的 Mermaid 文本。')}
                      </p>
                    </div>
                  </div>
                </Disclosure.Panel>
              </>
            )}
          </Disclosure>

          {/* Section 2: 文本与排版优化 */}
          <Disclosure as="div" defaultOpen>
            {({ open }) => (
              <>
                <Disclosure.Button className="flex justify-between w-full px-4 py-3 text-sm font-medium text-left text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-blue-500 focus-visible:ring-opacity-75">
                  <span>{t('convert:textSettingsTitle', '文本与排版优化')}</span>
                   <svg className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </Disclosure.Button>
                <Disclosure.Panel className="px-4 pt-4 pb-2 space-y-6 text-sm text-gray-700">
                  {/* 智能处理换行与段落 */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable_llm_reformat"
                        name="enable_llm_reformat"
                        type="checkbox"
                        checked={effectiveOptions.enable_llm_reformat || false}
                        onChange={handleCheckboxChange}
                        disabled={disabled || isFreeTier}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="ml-3 text-sm relative">
                      <label htmlFor="enable_llm_reformat" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                        {t('convert:enableLLMReformat', '智能处理换行与段落')}
                        <span
                          className="ml-2 text-blue-500 cursor-pointer"
                          onMouseEnter={() => setHoveredOption('enable_llm_reformat')}
                          onMouseLeave={() => setHoveredOption(null)}
                        >
                          (?)
                        </span>
                      </label>
                      {hoveredOption === 'enable_llm_reformat' && SvgAnimations['enable_llm_reformat'] && (
                        <div className="absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none">
                          <Image src={SvgAnimations['enable_llm_reformat']} alt="LLM Reformat Animation" width={180} height={120} />
                        </div>
                      )}
                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                        {isFreeTier ? t('convert:enableLLMReformatDescFree', '此为高级功能。') : t('convert:enableLLMReformatDesc', '利用大语言模型优化文本的换行和分段，提升阅读体验。')}
                      </p>
                    </div>
                  </div>

                  {/* 自动校对语法与错别字 */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable_grammar_correction"
                        name="enable_grammar_correction"
                        type="checkbox"
                        checked={effectiveOptions.enable_grammar_correction || false}
                        onChange={handleCheckboxChange}
                        disabled={disabled || isFreeTier}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="ml-3 text-sm relative">
                      <label htmlFor="enable_grammar_correction" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                        {t('convert:enableGrammarCorrection', '自动校对语法与错别字')}
                        <span
                          className="ml-2 text-blue-500 cursor-pointer"
                          onMouseEnter={() => setHoveredOption('enable_grammar_correction')}
                          onMouseLeave={() => setHoveredOption(null)}
                        >
                          (?)
                        </span>
                      </label>
                      {hoveredOption === 'enable_grammar_correction' && SvgAnimations['enable_grammar_correction'] && (
                        <div className="absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none">
                          <Image src={SvgAnimations['enable_grammar_correction']} alt="Grammar Correction Animation" width={180} height={120} />
                        </div>
                      )}
                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                        {isFreeTier ? t('convert:enableGrammarCorrectionDescFree', '此为高级功能。') : t('convert:enableGrammarCorrectionDesc', '自动检测并修正文档中的语法错误和拼写错误。')}
                      </p>
                    </div>
                  </div>

                  {/* 生成文档摘要 */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="enable_summarize"
                        name="enable_summarize"
                        type="checkbox"
                        checked={effectiveOptions.enable_summarize || false}
                        onChange={handleCheckboxChange}
                        disabled={disabled || isFreeTier}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:bg-gray-200 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="ml-3 text-sm relative">
                      <label htmlFor="enable_summarize" className={`font-medium ${isFreeTier ? 'text-gray-400' : 'text-gray-700'}`}>
                        {t('convert:enableSummarize', '生成文档摘要')}
                        <span
                          className="ml-2 text-blue-500 cursor-pointer"
                          onMouseEnter={() => setHoveredOption('enable_summarize')}
                          onMouseLeave={() => setHoveredOption(null)}
                        >
                          (?)
                        </span>
                      </label>
                      {hoveredOption === 'enable_summarize' && SvgAnimations['enable_summarize'] && (
                        <div className="absolute z-50 top-0 right-0 mt-6 mr-4 w-48 bg-white border border-gray-300 rounded-md shadow-lg p-2 pointer-events-none">
                          <Image src={SvgAnimations['enable_summarize']} alt="Summarize Animation" width={180} height={120} />
                        </div>
                      )}
                      <p className={`text-xs ${isFreeTier ? 'text-gray-400' : 'text-gray-500'}`}>
                        {isFreeTier ? t('convert:enableSummarizeDescFree', '此为高级功能。') : t('convert:enableSummarizeDesc', '自动为转换后的文档生成内容摘要。')}
                      </p>
                    </div>
                  </div>
                </Disclosure.Panel>
              </>
            )}
          </Disclosure>
        </div>
      </div>
    </div>
  );
};

export default ConversionOptions;
