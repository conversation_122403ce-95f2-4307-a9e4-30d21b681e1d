{"name": "src", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "dompurify": "^3.2.5", "firebase": "^11.6.1", "i18next": "^25.1.1", "next": "15.3.2", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/dompurify": "^3.0.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}