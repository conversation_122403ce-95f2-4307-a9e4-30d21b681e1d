{
  "compilerOptions": {
    "target": "ES2017",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": "../", // Set base URL to project root
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "src/*"
      ] // Paths are relative to baseUrl
    }
  },
  "include": [
    "../.next/types/**/*.ts", // Adjusted path for next-env.d.ts
    "../app/**/*.ts", // Files within src/
    "../app/**/*.tsx", // Files within src/
    "../components/**/*.ts", // Files within root app/
    "../components/**/*.tsx",
    "../next-env.d.ts", // Files within root components/
    "./**/*.ts",
    "./**/*.tsx" // Adjusted path for .next/types
    ,
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "../node_modules"
  ] // Ensure both node_modules are excluded
}
