'use client';

import React from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import { useTranslation } from 'react-i18next';
import createI18nInstance from '../../i18n.js';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function PricingPage() {
  const { t, i18n } = useTranslation(['pricing', 'common']);
  const i18nInstance = createI18nInstance(i18n.language, ['pricing', 'common']);
  const [frequency] = React.useState('monthly');

  const tiers = [
    {
      name: i18nInstance.t('pricing:free'),
      id: 'tier-free',
      href: '/convert',
      price: { monthly: '$0', annually: '$0' }, // Prices are usually not translated or handled differently
      description: 'Perfect for occasional use and small documents.', // This could be translated if needed
      features: [
        'Convert up to 5 files per day',
        'Max file size: 5MB',
        'Basic Markdown conversion',
        'Standard support',
      ], // Features could also be translated
      mostPopular: false,
    },
    {
      name: i18nInstance.t('pricing:pro'),
      id: 'tier-pro',
      href: '#', // Replace with actual link or handle with router
      price: { monthly: '$15', annually: '$144' },
      description: 'Ideal for regular users and content creators.',
      features: [
        'Unlimited conversions',
        'Max file size: 20MB',
        'Advanced Markdown options',
        'Batch processing',
        'Priority support',
        'No ads',
      ],
      mostPopular: true,
    },
    {
      name: i18nInstance.t('pricing:enterprise'),
      id: 'tier-enterprise',
      href: '#', // Replace with actual link or handle with router
      price: { monthly: '$39', annually: '$384' },
      description: 'For teams and professional content creators.',
      features: [
        'Everything in Pro',
        'Max file size: 50MB',
        'API access',
        'Custom conversion options',
        'Team management',
        'Dedicated support',
        'Advanced analytics',
      ],
      mostPopular: false,
    },
  ];


  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-base font-semibold leading-7 text-blue-600">{t('pricing:title')}</h1>
          <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            {t('pricing:subtitle')}
          </p>
        </div>
        <p className="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600">
          {t('pricing:description')}
        </p>
        
        <div className="mt-16 flex justify-center">
          <div className="grid grid-cols-1 gap-y-8 sm:grid-cols-2 sm:gap-x-8 lg:grid-cols-3">
            {tiers.map((tier) => (
              <div
                key={tier.id}
                className={classNames(
                  tier.mostPopular ? 'ring-2 ring-blue-600' : 'ring-1 ring-gray-200',
                  'rounded-3xl p-8 xl:p-10'
                )}
              >
                <div className="flex items-center justify-between gap-x-4">
                  <h2 id={tier.id} className="text-lg font-semibold leading-8 text-gray-900">
                    {tier.name}
                  </h2>
                  {tier.mostPopular ? (
                    <p className="rounded-full bg-blue-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-blue-600">
                      {t('pricing:mostPopular')}
                    </p>
                  ) : null}
                </div>
                <p className="mt-4 text-sm leading-6 text-gray-600">{tier.description}</p>
                <p className="mt-6 flex items-baseline gap-x-1">
                  <span className="text-4xl font-bold tracking-tight text-gray-900">
                    {frequency === 'annually' ? tier.price.annually : tier.price.monthly}
                  </span>
                  <span className="text-sm font-semibold leading-6 text-gray-600">
                    {frequency === 'annually' ? t('pricing:yearly') : t('pricing:monthly')}
                  </span>
                </p>
                <a
                  href={tier.href}
                  aria-describedby={tier.id}
                  className={classNames(
                    tier.mostPopular
                      ? 'bg-blue-600 text-white shadow-sm hover:bg-blue-500'
                      : 'text-blue-600 ring-1 ring-inset ring-blue-200 hover:ring-blue-300',
                    'mt-6 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                  )}
                >
                  {tier.name === i18nInstance.t('pricing:free') ? t('pricing:getStarted') : t('pricing:subscribe')}
                </a>
                <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <CheckIcon className="h-6 w-5 flex-none text-blue-600" aria-hidden="true" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
