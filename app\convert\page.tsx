'use client';

import React, { useState, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import FileUploader from '../../components/conversion/FileUploader';
import FileListManager, { FileStatus } from '../../components/conversion/FileListManager';
import ConversionOptions, { ConversionOptionsType } from '../../components/conversion/ConversionOptions';
import ConversionProgress from '../../components/conversion/ConversionProgress';
import ResultDisplay from '../../components/conversion/ResultDisplay';
import MarkdownPreviewer from '../../components/conversion/MarkdownPreviewer';

interface FileItem {
  file: File;
  id: string;
  progress: number;
  status: FileStatus;
  error?: string;
  resultUrl?: string;
  markdownContent?: string;
}

export default function ConvertPage() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [selectedPreviewId, setSelectedPreviewId] = useState<string | null>(null);
  const [conversionOptions, setConversionOptions] = useState<ConversionOptionsType>({
    enable_summarize: false,
    enable_grammar_correction: false,
    enable_llm_reformat: false,
    image_mode_preference: 'embedded',
    processing_mode: '',
    enable_image_recognition: false,
    enable_image_description: false,
    image_description_style: 'concise',
  });

  const handleFilesSelected = useCallback((selectedFiles: File[]) => {
    const newFileItems = selectedFiles.map(file => ({
      file,
      id: uuidv4(),
      progress: 0,
      status: 'pending' as FileStatus,
    }));
    
    setFiles(prevFiles => [...prevFiles, ...newFileItems]);
  }, []);

  const handleRemoveFile = useCallback((id: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.id !== id));
    if (selectedPreviewId === id) {
      setSelectedPreviewId(null);
    }
  }, [selectedPreviewId]);

  const handleRemoveAllFiles = useCallback(() => {
    setFiles([]);
    setSelectedPreviewId(null);
  }, []);

  const handleStartConversion = useCallback(() => {
    if (files.length === 0 || isConverting) return;
    
    setIsConverting(true);
    setOverallProgress(0);
    
    // Update file statuses to 'uploading'
    setFiles(prevFiles => 
      prevFiles.map(file => ({
        ...file,
        status: file.status === 'pending' || file.status === 'failed' ? 'uploading' : file.status,
        progress: file.status === 'pending' || file.status === 'failed' ? 5 : file.progress,
      }))
    );
    
    // Simulate file conversion process
    const pendingFiles = files.filter(file => 
      file.status === 'pending' || file.status === 'uploading' || file.status === 'failed'
    );
    
    let completedCount = 0;
    
    pendingFiles.forEach((fileItem, index) => {
      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setFiles(prevFiles => {
          const fileIndex = prevFiles.findIndex(f => f.id === fileItem.id);
          if (fileIndex === -1) {
            clearInterval(uploadInterval);
            return prevFiles;
          }
          
          const file = prevFiles[fileIndex];
          if (file.progress >= 100) {
            clearInterval(uploadInterval);
            return prevFiles;
          }
          
          const newProgress = Math.min(file.progress + 10, 100);
          const newStatus = newProgress === 100 ? 'processing' : file.status;
          
          const updatedFiles = [...prevFiles];
          updatedFiles[fileIndex] = {
            ...file,
            progress: newProgress,
            status: newStatus,
          };
          
          // Update overall progress
          const totalProgress = updatedFiles.reduce((sum, f) => sum + f.progress, 0);
          setOverallProgress(Math.floor(totalProgress / updatedFiles.length));
          
          return updatedFiles;
        });
      }, 500 + index * 200);
      
      // Simulate processing and completion
      setTimeout(() => {
        clearInterval(uploadInterval);
        
        // Simulate success or failure (90% success rate)
        const isSuccess = Math.random() < 0.9;
        
        setFiles(prevFiles => {
          const fileIndex = prevFiles.findIndex(f => f.id === fileItem.id);
          if (fileIndex === -1) return prevFiles;
          
          const updatedFiles = [...prevFiles];
          updatedFiles[fileIndex] = {
            ...updatedFiles[fileIndex],
            progress: 100,
            status: isSuccess ? 'completed' : 'failed',
            error: isSuccess ? undefined : 'Conversion failed. Please try again.',
            resultUrl: isSuccess ? `#${fileItem.id}` : undefined,
            markdownContent: isSuccess 
              ? `# ${fileItem.file.name}\n\nThis is a sample Markdown content for ${fileItem.file.name}.\n\n## Features\n\n- Feature 1\n- Feature 2\n- Feature 3\n\n## Code Example\n\n\`\`\`javascript\nconst hello = 'world';\nconsole.log(hello);\n\`\`\``
              : undefined,
          };
          
          completedCount++;
          if (completedCount === pendingFiles.length) {
            setIsConverting(false);
          }
          
          // Update overall progress
          const totalProgress = updatedFiles.reduce((sum, f) => sum + f.progress, 0);
          setOverallProgress(Math.floor(totalProgress / updatedFiles.length));
          
          return updatedFiles;
        });
      }, 3000 + index * 1000);
    });
  }, [files, isConverting]);

  const handleDownloadFile = useCallback((id: string) => {
    const fileItem = files.find(file => file.id === id);
    if (!fileItem || !fileItem.markdownContent) return;
    
    // Create a blob from the markdown content
    const blob = new Blob([fileItem.markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    
    // Create a temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileItem.file.name.split('.')[0]}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL
    URL.revokeObjectURL(url);
  }, [files]);

  const handleDownloadAll = useCallback(() => {
    // In a real implementation, this would call a backend API to create a ZIP file
    // For this example, we'll just download the first file
    const completedFile = files.find(file => file.status === 'completed');
    if (completedFile) {
      handleDownloadFile(completedFile.id);
    }
  }, [files, handleDownloadFile]);

  const handlePreviewFile = useCallback((id: string) => {
    setSelectedPreviewId(id);
  }, []);

  const completedFiles = files.filter(file => file.status === 'completed').length;
  const selectedPreviewFile = selectedPreviewId 
    ? files.find(file => file.id === selectedPreviewId) 
    : null;

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">Convert to Markdown</h1>
        <p className="mt-2 text-sm text-gray-500">
          Upload your documents and convert them to clean, well-formatted Markdown.
        </p>
        
        <div className="mt-6">
          <FileUploader onFilesSelected={handleFilesSelected} />
          
          <FileListManager 
            files={files}
            onRemoveFile={handleRemoveFile}
            onRemoveAllFiles={handleRemoveAllFiles}
            onDownloadFile={handleDownloadFile}
            onRetryFile={(id) => {
              setFiles(prevFiles => 
                prevFiles.map(file => 
                  file.id === id ? { ...file, status: 'pending', progress: 0, error: undefined } : file
                )
              );
            }}
          />
          
          <ConversionOptions 
            options={conversionOptions}
            onChange={setConversionOptions}
          />
          
          {files.length > 0 && !isConverting && (
            <div className="mt-6">
              <button
                type="button"
                onClick={handleStartConversion}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Start Conversion
              </button>
            </div>
          )}
          
          <ConversionProgress 
            isConverting={isConverting}
            progress={overallProgress}
            totalFiles={files.length}
            completedFiles={completedFiles}
          />
          
          <ResultDisplay 
            results={files.map(file => ({
              id: file.id,
              fileName: file.file.name,
              status: file.status,
              resultUrl: file.resultUrl,
            }))}
            onDownloadFile={handleDownloadFile}
            onDownloadAll={handleDownloadAll}
          />
          
          {selectedPreviewFile && selectedPreviewFile.markdownContent && (
            <MarkdownPreviewer 
              markdownContent={selectedPreviewFile.markdownContent}
              fileName={selectedPreviewFile.file.name}
            />
          )}
        </div>
      </div>
    </div>
  );
}
