'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { getSupabaseClient } from '../../lib/supabaseClient';
import { AuthError, SupabaseClient } from '@supabase/supabase-js';
import { useTranslation } from 'react-i18next';
import createI18nInstance from '../../i18n.js';

export default function RegisterPage() {
  const { t, i18n } = useTranslation(['auth', 'common']);
  const i18nInstance = createI18nInstance(i18n.language, ['auth', 'common']);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState(''); // Added confirm password
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null); // For success messages
  const router = useRouter();
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);

  useEffect(() => {
    setSupabase(getSupabaseClient());
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setMessage(null);

    if (!supabase) {
      setError(i18nInstance.t('auth:supabaseNotInitialized', { ns: 'auth', defaultValue: "Supabase client is not initialized."}));
      setIsLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError(i18nInstance.t('auth:passwordsDoNotMatch', { ns: 'auth', defaultValue: "Passwords do not match."}));
      setIsLoading(false);
      return;
    }

    try {
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          // Ensure emailRedirectTo is an absolute URL
          emailRedirectTo: `${window.location.origin}/login`,
        }
      });

      if (signUpError) {
        throw signUpError;
      }

      if (data.user && !data.session) {
        setMessage(i18nInstance.t('auth:registrationSuccessConfirmEmail', { ns: 'auth', defaultValue: 'Registration successful! Please check your email to confirm your account.'}));
      } else if (data.user && data.session) {
        setMessage(i18nInstance.t('auth:registrationSuccessLoggedIn', { ns: 'auth', defaultValue: 'Registration successful! You are now logged in.'}));
        // router.push('/convert'); // Optionally redirect
      } else {
         setMessage(i18nInstance.t('auth:registrationSuccessConfirmLink', { ns: 'auth', defaultValue: 'Registration successful! Please check your email for a confirmation link.'}));
      }
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      // Redirect after a short delay to allow the user to see the message.
      setTimeout(() => {
        router.push('/login');
      }, 3000); // 3 seconds delay

    } catch (err) {
      if (err instanceof AuthError) {
        setError(err.message);
      } else {
        setError(i18nInstance.t('auth:registrationError', { ns: 'auth', defaultValue: 'An unexpected error occurred during registration.'}));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
          {t('auth:registerTitle')}
        </h2>
      </div>

      <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
        <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{error}</h3>
                  </div>
                </div>
              </div>
            )}
            {message && (
              <div className="rounded-md bg-green-50 p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">{message}</h3>
                  </div>
                </div>
              </div>
            )}
            <div>
              <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                {t('auth:emailLabel')}
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
                {t('auth:passwordLabel')}
              </label>
              <div className="mt-2">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div>
              <label htmlFor="confirm-password" className="block text-sm font-medium leading-6 text-gray-900">
                {t('auth:confirmPasswordLabel')}
              </label>
              <div className="mt-2">
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? t('auth:creatingAccount', { ns: 'auth', defaultValue: 'Creating account...'}) : t('auth:registerButton')}
              </button>
            </div>
          </form>

          <p className="mt-10 text-center text-sm text-gray-500">
            {t('auth:hasAccount')}{' '}
            <Link href="/login" className="font-semibold leading-6 text-blue-600 hover:text-blue-500">
              {t('auth:signInLink')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
