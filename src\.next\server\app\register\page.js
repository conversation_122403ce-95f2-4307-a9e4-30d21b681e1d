(()=>{var e={};e.id=454,e.ids=[454],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3485:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>u});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let u={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8874)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\register\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6833:(e,t,s)=>{Promise.resolve().then(s.bind(s,8874))},7319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(687),a=s(3210),i=s(5814),n=s.n(i),o=s(6189);s(7163);var l=s(4199),u=s(6457),d=s(8262);function c(){let{t:e,i18n:t}=(0,u.Bd)(["auth","common"]),s=(0,d.A)(t.language,["auth","common"]),[i,c]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),[x,h]=(0,a.useState)(""),[g,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(null),[w,y]=(0,a.useState)(null),j=(0,o.useRouter)(),[k,N]=(0,a.useState)(null),q=async e=>{if(e.preventDefault(),f(!0),v(null),y(null),!k){v(s.t("auth:supabaseNotInitialized",{ns:"auth",defaultValue:"Supabase client is not initialized."})),f(!1);return}if(m!==x){v(s.t("auth:passwordsDoNotMatch",{ns:"auth",defaultValue:"Passwords do not match."})),f(!1);return}try{let{data:e,error:t}=await k.auth.signUp({email:i,password:m,options:{emailRedirectTo:`${window.location.origin}/login`}});if(t)throw t;e.user&&!e.session?y(s.t("auth:registrationSuccessConfirmEmail",{ns:"auth",defaultValue:"Registration successful! Please check your email to confirm your account."})):e.user&&e.session?y(s.t("auth:registrationSuccessLoggedIn",{ns:"auth",defaultValue:"Registration successful! You are now logged in."})):y(s.t("auth:registrationSuccessConfirmLink",{ns:"auth",defaultValue:"Registration successful! Please check your email for a confirmation link."})),c(""),p(""),h(""),setTimeout(()=>{j.push("/login")},3e3)}catch(e){e instanceof l.lR?v(e.message):v(s.t("auth:registrationError",{ns:"auth",defaultValue:"An unexpected error occurred during registration."}))}finally{f(!1)}};return(0,r.jsxs)("div",{className:"flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsx)("h2",{className:"mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900",children:e("auth:registerTitle")})}),(0,r.jsx)("div",{className:"mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]",children:(0,r.jsxs)("div",{className:"bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12",children:[(0,r.jsxs)("form",{className:"space-y-6",onSubmit:q,children:[b&&(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:b})})})}),w&&(0,r.jsx)("div",{className:"rounded-md bg-green-50 p-4",children:(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800",children:w})})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:emailLabel")}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:e=>c(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:passwordLabel")}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:m,onChange:e=>p(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:confirmPasswordLabel")}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,value:x,onChange:e=>h(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:g,className:"flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:g?e("auth:creatingAccount",{ns:"auth",defaultValue:"Creating account..."}):e("auth:registerButton")})})]}),(0,r.jsxs)("p",{className:"mt-10 text-center text-sm text-gray-500",children:[e("auth:hasAccount")," ",(0,r.jsx)(n(),{href:"/login",className:"font-semibold leading-6 text-blue-600 hover:text-blue-500",children:e("auth:signInLink")})]})]})})]})}},7910:e=>{"use strict";e.exports=require("stream")},8874:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\github\\\\alltomarkdown_frontend\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\github\\alltomarkdown_frontend\\src\\app\\register\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9985:(e,t,s)=>{Promise.resolve().then(s.bind(s,7319))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,373,658,522],()=>s(3485));module.exports=r})();