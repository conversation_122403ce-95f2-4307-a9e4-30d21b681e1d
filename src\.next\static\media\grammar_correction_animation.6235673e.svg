<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Document with Errors -->
  <g id="originalDocWithErrors">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">With Errors</text>

    <!-- Text lines with error indicators -->
    <line x1="20" y1="35" x2="80" y2="35" stroke="#757575" stroke-width="2"/>

    <line x1="20" y1="45" x2="80" y2="45" stroke="#757575" stroke-width="2"/>
    <path d="M25,48 q6,-2 12,0 q6,2 12,0" stroke="#FF5722" fill="none" stroke-width="1.5"/>

    <line x1="20" y1="55" x2="75" y2="55" stroke="#757575" stroke-width="2"/>

    <line x1="20" y1="65" x2="70" y2="65" stroke="#757575" stroke-width="2"/>
    <path d="M35,68 q6,-2 12,0 q6,2 12,0" stroke="#FF5722" fill="none" stroke-width="1.5"/>

    <line x1="20" y1="75" x2="80" y2="75" stroke="#757575" stroke-width="2"/>

    <line x1="20" y1="85" x2="65" y2="85" stroke="#757575" stroke-width="2"/>
    <path d="M30,88 q6,-2 12,0 q6,2 12,0" stroke="#FF5722" fill="none" stroke-width="1.5"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; 60,0"
                      dur="2s"
                      repeatCount="indefinite" />
    <animate attributeName="opacity"
             values="1;0"
             dur="2s"
             sleep="1s"
             repeatCount="indefinite" />
  </g>

  <!-- Corrected Document -->
  <g id="correctedDoc" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Corrected</text>

    <!-- Clean corrected text lines -->
    <line x1="20" y1="35" x2="80" y2="35" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="45" x2="80" y2="45" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="55" x2="75" y2="55" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="65" x2="70" y2="65" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="75" x2="80" y2="75" stroke="#4CAF50" stroke-width="2"/>
    <line x1="20" y1="85" x2="65" y2="85" stroke="#4CAF50" stroke-width="2"/>

    <!-- Check marks to indicate corrections -->
    <circle cx="85" cy="45" r="3" fill="#4CAF50"/>
    <path d="M83,45 l2,2 l4,-4" stroke="white" fill="none" stroke-width="1"/>

    <circle cx="85" cy="65" r="3" fill="#4CAF50"/>
    <path d="M83,65 l2,2 l4,-4" stroke="white" fill="none" stroke-width="1"/>

    <circle cx="85" cy="85" r="3" fill="#4CAF50"/>
    <path d="M83,85 l2,2 l4,-4" stroke="white" fill="none" stroke-width="1"/>

    <animateTransform attributeName="transform" type="translate"
                      values="40,0; 100,0"
                      dur="2s"
                      repeatCount="indefinite" />
    <animate attributeName="opacity"
             values="0;1"
             dur="2s"
             begin="1s"
             repeatCount="indefinite" />
  </g>
</svg>
