@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Fix for header shift when body gets padding-right due to scrollbar compensation */
body[style*="padding-right"] .main-header {
  left: 0 !important;
  padding-right: 15px !important; /* Compensate for body's padding-right */
}

/* Language switcher hover styles */
@media (hover: hover) {
  .language-dropdown {
    transition: opacity 150ms ease-out, transform 150ms ease-out, visibility 0s 150ms;
    visibility: hidden;
  }

  .language-dropdown-container:hover .language-dropdown {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
    visibility: visible;
    transition: opacity 150ms ease-out, transform 150ms ease-out, visibility 0s;
  }
}
