import React, { useState, useEffect } from 'react';
import DOMPurify from 'dompurify';

interface MarkdownPreviewerProps {
  markdownContent: string;
  fileName: string;
}

const MarkdownPreviewer: React.FC<MarkdownPreviewerProps> = ({
  markdownContent,
  fileName,
}) => {
  const [htmlContent, setHtmlContent] = useState<string>('');

  useEffect(() => {
    // In a real implementation, we would use a Markdown parser library
    // like marked or markdown-it to convert Markdown to HTML
    // For this example, we'll simulate it with a simple conversion
    const simulateMarkdownToHtml = (markdown: string): string => {
      // This is a very simplified example
      // In a real app, use a proper Markdown parser
      let html = markdown
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/\*\*(.*)\*\*/gm, '<strong>$1</strong>')
        .replace(/\*(.*)\*/gm, '<em>$1</em>')
        .replace(/\n/gm, '<br>');
      
      return html;
    };

    // Convert markdown to HTML
    const rawHtml = simulateMarkdownToHtml(markdownContent);
    
    // Sanitize HTML to prevent XSS attacks
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      USE_PROFILES: { html: true },
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'hr',
        'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
        'em', 'strong', 'del', 'a', 'img', 'table',
        'thead', 'tbody', 'tr', 'th', 'td'
      ],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'title']
    });

    setHtmlContent(sanitizedHtml);
  }, [markdownContent]);

  if (!markdownContent) {
    return null;
  }

  return (
    <div className="mt-6 bg-white shadow sm:rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium leading-6 text-gray-900">
          Preview: {fileName}
        </h3>
        <div className="mt-4 border rounded-md p-4 prose prose-sm max-w-none">
          <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
        </div>
      </div>
    </div>
  );
};

export default MarkdownPreviewer;
