<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Image Only -->
  <g id="imageOnly" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="8" fill="#424242" text-anchor="middle">Image Only</text>

    <!-- Simple image representation: sun and mountain -->
    <circle cx="65" cy="50" r="10" fill="#BDBDBD"/>
    <polygon points="25,85 45,55 55,70 70,45 80,85" fill="#BDBDBD"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="2s" fill="freeze" />
  </g>

  <!-- Image with Description Below -->
  <g id="imageWithDescription" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Image + Description</text>

    <!-- Image part (top) -->
    <g id="imagePart">
      <rect x="20" y="30" width="60" height="40" fill="#F0F0F0" rx="2" ry="2"/>
      <!-- Sun and mountain in smaller space -->
      <circle cx="65" cy="45" r="6" fill="#BDBDBD"/>
      <polygon points="25,65 40,50 50,58 65,45 75,65" fill="#BDBDBD"/>
    </g>

    <!-- Description part (bottom) -->
    <g id="descriptionPart">
      <rect x="20" y="75" width="60" height="25" fill="#F5F5F5" rx="2" ry="2"/>
      <line x1="25" y1="82" x2="75" y2="82" stroke="#4CAF50" stroke-width="1.5"/>
      <line x1="25" y1="87" x2="70" y2="87" stroke="#4CAF50" stroke-width="1.5"/>
      <line x1="25" y1="92" x2="65" y2="92" stroke="#4CAF50" stroke-width="1.5"/>
      <text x="22" y="79" font-size="4" fill="#4CAF50">Description:</text>
    </g>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="0s" dur="2s" fill="freeze" />
  </g>
</svg>
