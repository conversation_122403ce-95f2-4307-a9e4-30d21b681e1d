'use client';

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '../lib/supabaseClient';
import { User, Session } from '@supabase/supabase-js';

// Define a key for storing the local JWT in localStorage
const LOCAL_JWT_KEY = 'local_jwt_token';

// Function to call the backend callback API
async function syncWithBackend(supabaseAccessToken: string): Promise<string | null> {
  try {
    console.log('[Auth Debug] syncWithBackend started with token:', supabaseAccessToken.substring(0, 10) + '...');

    // Ensure this path is correct based on your API setup.
    // If your Next.js app serves the API, it might be relative.
    // If the API is on a different domain, use the full URL.
    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/auth/supabase/callback`
      : '/api/v1/users/auth/supabase/callback';

    console.log('[Auth Debug] Calling backend API at:', apiUrl);

    const startTime = Date.now();
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ access_token: supabaseAccessToken }),
    });

    const duration = Date.now() - startTime;
    console.log(`[Auth Debug] Backend API call completed in ${duration}ms with status:`, response.status);

    if (response.ok) {
      const data = await response.json();
      if (data.access_token) {
        if (typeof window !== 'undefined') {
          localStorage.setItem(LOCAL_JWT_KEY, data.access_token);
        }
        console.log('Local JWT stored successfully.');
        return data.access_token;
      } else {
        console.error('Backend callback response missing access_token:', data);
        if (typeof window !== 'undefined') {
          localStorage.removeItem(LOCAL_JWT_KEY);
        }
        return null;
      }
    } else {
      // Attempt to parse error response, but fallback if it's not JSON
      let errorData;
      try {
        errorData = await response.json();
      } catch { // Removed unused _e parameter
        errorData = { message: response.statusText };
      }
      console.error('Error syncing with backend:', response.status, errorData);
      if (typeof window !== 'undefined') {
        localStorage.removeItem(LOCAL_JWT_KEY);
      }
      return null;
    }
  } catch (error) {
    console.error('Network error or other issue syncing with backend:', error);
    if (typeof window !== 'undefined') {
      localStorage.removeItem(LOCAL_JWT_KEY);
    }
    return null;
  }
}

interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
}

export function useAuth(): AuthState {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
  });

  useEffect(() => {
    console.log('[Auth Debug] useAuth hook initialized');
    const supabase = getSupabaseClient();

    // 记录 Supabase 客户端状态
    console.log('[Auth Debug] Supabase client obtained:', !!supabase);

    // 初始化认证状态 - 避免在初始化过程中调用 getUser()
    const initializeAuth = async () => {
      console.log('useAuth: initializeAuth started');
      try {
        // 1. 获取会话
        const { data: { session: initialSession }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) {
          console.error('useAuth: initializeAuth - error fetching session:', sessionError);
          setAuthState({ user: null, session: null, isLoading: false });
          console.log('useAuth: initializeAuth failed due to session error.');
          return;
        }
        console.log('useAuth: initializeAuth - initialSession fetched:', initialSession ? 'exists' : 'null');

        // 2. 如果会话存在，直接使用会话中的用户信息
        if (initialSession && initialSession.access_token) {
          console.log('useAuth: initializeAuth - session exists');

          // 直接从 session 中获取用户信息
          const user = initialSession.user;

          console.log('useAuth: initializeAuth - user from session:', user ? user.id : 'null');

          if (user) {
            console.log('useAuth: initializeAuth - user exists, syncing with backend...');
            await syncWithBackend(initialSession.access_token);
            console.log('useAuth: initializeAuth - backend sync complete.');
            setAuthState({ user, session: initialSession, isLoading: false });
            console.log('useAuth: initializeAuth completed with session and user.');
          } else {
            console.warn('useAuth: initializeAuth - session exists but user is null in session.');
            if (typeof window !== 'undefined') {
              localStorage.removeItem(LOCAL_JWT_KEY);
            }
            setAuthState({ user: null, session: null, isLoading: false });
            console.log('useAuth: initializeAuth completed with session but no user.');
          }
        } else {
          if (typeof window !== 'undefined') {
            localStorage.removeItem(LOCAL_JWT_KEY);
          }
          setAuthState({ user: null, session: null, isLoading: false });
          console.log('useAuth: initializeAuth completed without session.');
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (typeof window !== 'undefined') {
          localStorage.removeItem(LOCAL_JWT_KEY);
        }
        setAuthState({ user: null, session: null, isLoading: false });
        console.log('useAuth: initializeAuth failed in catch block.');
      }
    };

    // 启动初始化
    initializeAuth();

    // 设置认证状态变化监听 - 避免在回调中调用 getUser()
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        console.log('useAuth: onAuthStateChange event:', event, 'Session:', currentSession ? 'exists' : 'null');

        try {
          if (currentSession && currentSession.access_token) {
            // 直接从 session 中获取用户信息，避免再次调用 API
            const user = currentSession.user;

            console.log('useAuth: onAuthStateChange - user from session:', user ? user.id : 'null');

            if (user) {
              console.log('useAuth: onAuthStateChange - User object exists. Attempting to sync with backend...');
              try {
                await syncWithBackend(currentSession.access_token);
                console.log('useAuth: onAuthStateChange - backend sync complete.');
              } catch (syncError) {
                console.error('useAuth: onAuthStateChange - EXCEPTION syncing with backend:', syncError);
              }
              setAuthState({ user, session: currentSession, isLoading: false });
              console.log('useAuth: onAuthStateChange updated state with session.');
            } else {
              console.warn('useAuth: onAuthStateChange - User is null in session.');
              if (typeof window !== 'undefined') {
                localStorage.removeItem(LOCAL_JWT_KEY);
              }
              setAuthState({ user: null, session: null, isLoading: false });
              console.log('useAuth: onAuthStateChange updated state to no user.');
            }
          } else {
            // 处理登出或会话为空的情况
            if (typeof window !== 'undefined') {
              localStorage.removeItem(LOCAL_JWT_KEY);
            }
            setAuthState({ user: null, session: null, isLoading: false });
            console.log('useAuth: onAuthStateChange updated state without session (SIGNED_OUT or null session).');
          }
        } catch (error) {
          console.error('useAuth: Error in onAuthStateChange callback:', error);
          if (typeof window !== 'undefined') {
            localStorage.removeItem(LOCAL_JWT_KEY);
          }
          setAuthState({ user: null, session: null, isLoading: false });
          console.log('useAuth: onAuthStateChange failed in catch block, set isLoading to false.');
        }
      }
    );

    // 清理订阅
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return authState;
}

// Export a function to get the local JWT for use in API calls
export function getLocalAuthToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(LOCAL_JWT_KEY);
  }
  return null;
}
