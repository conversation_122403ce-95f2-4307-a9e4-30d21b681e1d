<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/cb040c7238448b67.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-f00bc68ecb5ff24e.js"/><script src="/_next/static/chunks/4bd1b696-014f1718dfa463f0.js" async=""></script><script src="/_next/static/chunks/684-819313dbdbbb28b4.js" async=""></script><script src="/_next/static/chunks/main-app-09279af346d29a03.js" async=""></script><script src="/_next/static/chunks/218-e6728dbb1f2b9008.js" async=""></script><script src="/_next/static/chunks/897-19a9bdd2cdb08ddc.js" async=""></script><script src="/_next/static/chunks/985-f18d388702cb1bef.js" async=""></script><script src="/_next/static/chunks/874-43aa1baf760795c9.js" async=""></script><script src="/_next/static/chunks/766-9eba7ddadf798198.js" async=""></script><script src="/_next/static/chunks/app/layout-41b2a25be9e4e126.js" async=""></script><script src="/_next/static/chunks/app/page-62730e1b19ebc181.js" async=""></script><title>All to Markdown - Convert Any Document to AI-Friendly Markdown</title><meta name="description" content="Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Make your content truly understandable by AI models like ChatGPT and Claude."/><meta name="keywords" content="markdown converter, ai friendly format, chatgpt friendly, pdf to markdown, word to markdown, excel to markdown, html to markdown, image to markdown, website to markdown, url to markdown, doc to markdown, docx to markdown, xlsx to markdown, document conversion, ai readable"/><meta property="og:title" content="All to Markdown - Convert Any Document to AI-Friendly Markdown"/><meta property="og:description" content="Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis."/><meta property="og:url" content="https://alltomarkdown.com"/><meta property="og:site_name" content="All to Markdown"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="All to Markdown - Convert Any Document to AI-Friendly Markdown"/><meta name="twitter:description" content="Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="antialiased"><header class="main-header shadow-md fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-white/90"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16 items-center"><div class="flex-shrink-0 flex items-center"><a class="text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors" href="/">All to Markdown</a></div><div class="flex items-center justify-between flex-1 pl-6 md:pl-10 lg:pl-16"> <nav class="hidden md:flex space-x-6 lg:space-x-8 items-baseline"> <a class="px-3 py-2 rounded-md text-sm font-medium transition-colors bg-gray-200 text-blue-700" href="/">Home</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600" href="/convert">Convert</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600" href="/pricing">Pricing</a></nav><div class="flex items-center space-x-3 md:space-x-4 lg:space-x-5"><div class="relative block text-left language-dropdown-container"><div><button id="language-menu-button" type="button" class="inline-flex items-center gap-x-1 rounded-md bg-transparent px-2 py-1.5 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2" aria-label="Language" aria-haspopup="true"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="h-5 w-5"><path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"></path></svg><span class="hidden sm:inline">English</span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon" class="h-5 w-5"><path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"></path></svg></button></div><div class="language-dropdown opacity-0 scale-95 pointer-events-none absolute right-0 z-10 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-blue-600 ring-opacity-5 focus:outline-none overflow-hidden transition-all duration-100 ease-out" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" style="top:100%"><div class="py-1"><button class="hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors" role="menuitem">English</button><button class="hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors" role="menuitem">中文</button></div></div></div><div class="h-10 w-10 rounded-full auth-loading-pulse"></div></div></div></div></div></header><main class="pt-16"> <div class="min-h-screen"><div class="bg-white"><div class="relative isolate px-6 pt-6 lg:px-8"><div class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"><div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-blue-200 to-blue-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"></div></div><div class="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56"><div class="text-center"><h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">Convert Documents to AI-Friendly Markdown</h1><p class="mt-6 text-lg leading-8 text-gray-600">Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.</p><div class="mt-4 flex flex-wrap justify-center gap-2"><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">PDF</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Word (DOC/DOCX)</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Excel (XLS/XLSX)</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">HTML</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Images</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Websites</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">URLs</span><span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">Text Files</span></div><div class="mt-10 flex flex-col items-center justify-center"><a class="rounded-lg bg-blue-600 px-10 py-5 text-xl font-bold text-white shadow-md hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-all hover:shadow-lg hover:translate-y-[-2px] active:translate-y-[1px] w-80 text-center" href="/convert">Start Converting</a><div class="mt-4"><a class="text-xs text-gray-400 hover:text-blue-500 transition-colors" href="/pricing">View Pricing<!-- --> <span aria-hidden="true">→</span></a></div></div></div></div><div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"><div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-200 to-blue-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div></div></div></div><div class="bg-blue-50 py-16 sm:py-24"><div class="mx-auto max-w-7xl px-6 lg:px-8"><div class="mx-auto max-w-2xl lg:text-center"><h2 class="text-base font-semibold leading-7 text-blue-600">AI-Friendly Format</h2><p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Why Markdown is Perfect for AI</p><p class="mt-6 text-lg leading-8 text-gray-600">Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.</p></div><div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl"><dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-3 lg:gap-y-16"><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"></path></svg></div>Clear Structure</dt><dd class="mt-2 text-base leading-7 text-gray-600">Markdown&#x27;s simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.</dd></div><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z"></path></svg></div>Enhanced AI Responses</dt><dd class="mt-2 text-base leading-7 text-gray-600">When feeding Markdown to AI like ChatGPT or Claude, you&#x27;ll get more accurate responses because the AI can better understand context and relationships in your content.</dd></div><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"></path></svg></div>No Formatting Noise</dt><dd class="mt-2 text-base leading-7 text-gray-600">Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.</dd></div></dl></div></div></div><div class="bg-white py-24 sm:py-32"><div class="mx-auto max-w-7xl px-6 lg:px-8"><div class="mx-auto max-w-2xl lg:text-center"><h2 class="text-base font-semibold leading-7 text-blue-600">Convert Any Format</h2><p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Convert any document format to clean Markdown</p><p class="mt-6 text-lg leading-8 text-gray-600">Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.</p></div><div class="mx-auto mt-10 max-w-2xl"><h3 class="text-center text-lg font-semibold leading-8 text-gray-900">Supported File Formats</h3><div class="mt-6 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4"><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"></path></svg><span class="mt-2 text-sm font-medium">PDF</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"></path></svg><span class="mt-2 text-sm font-medium">Word</span><span class="text-xs text-gray-500">DOC, DOCX</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0112 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H11.25a9.06 9.06 0 00-1.5.124m7.5 10.376h3.375c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span class="mt-2 text-sm font-medium">Excel</span><span class="text-xs text-gray-500">XLS, XLSX</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z"></path></svg><span class="mt-2 text-sm font-medium">HTML</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"></path></svg><span class="mt-2 text-sm font-medium">Images</span><span class="text-xs text-gray-500">JPG, PNG, etc.</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418"></path></svg><span class="mt-2 text-sm font-medium">Websites</span><span class="text-xs text-gray-500">URLs</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"></path></svg><span class="mt-2 text-sm font-medium">Text Files</span><span class="text-xs text-gray-500">TXT, RTF</span></div><div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg"><svg class="h-8 w-8 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6.429 9.75L2.25 12l4.179 2.25m0-4.5l5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0l4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0l-5.571 3-5.571-3"></path></svg><span class="mt-2 text-sm font-medium">And More</span><span class="text-xs text-gray-500">Many formats</span></div></div></div><div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl"><dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16"><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z"></path></svg></div>Easy File Upload</dt><dd class="mt-2 text-base leading-7 text-gray-600">Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.</dd></div><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"></path></svg></div>Advanced Options</dt><dd class="mt-2 text-base leading-7 text-gray-600">Customize your Markdown output with options for different Markdown flavors, image handling, and more.</dd></div><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z"></path></svg></div>Batch Processing</dt><dd class="mt-2 text-base leading-7 text-gray-600">Convert multiple files at once and download them individually or as a zip archive.</dd></div><div class="relative pl-16"><dt class="text-base font-semibold leading-7 text-gray-900"><div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600"><svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"></path></svg></div>Fast Conversion</dt><dd class="mt-2 text-base leading-7 text-gray-600">Our optimized conversion engine processes your documents quickly, saving you time and effort.</dd></div></dl></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--></main><footer class="bg-white"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="border-t border-gray-200 py-8 text-center text-sm text-gray-500"><p>© 2025 All to Markdown. All rights reserved.</p></div></div></footer><script src="/_next/static/chunks/webpack-f00bc68ecb5ff24e.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[4350,[\"218\",\"static/chunks/218-e6728dbb1f2b9008.js\",\"897\",\"static/chunks/897-19a9bdd2cdb08ddc.js\",\"985\",\"static/chunks/985-f18d388702cb1bef.js\",\"874\",\"static/chunks/874-43aa1baf760795c9.js\",\"766\",\"static/chunks/766-9eba7ddadf798198.js\",\"177\",\"static/chunks/app/layout-41b2a25be9e4e126.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[1057,[\"218\",\"static/chunks/218-e6728dbb1f2b9008.js\",\"874\",\"static/chunks/874-43aa1baf760795c9.js\",\"974\",\"static/chunks/app/page-62730e1b19ebc181.js\"],\"default\"]\n9:I[9665,[],\"MetadataBoundary\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[6614,[],\"\"]\n:HL[\"/_next/static/css/cb040c7238448b67.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"Ar_N2Lk6nq4F2uBmM9nvn\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/cb040c7238448b67.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"qPB9UaeRDfR0vie-xiozM\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\na:[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"15:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"All to Markdown - Convert Any Document to AI-Friendly Markdown\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Make your content truly understandable by AI models like ChatGPT and Claude.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"keywords\",\"content\":\"markdown converter, ai friendly format, chatgpt friendly, pdf to markdown, word to markdown, excel to markdown, html to markdown, image to markdown, website to markdown, url to markdown, doc to markdown, docx to markdown, xlsx to markdown, document conversion, ai readable\"}],[\"$\",\"meta\",\"3\",{\"property\":\"og:title\",\"content\":\"All to Markdown - Convert Any Document to AI-Friendly Markdown\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:description\",\"content\":\"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://alltomarkdown.com\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:site_name\",\"content\":\"All to Markdown\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"8\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:title\",\"content\":\"All to Markdown - Convert Any Document to AI-Friendly Markdown\"}],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:description\",\"content\":\"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.\"}],[\"$\",\"link\",\"11\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\nf:{\"metadata\":\"$15:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>