(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{4983:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var a=n(9724);let s=null,o=()=>{if(s)return s;let e="https://rpzceoedurujspnnyvkm.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJwemNlb2VkdXJ1anNwbm55dmttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1OTkwNDgsImV4cCI6MjA2MjE3NTA0OH0._zuv48OJiram1Q_QXndbl6exkL3P8qrgI_MfybQzCEo";if(!e)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");if(!t)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");return s=(0,a.UU)(e,t,{auth:{debug:!0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{fetch:async(e,t)=>{console.log("[Supabase Debug] Fetch request to: ".concat(e)),console.log("[Supabase Debug] Fetch options:",t);try{let n=await fetch(e,t);return console.log("[Supabase Debug] Response status: ".concat(n.status)),n}catch(e){throw console.error("[Supabase Debug] Fetch error:",e),e}}}}),console.log("[Supabase Debug] Client initialized with URL:",e),s}},7190:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var a=n(7985),s=n(1218);let o={common:{siteName:"All to Markdown",home:"Home",convert:"Convert",pricing:"Pricing",login:"Login",logout:"Logout",profile:"Profile",subscription:"Subscription",user:"User",footer:"\xa9 {{year}} All to Markdown. All rights reserved.",languageSwitcher:"Language",english:"English",chinese:"中文",userGroupPlaceholder:"N/A",paymentStatusPlaceholder:"N/A",openUserMenu:"Open user menu",userGroup:"User Group",paymentStatus:"Payment Status"},home:{title:"Convert Documents to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.",startConverting:"Start Converting",viewPricing:"View Pricing",aiTitle:"Why Markdown is Perfect for AI",aiDescription:"Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.",clearStructure:"Clear Structure",clearStructureDesc:"Markdown's simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.",enhancedResponses:"Enhanced AI Responses",enhancedResponsesDesc:"When feeding Markdown to AI like ChatGPT or Claude, you'll get more accurate responses because the AI can better understand context and relationships in your content.",noFormatting:"No Formatting Noise",noFormattingDesc:"Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.",convertAnyFormat:"Convert any document format to clean Markdown",convertDesc:"Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.",supportedFormats:"Supported File Formats",easyUpload:"Easy File Upload",easyUploadDesc:"Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.",advancedOptions:"Advanced Options",advancedOptionsDesc:"Customize your Markdown output with options for different Markdown flavors, image handling, and more.",batchProcessing:"Batch Processing",batchProcessingDesc:"Convert multiple files at once and download them individually or as a zip archive.",fastConversion:"Fast Conversion",fastConversionDesc:"Our optimized conversion engine processes your documents quickly, saving you time and effort."},convert:{title:"Convert to AI-Friendly Markdown",description:"Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.",aiTip:"AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.",startConversion:"Start Conversion",options:"Conversion Options",markdownFlavor:"Markdown Flavor",markdownFlavorDesc:"Select the Markdown specification to follow",aiOptimized:"AI-Optimized Format",aiOptimizedDesc:"Optimize output for AI models like ChatGPT and Claude",advancedOptions:"Advanced Options",imageHandling:"Image Handling",imageHandlingDesc:"How to handle images in the converted Markdown",enableImageDescription:"Generate descriptive text for images",enableImageDescriptionDesc:"Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.",imageDescriptionAttachmentMode:"Description Attachment Method:",attachmentModeKeepImage:"Keep Image",attachmentModeReplaceImage:"Replace Image",tableHandling:"Table Handling",tableHandlingDesc:"Table formatting style in the converted Markdown",successMessage:"Your file has been successfully converted to Markdown!",successMessagePlural:"{{count}} files have been successfully converted to Markdown!",aiSuccessTip:"Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.",download:"Download .md",downloadAll:"Download All (.zip)"},pricing:{title:"Pricing",subtitle:"Pricing plans for all needs",description:"Choose the perfect plan for your document conversion needs. All plans include our core conversion features.",free:"Free",pro:"Pro",enterprise:"Enterprise",mostPopular:"Most popular",monthly:"/month",yearly:"/year",getStarted:"Get started",subscribe:"Subscribe"},auth:{loginTitle:"Login",loginDescription:"Login to your account",emailLabel:"Email",passwordLabel:"Password",loginButton:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",signUpLink:"Sign up",registerTitle:"Sign Up",registerDescription:"Create a new account",confirmPasswordLabel:"Confirm Password",registerButton:"Sign Up",hasAccount:"Already have an account?",signInLink:"Sign in",orContinueWith:"Or continue with",github:"GitHub",google:"Google",magicLinkSent:"Magic link sent!",checkYourEmail:"Check your email for the magic link to login."}},r={common:{siteName:"All to Markdown",home:"首页",convert:"转换",pricing:"价格",login:"登录",logout:"退出登录",profile:"个人资料",subscription:"订阅状态",user:"用户",footer:"\xa9 {{year}} All to Markdown. 保留所有权利。",languageSwitcher:"语言",english:"English",chinese:"中文",userGroupPlaceholder:"暂无",paymentStatusPlaceholder:"暂无",openUserMenu:"打开用户菜单",userGroup:"用户组",paymentStatus:"支付状态",register:"注册"},home:{title:"将文档转换为AI友好的Markdown格式",description:"将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。",startConverting:"开始转换",viewPricing:"查看价格",aiTitle:"为什么Markdown对AI来说是完美的",aiDescription:"Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。",clearStructure:"清晰的结构",clearStructureDesc:"Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。",enhancedResponses:"增强的AI响应",enhancedResponsesDesc:"当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。",noFormatting:"没有格式噪音",noFormattingDesc:"与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。",convertAnyFormat:"将任何文档格式转换为清晰的Markdown",convertDesc:"我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。",supportedFormats:"支持的文件格式",easyUpload:"轻松上传文件",easyUploadDesc:"拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。",advancedOptions:"高级选项",advancedOptionsDesc:"使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。",batchProcessing:"批量处理",batchProcessingDesc:"一次转换多个文件，并单独下载或作为zip存档下载。",fastConversion:"快速转换",fastConversionDesc:"我们优化的转换引擎快速处理您的文档，节省您的时间和精力。"},convert:{title:"转换为AI友好的Markdown",description:"上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。",aiTip:"AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。",startConversion:"开始转换",options:"转换选项",markdownFlavor:"Markdown风格",markdownFlavorDesc:"选择要遵循的Markdown规范",aiOptimized:"AI优化格式",aiOptimizedDesc:"为ChatGPT和Claude等AI模型优化输出",advancedOptions:"高级选项",imageHandling:"图像处理",imageHandlingDesc:"如何处理转换后的Markdown中的图像",enableImageDescription:"为图片生成描述性文字",enableImageDescriptionDesc:"为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。",imageDescriptionAttachmentMode:"描述文字附加方式:",attachmentModeKeepImage:"保留图片",attachmentModeReplaceImage:"替换图片",tableHandling:"表格处理",tableHandlingDesc:"转换后的Markdown中的表格格式样式",successMessage:"您的文件已成功转换为Markdown！",successMessagePlural:"{{count}}个文件已成功转换为Markdown！",aiSuccessTip:"您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。",download:"下载.md",downloadAll:"下载全部(.zip)"},pricing:{title:"价格",subtitle:"满足所有需求的价格计划",description:"选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。",free:"免费版",pro:"专业版",enterprise:"企业版",mostPopular:"最受欢迎",monthly:"/月",yearly:"/年",getStarted:"开始使用",subscribe:"订阅"},auth:{loginTitle:"登录",loginDescription:"登录您的账户",emailLabel:"邮箱",passwordLabel:"密码",loginButton:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账户？",signUpLink:"注册",registerTitle:"注册",registerDescription:"创建一个新账户",confirmPasswordLabel:"确认密码",registerButton:"注册",hasAccount:"已有账户？",signInLink:"登录",orContinueWith:"或继续使用",github:"GitHub",google:"Google",magicLinkSent:"魔法链接已发送！",checkYourEmail:"请检查您的邮箱以获取魔法链接进行登录。"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["common","auth"],n=(0,a.Q_)();return n.use(s.r9).init({lng:e,ns:t,fallbackLng:"en",interpolation:{escapeValue:!1},resources:{en:{...o},zh:{...r}}}),n}},7977:(e,t,n)=>{Promise.resolve().then(n.bind(n,8007))},8007:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var a=n(5155),s=n(2115),o=n(6874),r=n.n(o),i=n(4983),l=n(1218),d=n(7190);function c(){let{t:e,i18n:t}=(0,l.Bd)(["auth","common"]),n=(0,d.A)(t.language,["auth","common"]),[o,c]=(0,s.useState)(""),[u,m]=(0,s.useState)(""),[g,h]=(0,s.useState)(!1),[p,f]=(0,s.useState)(null),b=async e=>{e.preventDefault(),h(!0),f(null);let t=(0,i.A)();try{let{error:e}=await t.auth.signInWithPassword({email:o,password:u});if(e)throw e;window.location.href="/convert"}catch(e){console.error("Login attempt failed:",e),e instanceof Error?f(e.message):f(n.t("auth:loginError"))}finally{h(!1)}};return(0,a.jsxs)("div",{className:"flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)("h2",{className:"mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900",children:e("auth:loginTitle")})}),(0,a.jsxs)("div",{className:"mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]",children:[(0,a.jsxs)("div",{className:"bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12",children:[(0,a.jsxs)("form",{className:"space-y-6",onSubmit:b,children:[p&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:p})})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:emailLabel")}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:e=>c(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium leading-6 text-gray-900",children:e("auth:passwordLabel")}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:u,onChange:e=>m(e.target.value),className:"block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"}),(0,a.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm leading-6 text-gray-900",children:e("auth:rememberMe",{ns:"auth",defaultValue:"Remember me"})})]}),(0,a.jsx)("div",{className:"text-sm leading-6",children:(0,a.jsx)("a",{href:"#",className:"font-semibold text-blue-600 hover:text-blue-500",children:e("auth:forgotPassword")})})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:g,className:"flex w-full justify-center rounded-md bg-blue-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:g?e("auth:loggingIn",{ns:"auth",defaultValue:"Signing in..."}):e("auth:loginButton")})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"relative mt-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center","aria-hidden":"true",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm font-medium leading-6",children:(0,a.jsx)("span",{className:"bg-white px-6 text-gray-900",children:e("auth:orContinueWith")})})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-4",children:[(0,a.jsxs)("a",{href:"#",className:"flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent",children:[(0,a.jsxs)("svg",{className:"h-5 w-5","aria-hidden":"true",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M12.0003 4.75C13.7703 4.75 15.3553 5.36002 16.6053 6.54998L20.0303 3.125C17.9502 1.19 15.2353 0 12.0003 0C7.31028 0 3.25527 2.69 1.28027 6.60998L5.27028 9.70498C6.21525 6.86002 8.87028 4.75 12.0003 4.75Z",fill:"#EA4335"}),(0,a.jsx)("path",{d:"M23.49 12.275C23.49 11.49 23.415 10.73 23.3 10H12V14.51H18.47C18.18 15.99 17.34 17.25 16.08 18.1L19.945 21.1C22.2 19.01 23.49 15.92 23.49 12.275Z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M5.26498 14.2949C5.02498 13.5699 4.88501 12.7999 4.88501 11.9999C4.88501 11.1999 5.01998 10.4299 5.26498 9.7049L1.275 6.60986C0.46 8.22986 0 10.0599 0 11.9999C0 13.9399 0.46 15.7699 1.28 17.3899L5.26498 14.2949Z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M12.0004 24.0001C15.2404 24.0001 17.9654 22.935 19.9454 21.095L16.0804 18.095C15.0054 18.82 13.6204 19.245 12.0004 19.245C8.8704 19.245 6.21537 17.135 5.2654 14.29L1.27539 17.385C3.25539 21.31 7.3104 24.0001 12.0004 24.0001Z",fill:"#34A853"})]}),(0,a.jsx)("span",{className:"text-sm font-semibold leading-6",children:e("auth:google")})]}),(0,a.jsxs)("a",{href:"#",className:"flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent",children:[(0,a.jsx)("svg",{className:"h-5 w-5 fill-[#24292F]","aria-hidden":"true",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z",clipRule:"evenodd"})}),(0,a.jsx)("span",{className:"text-sm font-semibold leading-6",children:e("auth:github")})]})]})]})]}),(0,a.jsxs)("p",{className:"mt-10 text-center text-sm text-gray-500",children:[e("auth:noAccount")," ",(0,a.jsx)(r(),{href:"/register",className:"font-semibold leading-6 text-blue-600 hover:text-blue-500",children:e("auth:signUpLink")})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[218,897,985,874,441,684,358],()=>t(7977)),_N_E=e.O()}]);