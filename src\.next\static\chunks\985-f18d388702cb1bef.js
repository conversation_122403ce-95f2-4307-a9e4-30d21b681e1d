"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[985],{7985:(e,t,s)=>{s.d(t,{Q_:()=>X});let i=e=>"string"==typeof e,r=()=>{let e,t,s=new Promise((s,i)=>{e=s,t=i});return s.resolve=e,s.reject=t,s},a=e=>null==e?"":""+e,o=(e,t,s)=>{e.forEach(e=>{t[e]&&(s[e]=t[e])})},n=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(n,"."):e,h=e=>!e||i(e),u=(e,t,s)=>{let r=i(t)?t.split("."):t,a=0;for(;a<r.length-1;){if(h(e))return{};let t=l(r[a]);!e[t]&&s&&(e[t]=new s),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++a}return h(e)?{}:{obj:e,k:l(r[a])}},g=(e,t,s)=>{let{obj:i,k:r}=u(e,t,Object);if(void 0!==i||1===t.length){i[r]=s;return}let a=t[t.length-1],o=t.slice(0,t.length-1),n=u(e,o,Object);for(;void 0===n.obj&&o.length;)a=`${o[o.length-1]}.${a}`,n=u(e,o=o.slice(0,o.length-1),Object),n?.obj&&void 0!==n.obj[`${n.k}.${a}`]&&(n.obj=void 0);n.obj[`${n.k}.${a}`]=s},p=(e,t,s,i)=>{let{obj:r,k:a}=u(e,t,Object);r[a]=r[a]||[],r[a].push(s)},d=(e,t)=>{let{obj:s,k:i}=u(e,t);if(s&&Object.prototype.hasOwnProperty.call(s,i))return s[i]},c=(e,t,s)=>{let i=d(e,s);return void 0!==i?i:d(t,s)},f=(e,t,s)=>{for(let r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?i(e[r])||e[r]instanceof String||i(t[r])||t[r]instanceof String?s&&(e[r]=t[r]):f(e[r],t[r],s):e[r]=t[r]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var v={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let y=e=>i(e)?e.replace(/[&<>"'\/]/g,e=>v[e]):e;class x{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}let b=[" ",",","?","!",";"],k=new x(20),S=(e,t,s)=>{t=t||"",s=s||"";let i=b.filter(e=>0>t.indexOf(e)&&0>s.indexOf(e));if(0===i.length)return!0;let r=k.getRegExp(`(${i.map(e=>"?"===e?"\\?":e).join("|")})`),a=!r.test(e);if(!a){let t=e.indexOf(s);t>0&&!r.test(e.substring(0,t))&&(a=!0)}return a},O=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}let i=t.split(s),r=e;for(let e=0;e<i.length;){let t;if(!r||"object"!=typeof r)return;let a="";for(let o=e;o<i.length;++o)if(o!==e&&(a+=s),a+=i[o],void 0!==(t=r[a])){if(["string","number","boolean"].indexOf(typeof t)>-1&&o<i.length-1)continue;e+=o-e+1;break}r=t}return r},L=e=>e?.replace("_","-"),$={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class w{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||$,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,r){return r&&!this.debug?null:(i(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new w(this.logger,{...{prefix:`${this.prefix}:${e}:`},...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new w(this.logger,e)}}var R=new w;class C{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t)return void delete this.observers[e];this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,i]=e;for(let e=0;e<i;e++)t(...s)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[i,r]=t;for(let t=0;t<r;t++)i.apply(i,[e,...s])})}}class P extends C{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s){let r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator,n=void 0!==a.ignoreJSONStructure?a.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?r=e.split("."):(r=[e,t],s&&(Array.isArray(s)?r.push(...s):i(s)&&o?r.push(...s.split(o)):r.push(s)));let l=d(this.data,r);return(!l&&!t&&!s&&e.indexOf(".")>-1&&(e=r[0],t=r[1],s=r.slice(2).join(".")),!l&&n&&i(s))?O(this.data?.[e]?.[t],s,o):l}addResource(e,t,s,i){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,o=[e,t];s&&(o=o.concat(a?s.split(a):s)),e.indexOf(".")>-1&&(o=e.split("."),i=t,t=o[1]),this.addNamespaces(t),g(this.data,o,i),r.silent||this.emit("added",e,t,s,i)}addResources(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let r in s)(i(s[r])||Array.isArray(s[r]))&&this.addResource(e,t,r,s[r],{silent:!0});r.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,i,r){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),i=s,s=t,t=o[1]),this.addNamespaces(t);let n=d(this.data,o)||{};a.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?f(n,s,r):n={...n,...s},g(this.data,o,n),a.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var N={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,r){return e.forEach(e=>{t=this.processors[e]?.process(t,s,i,r)??t}),t}};let j={},E=e=>!i(e)&&"boolean"!=typeof e&&"number"!=typeof e;class F extends C{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=R.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let s=this.resolve(e,t);return s?.res!==void 0}extractFromKey(e,t){let s=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===s&&(s=":");let r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,a=t.ns||this.options.defaultNS||[],o=s&&e.indexOf(s)>-1,n=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!S(e,s,r);if(o&&!n){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:i(a)?[a]:a};let o=e.split(s);(s!==r||s===r&&this.options.ns.indexOf(o[0])>-1)&&(a=o.shift()),e=o.join(r)}return{key:e,namespaces:i(a)?[a]:a}}translate(e,t,s){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let r=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,a=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:n}=this.extractFromKey(e[e.length-1],t),l=n[n.length-1],h=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(h?.toLowerCase()==="cimode"){if(u){let e=t.nsSeparator||this.options.nsSeparator;return r?{res:`${l}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${e}${o}`}return r?{res:o,usedKey:o,exactUsedKey:o,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:o}let g=this.resolve(e,t),p=g?.res,d=g?.usedKey||o,c=g?.exactUsedKey||o,f=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,m=!this.i18nFormat||this.i18nFormat.handleAsObject,v=void 0!==t.count&&!i(t.count),y=F.hasDefaultValue(t),x=v?this.pluralResolver.getSuffix(h,t.count,t):"",b=t.ordinal&&v?this.pluralResolver.getSuffix(h,t.count,{ordinal:!1}):"",k=v&&!t.ordinal&&0===t.count,S=k&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${x}`]||t[`defaultValue${b}`]||t.defaultValue,O=p;m&&!p&&y&&(O=S);let L=E(O),$=Object.prototype.toString.apply(O);if(m&&O&&L&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf($)&&!(i(f)&&Array.isArray(O))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(d,O,{...t,ns:n}):`key '${o} (${this.language})' returned an object instead of string.`;return r?(g.res=e,g.usedParams=this.getUsedParamsDetails(t),g):e}if(a){let e=Array.isArray(O),s=e?[]:{},i=e?c:d;for(let e in O)if(Object.prototype.hasOwnProperty.call(O,e)){let r=`${i}${a}${e}`;y&&!p?s[e]=this.translate(r,{...t,defaultValue:E(S)?S[e]:void 0,...{joinArrays:!1,ns:n}}):s[e]=this.translate(r,{...t,joinArrays:!1,ns:n}),s[e]===r&&(s[e]=O[e])}p=s}}else if(m&&i(f)&&Array.isArray(p))(p=p.join(f))&&(p=this.extendTranslation(p,e,t,s));else{let i=!1,r=!1;!this.isValidLookup(p)&&y&&(i=!0,p=S),this.isValidLookup(p)||(r=!0,p=o);let n=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&r?void 0:p,u=y&&S!==p&&this.options.updateMissing;if(r||i||u){if(this.logger.log(u?"updateKey":"missingKey",h,l,o,u?S:p),a){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],s=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&s&&s[0])for(let t=0;t<s.length;t++)e.push(s[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let i=(e,s,i)=>{let r=y&&i!==p?i:n;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,s,r,u,t):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,l,s,r,u,t),this.emit("missingKey",e,l,s,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&v?e.forEach(e=>{let s=this.pluralResolver.getSuffixes(e,t);k&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>s.indexOf(`${this.options.pluralSeparator}zero`)&&s.push(`${this.options.pluralSeparator}zero`),s.forEach(s=>{i([e],o+s,t[`defaultValue${s}`]||S)})}):i(e,o,S))}p=this.extendTranslation(p,e,t,g,s),r&&p===o&&this.options.appendNamespaceToMissingKey&&(p=`${l}:${o}`),(r||i)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${o}`:o,i?p:void 0,t))}return r?(g.res=p,g.usedParams=this.getUsedParamsDetails(t),g):p}extendTranslation(e,t,s,r,a){var o=this;if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!s.skipInterpolation){let n;s.interpolation&&this.interpolator.init({...s,...{interpolation:{...this.options.interpolation,...s.interpolation}}});let l=i(e)&&(s?.interpolation?.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);n=t&&t.length}let h=s.replace&&!i(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,s.lng||this.language||r.usedLng,s),l){let t=e.match(this.interpolator.nestingRegexp);n<(t&&t.length)&&(s.nest=!1)}!s.lng&&r&&r.res&&(s.lng=this.language||r.usedLng),!1!==s.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];return a?.[0]!==i[0]||s.context?o.translate(...i,t):(o.logger.warn(`It seems you are nesting recursively key: ${i[0]} in key: ${t[0]}`),null)},s)),s.interpolation&&this.interpolator.reset()}let n=s.postProcess||this.options.postProcess,l=i(n)?[n]:n;return null!=e&&l?.length&&!1!==s.applyPostProcessor&&(e=N.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e){let t,s,r,a,o,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,n),h=l.key;s=h;let u=l.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let g=void 0!==n.count&&!i(n.count),p=g&&!n.ordinal&&0===n.count,d=void 0!==n.context&&(i(n.context)||"number"==typeof n.context)&&""!==n.context,c=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(o=e,!j[`${c[0]}-${e}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(o)&&(j[`${c[0]}-${e}`]=!0,this.logger.warn(`key "${s}" for languages "${c.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),c.forEach(s=>{let i;if(this.isValidLookup(t))return;a=s;let o=[h];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(o,h,s,e,n);else{let e;g&&(e=this.pluralResolver.getSuffix(s,n.count,n));let t=`${this.options.pluralSeparator}zero`,i=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(o.push(h+e),n.ordinal&&0===e.indexOf(i)&&o.push(h+e.replace(i,this.options.pluralSeparator)),p&&o.push(h+t)),d){let s=`${h}${this.options.contextSeparator}${n.context}`;o.push(s),g&&(o.push(s+e),n.ordinal&&0===e.indexOf(i)&&o.push(s+e.replace(i,this.options.pluralSeparator)),p&&o.push(s+t))}}for(;i=o.pop();)this.isValidLookup(t)||(r=i,t=this.getResource(s,e,i,n))}))})}),{res:t,usedKey:s,exactUsedKey:r,usedLng:a,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!i(e.replace),s=t?e.replace:e;if(t&&void 0!==e.count&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!t)for(let e of(s={...s},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete s[e];return s}static hasDefaultValue(e){let t="defaultValue";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&void 0!==e[s])return!0;return!1}}class V{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}getScriptPartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length||(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(i(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch(e){}return(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)?t:this.options.lowerCaseLng?e.toLowerCase():e}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let s=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let s=this.getScriptPartFromCode(e);if(this.isSupportedCode(s))return t=s;let i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(e=>{if(e===i||!(0>e.indexOf("-")&&0>i.indexOf("-"))&&(e.indexOf("-")>0&&0>i.indexOf("-")&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),i(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){let s=this.getFallbackCodes(t||this.options.fallbackLng||[],e),r=[],a=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return i(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&a(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&a(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&a(this.getLanguagePartFromCode(e))):i(e)&&a(this.formatLanguageCode(e)),s.forEach(e=>{0>r.indexOf(e)&&a(this.formatLanguageCode(e))}),r}}let D={zero:0,one:1,two:2,few:3,many:4,other:5},A={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class I{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=L("dev"===e?"en":e),r=s.ordinal?"ordinal":"cardinal",a=JSON.stringify({cleanedCode:i,type:r});if(a in this.pluralRulesCache)return this.pluralRulesCache[a];try{t=new Intl.PluralRules(i,{type:r})}catch(r){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),A;if(!e.match(/-|_/))return A;let i=this.languageUtils.getLanguagePartFromCode(e);t=this.getRule(i,s)}return this.pluralRulesCache[a]=t,t}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,s).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=this.getRule(e,t);return(s||(s=this.getRule("dev",t)),s)?s.resolvedOptions().pluralCategories.sort((e,t)=>D[e]-D[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):[]}getSuffix(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.getRule(e,s);return i?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}let T=function(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",a=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=c(e,t,s);return!o&&a&&i(s)&&void 0===(o=O(e,s,r))&&(o=O(t,s,r)),o},K=e=>e.replace(/\$/g,"$$$$");class U{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:r,prefixEscaped:a,suffix:o,suffixEscaped:n,formatSeparator:l,unescapeSuffix:h,unescapePrefix:u,nestingPrefix:g,nestingPrefixEscaped:p,nestingSuffix:d,nestingSuffixEscaped:c,nestingOptionsSeparator:f,maxReplaces:v,alwaysFormat:x}=e.interpolation;this.escape=void 0!==t?t:y,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=r?m(r):a||"{{",this.suffix=o?m(o):n||"}}",this.formatSeparator=l||",",this.unescapePrefix=h?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=g?m(g):p||m("$t("),this.nestingSuffix=d?m(d):c||m(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=v||1e3,this.alwaysFormat=void 0!==x&&x,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e?.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,r){let o,n,l,h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{if(0>e.indexOf(this.formatSeparator)){let i=T(t,h,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(i,void 0,s,{...r,...t,interpolationkey:e}):i}let i=e.split(this.formatSeparator),a=i.shift().trim(),o=i.join(this.formatSeparator).trim();return this.format(T(t,h,a,this.options.keySeparator,this.options.ignoreJSONStructure),o,s,{...r,...t,interpolationkey:a})};this.resetRegExp();let g=r?.missingInterpolationHandler||this.options.missingInterpolationHandler,p=r?.interpolation?.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>K(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?K(this.escape(e)):K(e)}].forEach(t=>{for(l=0;o=t.regex.exec(e);){let s=o[1].trim();if(void 0===(n=u(s)))if("function"==typeof g){let t=g(e,o,r);n=i(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,s))n="";else if(p){n=o[0];continue}else this.logger.warn(`missed to pass in variable ${s} for interpolating ${e}`),n="";else i(n)||this.useRawValueToEscape||(n=a(n));let h=t.safeValue(n);if(e=e.replace(o[0],h),p?(t.regex.lastIndex+=n.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let s,r,o,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let s=this.nestingOptionsSeparator;if(0>e.indexOf(s))return e;let i=e.split(RegExp(`${s}[ ]*{`)),r=`{${i[1]}`;e=i[0];let a=(r=this.interpolate(r,o)).match(/'/g),n=r.match(/"/g);((a?.length??0)%2!=0||n)&&n.length%2==0||(r=r.replace(/'/g,'"'));try{o=JSON.parse(r),t&&(o={...t,...o})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${s}${r}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;s=this.nestingRegexp.exec(e);){let h=[];(o=(o={...n}).replace&&!i(o.replace)?o.replace:o).applyPostProcessor=!1,delete o.defaultValue;let u=!1;if(-1!==s[0].indexOf(this.formatSeparator)&&!/{.*}/.test(s[1])){let e=s[1].split(this.formatSeparator).map(e=>e.trim());s[1]=e.shift(),h=e,u=!0}if((r=t(l.call(this,s[1].trim(),o),o))&&s[0]===e&&!i(r))return r;i(r)||(r=a(r)),r||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),r=""),u&&(r=h.reduce((e,t)=>this.format(e,t,n.lng,{...n,interpolationkey:s[1].trim()}),r.trim())),e=e.replace(s[0],r),this.regexp.lastIndex=0}return e}}let M=e=>{let t=e.toLowerCase().trim(),s={};if(e.indexOf("(")>-1){let i=e.split("(");t=i[0].toLowerCase().trim();let r=i[1].substring(0,i[1].length-1);"currency"===t&&0>r.indexOf(":")?s.currency||(s.currency=r.trim()):"relativetime"===t&&0>r.indexOf(":")?s.range||(s.range=r.trim()):r.split(";").forEach(e=>{if(e){let[t,...i]=e.split(":"),r=i.join(":").trim().replace(/^'+|'+$/g,""),a=t.trim();s[a]||(s[a]=r),"false"===r&&(s[a]=!1),"true"===r&&(s[a]=!0),isNaN(r)||(s[a]=parseInt(r,10))}})}return{formatName:t,formatOptions:s}},H=e=>{let t={};return(s,i,r)=>{let a=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(a={...a,[r.interpolationkey]:void 0});let o=i+JSON.stringify(a),n=t[o];return n||(n=e(L(i),r),t[o]=n),n(s)}};class z{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("formatter"),this.options=e,this.formats={number:H((e,t)=>{let s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)}),currency:H((e,t)=>{let s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)}),datetime:H((e,t)=>{let s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)}),relativetime:H((e,t)=>{let s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")}),list:H((e,t)=>{let s=new Intl.ListFormat(e,{...t});return e=>s.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=H(t)}format(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=t.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&0>r[0].indexOf(")")&&r.find(e=>e.indexOf(")")>-1)){let e=r.findIndex(e=>e.indexOf(")")>-1);r[0]=[r[0],...r.splice(1,e)].join(this.formatSeparator)}return r.reduce((e,t)=>{let{formatName:r,formatOptions:a}=M(t);if(this.formats[r]){let t=e;try{let o=i?.formatParams?.[i.interpolationkey]||{},n=o.locale||o.lng||i.locale||i.lng||s;t=this.formats[r](e,n,{...a,...i,...o})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${r}`),e},e)}}let B=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class J extends C{constructor(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(s,i.backend,i)}queueLoad(e,t,s,i){let r={},a={},o={},n={};return e.forEach(e=>{let i=!0;t.forEach(t=>{let o=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[o]=2:this.state[o]<0||(1===this.state[o]?void 0===a[o]&&(a[o]=!0):(this.state[o]=1,i=!1,void 0===a[o]&&(a[o]=!0),void 0===r[o]&&(r[o]=!0),void 0===n[t]&&(n[t]=!0)))}),i||(o[e]=!0)}),(Object.keys(r).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(r),pending:Object.keys(a),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(n)}}loaded(e,t,s){let i=e.split("|"),r=i[0],a=i[1];t&&this.emit("failedLoading",r,a,t),!t&&s&&this.store.addResourceBundle(r,a,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);let o={};this.queue.forEach(s=>{p(s.loaded,[r],a),B(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach(e=>{o[e]||(o[e]={});let t=s.loaded[e];t.length&&t.forEach(t=>{void 0===o[e][t]&&(o[e][t]=!0)})}),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(e=>!e.done)}read(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:r,callback:a});this.readingCalls++;let o=(o,n)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(o&&n&&i<this.maxRetries)return void setTimeout(()=>{this.read.call(this,e,t,s,i+1,2*r,a)},r);a(o,n)},n=this.backend[s].bind(this.backend);if(2===n.length){try{let s=n(e,t);s&&"function"==typeof s.then?s.then(e=>o(null,e)).catch(o):o(null,s)}catch(e){o(e)}return}return n(e,t,o)}prepareLoading(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();i(e)&&(e=this.languageUtils.toResolveHierarchy(e)),i(t)&&(t=[t]);let a=this.queueLoad(e,t,s,r);if(!a.toLoad.length)return a.pending.length||r(),null;a.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=e.split("|"),i=s[0],r=s[1];this.read(i,r,"read",void 0,void 0,(s,a)=>{s&&this.logger.warn(`${t}loading namespace ${r} for language ${i} failed`,s),!s&&a&&this.logger.log(`${t}loaded namespace ${r} for language ${i}`,a),this.loaded(e,s,a)})}saveMissing(e,t,s,i,r){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(t))return void this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");if(null!=s&&""!==s){if(this.backend?.create){let n={...a,isUpdate:r},l=this.backend.create.bind(this.backend);if(l.length<6)try{let r;(r=5===l.length?l(e,t,s,i,n):l(e,t,s,i))&&"function"==typeof r.then?r.then(e=>o(null,e)).catch(o):o(null,r)}catch(e){o(e)}else l(e,t,s,i,o,n)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}}let _=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),i(e[1])&&(t.defaultValue=e[1]),i(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let s=e[3]||e[2];Object.keys(s).forEach(e=>{t[e]=s[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),q=e=>(i(e.ns)&&(e.ns=[e.ns]),i(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),i(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),W=()=>{},Q=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class Y extends C{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=q(e),this.services={},this.logger=R,this.modules={external:[]},Q(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(s=t,t={}),null==t.defaultNS&&t.ns&&(i(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let a=_();this.options={...a,...this.options,...q(t)},this.options.interpolation={...a.interpolation,...this.options.interpolation},void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let o=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?R.init(o(this.modules.logger),this.options):R.init(null,this.options),t=this.modules.formatter?this.modules.formatter:z;let s=new V(this.options);this.store=new P(this.options.resources,this.options);let i=this.services;i.logger=R,i.resourceStore=this.store,i.languageUtils=s,i.pluralResolver=new I(s,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===a.interpolation.format)&&(i.formatter=o(t),i.formatter.init(i,this.options),this.options.interpolation.format=i.formatter.format.bind(i.formatter)),i.interpolator=new U(this.options),i.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},i.backendConnector=new J(o(this.modules.backend),i.resourceStore,i,this.options),i.backendConnector.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.languageDetector&&(i.languageDetector=o(this.modules.languageDetector),i.languageDetector.init&&i.languageDetector.init(i,this.options.detection,this.options)),this.modules.i18nFormat&&(i.i18nFormat=o(this.modules.i18nFormat),i.i18nFormat.init&&i.i18nFormat.init(this)),this.translator=new F(this.services,this.options),this.translator.on("*",function(t){for(var s=arguments.length,i=Array(s>1?s-1:0),r=1;r<s;r++)i[r-1]=arguments[r];e.emit(t,...i)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,s||(s=W),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let n=r(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),n.resolve(t),s(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),n}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W,s=t,r=i(e)?e:this.language;if("function"==typeof e&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if(r?.toLowerCase()==="cimode"&&(!this.options.preload||0===this.options.preload.length))return s();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};r?t(r):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload?.forEach?.(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),s(e)})}else s(null)}reloadResources(e,t,s){let i=r();return"function"==typeof e&&(s=e,e=void 0),"function"==typeof t&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=W),this.services.backendConnector.reload(e,t,e=>{i.resolve(),s(e)}),i}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&N.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&0>this.languages.indexOf(e)&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){var s=this;this.isLanguageChangingTo=e;let a=r();this.emit("languageChanging",e);let o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},n=(i,r)=>{r?this.isLanguageChangingTo===e&&(o(r),this.translator.changeLanguage(r),this.isLanguageChangingTo=void 0,this.emit("languageChanged",r),this.logger.log("languageChanged",r)):this.isLanguageChangingTo=void 0,a.resolve(function(){return s.t(...arguments)}),t&&t(i,function(){return s.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let s=i(t)?t:t&&t[0],r=this.store.hasLanguageSomeTranslations(s)?s:this.services.languageUtils.getBestMatchFromCodes(i(t)?[t]:t);r&&(this.language||o(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector?.cacheUserLanguage?.(r)),this.loadResources(r,e=>{n(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),a}getFixedT(e,t,s){var r=this;let a=function(e,t){let i,o;if("object"!=typeof t){for(var n=arguments.length,l=Array(n>2?n-2:0),h=2;h<n;h++)l[h-2]=arguments[h];i=r.options.overloadTranslationOptionHandler([e,t].concat(l))}else i={...t};i.lng=i.lng||a.lng,i.lngs=i.lngs||a.lngs,i.ns=i.ns||a.ns,""!==i.keyPrefix&&(i.keyPrefix=i.keyPrefix||s||a.keyPrefix);let u=r.options.keySeparator||".";return o=i.keyPrefix&&Array.isArray(e)?e.map(e=>`${i.keyPrefix}${u}${e}`):i.keyPrefix?`${i.keyPrefix}${u}${e}`:e,r.t(o,i)};return i(e)?a.lng=e:a.lngs=e,a.ns=t,a.keyPrefix=s,a}t(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.translator?.translate(...t)}exists(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.translator?.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,r=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;let a=(e,t)=>{let s=this.services.backendConnector.state[`${e}|${t}`];return -1===s||0===s||2===s};if(t.precheck){let e=t.precheck(this,a);if(void 0!==e)return e}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(s,e)&&(!i||a(r,e)))}loadNamespaces(e,t){let s=r();return this.options.ns?(i(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){let s=r();i(e)&&(e=[e]);let a=this.options.preload||[],o=e.filter(e=>0>a.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return o.length?(this.options.preload=a.concat(o),this.loadResources(e=>{s.resolve(),t&&t(e)}),s):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services?.languageUtils||new V(_())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new Y(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W,s=e.forkResourceStore;s&&delete e.forkResourceStore;let i={...this.options,...e,isClone:!0},r=new Y(i);return(void 0!==e.debug||void 0!==e.prefix)&&(r.logger=r.logger.clone(e)),["store","services","language"].forEach(e=>{r[e]=this[e]}),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},s&&(r.store=new P(Object.keys(this.store.data).reduce((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce((s,i)=>(s[i]={...e[t][i]},s),e[t]),e),{}),i),r.services.resourceStore=r.store),r.translator=new F(r.services,i),r.translator.on("*",function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];r.emit(e,...s)}),r.init(i,t),r.translator.options=i,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let G=Y.createInstance();G.createInstance=Y.createInstance;let X=G.createInstance;G.dir,G.init,G.loadResources,G.reloadResources,G.use,G.changeLanguage,G.getFixedT,G.t,G.exists,G.setDefaultNamespace,G.hasLoadedNamespace,G.loadNamespaces,G.loadLanguages}}]);