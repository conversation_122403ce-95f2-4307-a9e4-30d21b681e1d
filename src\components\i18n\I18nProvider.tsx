'use client';

import React, { useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import createI18nInstance from '../../i18n';

interface I18nProviderProps {
  children: React.ReactNode;
}

const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  const [instance] = useState(createI18nInstance());

  useEffect(() => {
    // Check if there's a language preference in localStorage
    const storedLanguage = localStorage.getItem('language');
    if (storedLanguage) {
      instance.changeLanguage(storedLanguage);
    } else {
      // Try to detect browser language
      const browserLanguage = navigator.language;
      if (browserLanguage.startsWith('zh')) {
        instance.changeLanguage('zh');
        localStorage.setItem('language', 'zh');
      }
    }
  }, [instance]);

  return (
    <I18nextProvider i18n={instance}>
      {children}
    </I18nextProvider>
  );
};

export default I18nProvider;
