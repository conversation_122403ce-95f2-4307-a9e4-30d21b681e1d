<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Image -->
  <g id="originalImage" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Original Image</text>

    <!-- Simple shapes -->
    <circle cx="35" cy="50" r="10" fill="#BDBDBD"/>
    <rect x="55" y="40" width="20" height="20" fill="#BDBDBD"/>
    <polygon points="35,70 25,90 45,90" fill="#BDBDBD"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="1.5s" fill="freeze" />
  </g>

  <!-- Recognized Image -->
  <g id="recognizedImage" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Recognized Image</text>

    <!-- Simple shapes (same as original) -->
    <circle cx="35" cy="50" r="10" fill="#BDBDBD"/>
    <rect x="55" y="40" width="20" height="20" fill="#BDBDBD"/>
    <polygon points="35,70 25,90 45,90" fill="#BDBDBD"/>

    <!-- Bounding boxes -->
    <rect x="24" y="39" width="22" height="22" fill="none" stroke="#4CAF50" stroke-width="1" rx="1" ry="1"/> <!-- Circle BBox -->
    <rect x="54" y="39" width="22" height="22" fill="none" stroke="#2196F3" stroke-width="1" rx="1" ry="1"/> <!-- Square BBox -->
    <rect x="24" y="69" width="22" height="22" fill="none" stroke="#FF9800" stroke-width="1" rx="1" ry="1"/> <!-- Triangle BBox -->
    
    <!-- Optional: Text labels for recognized objects -->
    <!-- <text x="35" y="35" font-size="5" fill="#4CAF50" text-anchor="middle">obj1</text> -->
    <!-- <text x="65" y="35" font-size="5" fill="#2196F3" text-anchor="middle">obj2</text> -->
    <!-- <text x="35" y="98" font-size="5" fill="#FF9800" text-anchor="middle">obj3</text> -->


    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="1.5s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="1.5s" dur="1.5s" fill="freeze" />
  </g>
</svg>
