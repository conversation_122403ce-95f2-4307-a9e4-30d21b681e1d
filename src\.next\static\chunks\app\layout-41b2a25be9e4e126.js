(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1328:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,9324,23)),Promise.resolve().then(n.bind(n,4350))},4236:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,B:()=>l});var a=n(2115),o=n(4983);let r="local_jwt_token";async function s(e){try{console.log("[Auth Debug] syncWithBackend started with token:",e.substring(0,10)+"...");let t="".concat("http://localhost:8000","/api/v1/users/auth/supabase/callback");console.log("[Auth Debug] Calling backend API at:",t);let n=Date.now(),a=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({access_token:e})}),o=Date.now()-n;if(console.log("[Auth Debug] Backend API call completed in ".concat(o,"ms with status:"),a.status),a.ok){let e=await a.json();if(e.access_token)return localStorage.setItem(r,e.access_token),console.log("Local JWT stored successfully."),e.access_token;return console.error("Backend callback response missing access_token:",e),localStorage.removeItem(r),null}{let e;try{e=await a.json()}catch(t){e={message:a.statusText}}return console.error("Error syncing with backend:",a.status,e),localStorage.removeItem(r),null}}catch(e){return console.error("Network error or other issue syncing with backend:",e),localStorage.removeItem(r),null}}function i(){let[e,t]=(0,a.useState)({user:null,session:null,isLoading:!0});return(0,a.useEffect)(()=>{console.log("[Auth Debug] useAuth hook initialized");let e=(0,o.A)();console.log("[Auth Debug] Supabase client obtained:",!!e),(async()=>{console.log("useAuth: initializeAuth started");try{let{data:{session:n},error:a}=await e.auth.getSession();if(a){console.error("useAuth: initializeAuth - error fetching session:",a),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth failed due to session error.");return}if(console.log("useAuth: initializeAuth - initialSession fetched:",n?"exists":"null"),n&&n.access_token){console.log("useAuth: initializeAuth - session exists");let e=n.user;console.log("useAuth: initializeAuth - user from session:",e?e.id:"null"),e?(console.log("useAuth: initializeAuth - user exists, syncing with backend..."),await s(n.access_token),console.log("useAuth: initializeAuth - backend sync complete."),t({user:e,session:n,isLoading:!1}),console.log("useAuth: initializeAuth completed with session and user.")):(console.warn("useAuth: initializeAuth - session exists but user is null in session."),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth completed with session but no user."))}else localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth completed without session.")}catch(e){console.error("Error initializing auth:",e),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: initializeAuth failed in catch block.")}})();let{data:{subscription:n}}=e.auth.onAuthStateChange(async(e,n)=>{console.log("useAuth: onAuthStateChange event:",e,"Session:",n?"exists":"null");try{if(n&&n.access_token){let e=n.user;if(console.log("useAuth: onAuthStateChange - user from session:",e?e.id:"null"),e){console.log("useAuth: onAuthStateChange - User object exists. Attempting to sync with backend...");try{await s(n.access_token),console.log("useAuth: onAuthStateChange - backend sync complete.")}catch(e){console.error("useAuth: onAuthStateChange - EXCEPTION syncing with backend:",e)}t({user:e,session:n,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state with session.")}else console.warn("useAuth: onAuthStateChange - User is null in session."),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state to no user.")}else localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange updated state without session (SIGNED_OUT or null session).")}catch(e){console.error("useAuth: Error in onAuthStateChange callback:",e),localStorage.removeItem(r),t({user:null,session:null,isLoading:!1}),console.log("useAuth: onAuthStateChange failed in catch block, set isLoading to false.")}});return()=>{n.unsubscribe()}},[]),e}function l(){return localStorage.getItem(r)}},4350:(e,t,n)=>{"use strict";n.d(t,{default:()=>w});var a=n(5155),o=n(2115),r=n(6874),s=n.n(r),i=n(5695),l=n(1218),c=n(7190);let u=e=>{let{children:t}=e,[n]=(0,o.useState)((0,c.A)());return(0,o.useEffect)(()=>{let e=localStorage.getItem("language");e?n.changeLanguage(e):navigator.language.startsWith("zh")&&(n.changeLanguage("zh"),localStorage.setItem("language","zh"))},[n]),(0,a.jsx)(l.xC,{i18n:n,children:t})},d=o.forwardRef(function(e,t){let{title:n,titleId:a,...r}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),n?o.createElement("title",{id:a},n):null,o.createElement("path",{fillRule:"evenodd",d:"M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"}))}),g=o.forwardRef(function(e,t){let{title:n,titleId:a,...r}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),n?o.createElement("title",{id:a},n):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}),h=()=>{let{t:e,i18n:t}=(0,l.Bd)("common"),n=(0,i.useRouter)();(0,o.useEffect)(()=>{let e=document.createElement("style");return e.innerHTML="\n.language-dropdown-container:hover .language-dropdown {\n  opacity: 1;\n  transform: scale(1);\n  pointer-events: auto;\n}\n",document.head.appendChild(e),()=>{document.head.removeChild(e)}},[]);let r=e=>{t.changeLanguage(e),localStorage.setItem("language",e),n.refresh()},s=e("zh"===t.language?"chinese":"english");return(0,a.jsxs)("div",{className:"relative block text-left language-dropdown-container",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("button",{id:"language-menu-button",type:"button",className:"inline-flex items-center gap-x-1 rounded-md bg-transparent px-2 py-1.5 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2","aria-label":e("languageSwitcher"),"aria-haspopup":"true",children:[(0,a.jsx)(g,{className:"h-5 w-5","aria-hidden":"true"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:s}),(0,a.jsx)(d,{className:"h-5 w-5","aria-hidden":"true"})]})}),(0,a.jsx)("div",{className:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")}("language-dropdown opacity-0 scale-95 pointer-events-none","absolute right-0 z-10 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-blue-600 ring-opacity-5 focus:outline-none overflow-hidden transition-all duration-100 ease-out"),role:"menu","aria-orientation":"vertical","aria-labelledby":"language-menu-button",style:{top:"100%"},children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsx)("button",{onClick:()=>r("en"),className:"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors",role:"menuitem",children:"English"}),(0,a.jsx)("button",{onClick:()=>r("zh"),className:"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors",role:"menuitem",children:"中文"})]})})]})},m=o.forwardRef(function(e,t){let{title:n,titleId:a,...r}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),n?o.createElement("title",{id:a},n):null,o.createElement("path",{fillRule:"evenodd",d:"M7.84 1.804A1 1 0 0 1 8.82 1h2.36a1 1 0 0 1 .98.804l.331 1.652a6.993 6.993 0 0 1 1.929 1.115l1.598-.54a1 1 0 0 1 1.186.447l1.18 2.044a1 1 0 0 1-.205 1.251l-1.267 1.113a7.047 7.047 0 0 1 0 2.228l1.267 1.113a1 1 0 0 1 .206 1.25l-1.18 2.045a1 1 0 0 1-1.187.447l-1.598-.54a6.993 6.993 0 0 1-1.929 1.115l-.33 1.652a1 1 0 0 1-.98.804H8.82a1 1 0 0 1-.98-.804l-.331-1.652a6.993 6.993 0 0 1-1.929-1.115l-1.598.54a1 1 0 0 1-1.186-.447l-1.18-2.044a1 1 0 0 1 .205-1.251l1.267-1.114a7.05 7.05 0 0 1 0-2.227L1.821 7.773a1 1 0 0 1-.206-1.25l1.18-2.045a1 1 0 0 1 1.187-.447l1.598.54A6.992 6.992 0 0 1 7.51 3.456l.33-1.652ZM10 13a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",clipRule:"evenodd"}))});var p=n(4983),x=n(6766),y=n(4236);let b=e=>{var t,n,r,c,u,d,g,h;let{user:b}=e,{t:f}=(0,l.Bd)("common"),w=(0,i.useRouter)(),v=(0,p.A)(),[k,A]=(0,o.useState)(null),[j,C]=(0,o.useState)(!1),[I,L]=(0,o.useState)(null),[S,N]=(0,o.useState)("system");(0,o.useEffect)(()=>{let e=document.createElement("style");return e.innerHTML="\n@keyframes avatarPulse {\n  0% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n  50% {\n    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */\n  }\n  100% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n}\n\n.avatar-pulse {\n  animation: avatarPulse 1.5s ease-in-out infinite;\n}\n\n.dark .avatar-pulse {\n  animation-name: avatarPulseDark;\n}\n\n@keyframes avatarPulseDark {\n  0% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n  50% {\n    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */\n  }\n  100% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n}\n",document.head.appendChild(e),()=>{document.head.removeChild(e)}},[]),(0,o.useEffect)(()=>{let e=localStorage.getItem("theme");e&&N(e),"dark"===e?document.documentElement.classList.add("dark"):"light"===e?document.documentElement.classList.remove("dark"):("system"===e||!e)&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),e||(localStorage.setItem("theme","system"),N("system")));let t=window.matchMedia("(prefers-color-scheme: dark)"),n=e=>{"system"===localStorage.getItem("theme")&&(e.matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))};return t.addEventListener("change",n),()=>{t.removeEventListener("change",n)}},[]),(0,o.useEffect)(()=>{let e=async()=>{let e=(0,y.B)();if(!e)return void C(!1);C(!0),L(null);try{let t=new Promise(e=>setTimeout(e,1500)),n=fetch("".concat("http://localhost:8000","/api/v1/users/me"),{headers:{Authorization:"Bearer ".concat(e)}}),[a]=await Promise.all([n,t]);if(a.ok){let e=await a.json();A(e)}else{let e=await a.json();console.error("Failed to fetch local user data:",a.status,e),L(e.detail||"Error ".concat(a.status)),401===a.status&&localStorage.removeItem("local_jwt_token")}}catch(e){console.error("Error fetching local user data:",e),L(f("errorFetchingUserData","Failed to load user details."))}finally{C(!1)}};(null==b?void 0:b.email)?e():C(!1)},[b,f]);let M=async()=>{try{await v.auth.signOut(),localStorage.removeItem("local_jwt_token"),w.push("/"),w.refresh()}catch(e){console.error("Error signing out:",e)}},P=f("user"),E=null,D=null;return b&&(P=(null==(t=b.user_metadata)?void 0:t.name)||(null==(n=b.user_metadata)?void 0:n.full_name)||b.email||f("user"),E=b.email,D=(null==(r=b.user_metadata)?void 0:r.avatar_url)||(null==(c=b.user_metadata)?void 0:c.picture)),I?(P=f("error","Error"),E=null,D=null):k&&(P=k.full_name||k.username||(b?(null==(u=b.user_metadata)?void 0:u.name)||(null==(d=b.user_metadata)?void 0:d.full_name):"")||k.email,E=k.email,D=k.avatar_url||(b?(null==(g=b.user_metadata)?void 0:g.avatar_url)||(null==(h=b.user_metadata)?void 0:h.picture):null)),(0,a.jsxs)("div",{className:"relative inline-block text-left group",children:[j?(0,a.jsxs)("div",{className:"flex items-center justify-center rounded-full focus:outline-none h-10 w-10 cursor-pointer",children:[(0,a.jsx)("span",{className:"sr-only",children:f("loadingUserMenu","Loading user menu")}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full avatar-pulse"})]}):b||k?(0,a.jsxs)("div",{className:"flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none transition-colors h-10 w-10 cursor-pointer dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600",children:[(0,a.jsx)("span",{className:"sr-only",children:f("openUserMenu","Open user menu")}),D?(0,a.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:(0,a.jsx)(x.default,{src:D,alt:P,fill:!0,sizes:"40px",style:{objectFit:"cover"},priority:!0})}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,a.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("path",{d:"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(s(),{href:"/login",className:"px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-blue-400",children:f("login","Login")}),(0,a.jsx)("button",{onClick:()=>w.push("/register"),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:f("register","Register")})]}),!j&&(b||k)&&(0,a.jsxs)("div",{className:"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-150",style:{marginTop:"0.5rem"},children:[(0,a.jsx)("div",{className:"absolute -top-2 right-0 w-16 h-2 bg-transparent"}),(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:j?(0,a.jsx)("div",{className:"h-8 w-8 rounded-full avatar-pulse"}):D?(0,a.jsx)("div",{className:"relative h-8 w-8 rounded-full overflow-hidden",children:(0,a.jsx)(x.default,{src:D,alt:P,fill:!0,sizes:"32px",style:{objectFit:"cover"}})}):(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(0,a.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("path",{d:"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",title:P,children:P}),E&&(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",title:E,children:E})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("a",{href:"/account",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsxs)("svg",{className:"mr-3 h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,a.jsx)("circle",{cx:"12",cy:"7",r:"4"})]}),f("accountPreferences","Account Preferences")]}),(0,a.jsxs)("a",{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(m,{className:"mr-3 h-5 w-5 text-gray-400","aria-hidden":"true"}),f("settings","Settings")]})]}),(0,a.jsx)("div",{className:"py-1 border-t border-gray-100",children:(0,a.jsxs)("button",{onClick:M,className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsxs)("svg",{className:"mr-3 h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),(0,a.jsx)("polyline",{points:"16 17 21 12 16 7"}),(0,a.jsx)("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),f("logout","Sign out")]})}),(0,a.jsx)("div",{className:"py-1 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"px-4 py-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-500 mb-2",children:f("theme","Theme")}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light"),N("light")},className:"p-2 rounded-md bg-white border ".concat("light"===S?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"," hover:bg-gray-50"),"aria-label":f("lightTheme","Light theme"),children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ".concat("light"===S?"text-blue-600":"text-gray-700"),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"5"}),(0,a.jsx)("line",{x1:"12",y1:"1",x2:"12",y2:"3"}),(0,a.jsx)("line",{x1:"12",y1:"21",x2:"12",y2:"23"}),(0,a.jsx)("line",{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}),(0,a.jsx)("line",{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}),(0,a.jsx)("line",{x1:"1",y1:"12",x2:"3",y2:"12"}),(0,a.jsx)("line",{x1:"21",y1:"12",x2:"23",y2:"12"}),(0,a.jsx)("line",{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}),(0,a.jsx)("line",{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"})]})}),(0,a.jsx)("button",{onClick:()=>{document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark"),N("dark")},className:"p-2 rounded-md bg-gray-900 border ".concat("dark"===S?"border-blue-500 ring-2 ring-blue-500 ring-opacity-50":"border-gray-700"," hover:bg-gray-800"),"aria-label":f("darkTheme","Dark theme"),children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ".concat("dark"===S?"text-blue-400":"text-gray-200"),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,a.jsx)("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"})})}),(0,a.jsx)("button",{onClick:()=>{window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("theme","system"),N("system")},className:"p-2 rounded-md bg-gray-100 border ".concat("system"===S?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"," hover:bg-gray-200"),"aria-label":f("systemTheme","System theme"),children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ".concat("system"===S?"text-blue-600":"text-gray-700"),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}),(0,a.jsx)("line",{x1:"8",y1:"21",x2:"16",y2:"21"}),(0,a.jsx)("line",{x1:"12",y1:"17",x2:"12",y2:"21"})]})})]})]})})]})]})};function f(e){let{children:t}=e,{t:n}=(0,l.Bd)("common"),{user:r,isLoading:c}=(0,y.A)(),u=(0,i.usePathname)();(0,o.useEffect)(()=>{let e=document.createElement("style");return e.innerHTML="\n@keyframes authLoadingPulse {\n  0% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n  50% {\n    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */\n  }\n  100% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n}\n\n.auth-loading-pulse {\n  animation: authLoadingPulse 1.5s ease-in-out infinite;\n}\n\n.dark .auth-loading-pulse {\n  animation-name: authLoadingPulseDark;\n}\n\n@keyframes authLoadingPulseDark {\n  0% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n  50% {\n    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */\n  }\n  100% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n}\n",document.head.appendChild(e),()=>{document.head.removeChild(e)}},[]),(0,o.useEffect)(()=>{console.log("ClientLayout: isLoading:",c),console.log("ClientLayout: user object:",r)},[c,r]);let d=e=>"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(u===e?"bg-gray-200 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-blue-600");return(0,a.jsx)("html",{lang:"en",children:(0,a.jsxs)("body",{className:"antialiased",children:[(0,a.jsx)("header",{className:"main-header shadow-md fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-white/90",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16 items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)(s(),{href:"/",className:"text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors",children:n("siteName")})}),(0,a.jsxs)("div",{className:"flex items-center justify-between flex-1 pl-6 md:pl-10 lg:pl-16",children:[" ",(0,a.jsxs)("nav",{className:"hidden md:flex space-x-6 lg:space-x-8 items-baseline",children:[" ",(0,a.jsx)(s(),{href:"/",className:d("/"),children:n("home")}),(0,a.jsx)(s(),{href:"/convert",className:d("/convert"),children:n("convert")}),(0,a.jsx)(s(),{href:"/pricing",className:d("/pricing"),children:n("pricing")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 md:space-x-4 lg:space-x-5",children:[(0,a.jsx)(h,{}),c?(0,a.jsx)("div",{className:"h-10 w-10 rounded-full auth-loading-pulse"}):(0,a.jsx)(b,{user:r})]})]})]})})}),(0,a.jsxs)("main",{className:"pt-16",children:[" ",t]}),(0,a.jsx)("footer",{className:"bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"border-t border-gray-200 py-8 text-center text-sm text-gray-500",children:(0,a.jsx)("p",{children:n("footer",{year:new Date().getFullYear()})})})})})]})})}let w=e=>{let{children:t}=e;return(0,a.jsx)(u,{children:(0,a.jsx)(f,{children:t})})}},4983:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var a=n(9724);let o=null,r=()=>{if(o)return o;let e="https://rpzceoedurujspnnyvkm.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJwemNlb2VkdXJ1anNwbm55dmttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1OTkwNDgsImV4cCI6MjA2MjE3NTA0OH0._zuv48OJiram1Q_QXndbl6exkL3P8qrgI_MfybQzCEo";if(!e)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");if(!t)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");return o=(0,a.UU)(e,t,{auth:{debug:!0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{fetch:async(e,t)=>{console.log("[Supabase Debug] Fetch request to: ".concat(e)),console.log("[Supabase Debug] Fetch options:",t);try{let n=await fetch(e,t);return console.log("[Supabase Debug] Response status: ".concat(n.status)),n}catch(e){throw console.error("[Supabase Debug] Fetch error:",e),e}}}}),console.log("[Supabase Debug] Client initialized with URL:",e),o}},5695:(e,t,n)=>{"use strict";var a=n(8999);n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}})},7190:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var a=n(7985),o=n(1218);let r={common:{siteName:"All to Markdown",home:"Home",convert:"Convert",pricing:"Pricing",login:"Login",logout:"Logout",profile:"Profile",subscription:"Subscription",user:"User",footer:"\xa9 {{year}} All to Markdown. All rights reserved.",languageSwitcher:"Language",english:"English",chinese:"中文",userGroupPlaceholder:"N/A",paymentStatusPlaceholder:"N/A",openUserMenu:"Open user menu",userGroup:"User Group",paymentStatus:"Payment Status"},home:{title:"Convert Documents to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.",startConverting:"Start Converting",viewPricing:"View Pricing",aiTitle:"Why Markdown is Perfect for AI",aiDescription:"Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.",clearStructure:"Clear Structure",clearStructureDesc:"Markdown's simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.",enhancedResponses:"Enhanced AI Responses",enhancedResponsesDesc:"When feeding Markdown to AI like ChatGPT or Claude, you'll get more accurate responses because the AI can better understand context and relationships in your content.",noFormatting:"No Formatting Noise",noFormattingDesc:"Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.",convertAnyFormat:"Convert any document format to clean Markdown",convertDesc:"Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.",supportedFormats:"Supported File Formats",easyUpload:"Easy File Upload",easyUploadDesc:"Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.",advancedOptions:"Advanced Options",advancedOptionsDesc:"Customize your Markdown output with options for different Markdown flavors, image handling, and more.",batchProcessing:"Batch Processing",batchProcessingDesc:"Convert multiple files at once and download them individually or as a zip archive.",fastConversion:"Fast Conversion",fastConversionDesc:"Our optimized conversion engine processes your documents quickly, saving you time and effort."},convert:{title:"Convert to AI-Friendly Markdown",description:"Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.",aiTip:"AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.",startConversion:"Start Conversion",options:"Conversion Options",markdownFlavor:"Markdown Flavor",markdownFlavorDesc:"Select the Markdown specification to follow",aiOptimized:"AI-Optimized Format",aiOptimizedDesc:"Optimize output for AI models like ChatGPT and Claude",advancedOptions:"Advanced Options",imageHandling:"Image Handling",imageHandlingDesc:"How to handle images in the converted Markdown",enableImageDescription:"Generate descriptive text for images",enableImageDescriptionDesc:"Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.",imageDescriptionAttachmentMode:"Description Attachment Method:",attachmentModeKeepImage:"Keep Image",attachmentModeReplaceImage:"Replace Image",tableHandling:"Table Handling",tableHandlingDesc:"Table formatting style in the converted Markdown",successMessage:"Your file has been successfully converted to Markdown!",successMessagePlural:"{{count}} files have been successfully converted to Markdown!",aiSuccessTip:"Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.",download:"Download .md",downloadAll:"Download All (.zip)"},pricing:{title:"Pricing",subtitle:"Pricing plans for all needs",description:"Choose the perfect plan for your document conversion needs. All plans include our core conversion features.",free:"Free",pro:"Pro",enterprise:"Enterprise",mostPopular:"Most popular",monthly:"/month",yearly:"/year",getStarted:"Get started",subscribe:"Subscribe"},auth:{loginTitle:"Login",loginDescription:"Login to your account",emailLabel:"Email",passwordLabel:"Password",loginButton:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",signUpLink:"Sign up",registerTitle:"Sign Up",registerDescription:"Create a new account",confirmPasswordLabel:"Confirm Password",registerButton:"Sign Up",hasAccount:"Already have an account?",signInLink:"Sign in",orContinueWith:"Or continue with",github:"GitHub",google:"Google",magicLinkSent:"Magic link sent!",checkYourEmail:"Check your email for the magic link to login."}},s={common:{siteName:"All to Markdown",home:"首页",convert:"转换",pricing:"价格",login:"登录",logout:"退出登录",profile:"个人资料",subscription:"订阅状态",user:"用户",footer:"\xa9 {{year}} All to Markdown. 保留所有权利。",languageSwitcher:"语言",english:"English",chinese:"中文",userGroupPlaceholder:"暂无",paymentStatusPlaceholder:"暂无",openUserMenu:"打开用户菜单",userGroup:"用户组",paymentStatus:"支付状态",register:"注册"},home:{title:"将文档转换为AI友好的Markdown格式",description:"将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。",startConverting:"开始转换",viewPricing:"查看价格",aiTitle:"为什么Markdown对AI来说是完美的",aiDescription:"Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。",clearStructure:"清晰的结构",clearStructureDesc:"Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。",enhancedResponses:"增强的AI响应",enhancedResponsesDesc:"当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。",noFormatting:"没有格式噪音",noFormattingDesc:"与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。",convertAnyFormat:"将任何文档格式转换为清晰的Markdown",convertDesc:"我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。",supportedFormats:"支持的文件格式",easyUpload:"轻松上传文件",easyUploadDesc:"拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。",advancedOptions:"高级选项",advancedOptionsDesc:"使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。",batchProcessing:"批量处理",batchProcessingDesc:"一次转换多个文件，并单独下载或作为zip存档下载。",fastConversion:"快速转换",fastConversionDesc:"我们优化的转换引擎快速处理您的文档，节省您的时间和精力。"},convert:{title:"转换为AI友好的Markdown",description:"上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。",aiTip:"AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。",startConversion:"开始转换",options:"转换选项",markdownFlavor:"Markdown风格",markdownFlavorDesc:"选择要遵循的Markdown规范",aiOptimized:"AI优化格式",aiOptimizedDesc:"为ChatGPT和Claude等AI模型优化输出",advancedOptions:"高级选项",imageHandling:"图像处理",imageHandlingDesc:"如何处理转换后的Markdown中的图像",enableImageDescription:"为图片生成描述性文字",enableImageDescriptionDesc:"为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。",imageDescriptionAttachmentMode:"描述文字附加方式:",attachmentModeKeepImage:"保留图片",attachmentModeReplaceImage:"替换图片",tableHandling:"表格处理",tableHandlingDesc:"转换后的Markdown中的表格格式样式",successMessage:"您的文件已成功转换为Markdown！",successMessagePlural:"{{count}}个文件已成功转换为Markdown！",aiSuccessTip:"您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。",download:"下载.md",downloadAll:"下载全部(.zip)"},pricing:{title:"价格",subtitle:"满足所有需求的价格计划",description:"选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。",free:"免费版",pro:"专业版",enterprise:"企业版",mostPopular:"最受欢迎",monthly:"/月",yearly:"/年",getStarted:"开始使用",subscribe:"订阅"},auth:{loginTitle:"登录",loginDescription:"登录您的账户",emailLabel:"邮箱",passwordLabel:"密码",loginButton:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账户？",signUpLink:"注册",registerTitle:"注册",registerDescription:"创建一个新账户",confirmPasswordLabel:"确认密码",registerButton:"注册",hasAccount:"已有账户？",signInLink:"登录",orContinueWith:"或继续使用",github:"GitHub",google:"Google",magicLinkSent:"魔法链接已发送！",checkYourEmail:"请检查您的邮箱以获取魔法链接进行登录。"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["common","auth"],n=(0,a.Q_)();return n.use(o.r9).init({lng:e,ns:t,fallbackLng:"en",interpolation:{escapeValue:!1},resources:{en:{...r},zh:{...s}}}),n}},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[533,218,897,985,874,766,441,684,358],()=>t(1328)),_N_E=e.O()}]);