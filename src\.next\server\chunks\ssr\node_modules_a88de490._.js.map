{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/native.js"], "sourcesContent": ["import { randomUUID } from 'crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,YAAA,qGAAA,CAAA,aAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/rng.js"], "sourcesContent": ["import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,CAAA,GAAA,qGAAA,CAAA,iBAAc,AAAD,EAAE;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,4IAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/uuid/dist/esm/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAG,AAAD;IACpD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "file": "useLayoutEffect.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,4CAAkB,OAAO,aAAa,cAC/C,CAAA,yMAAA,UAAI,EAAE,eAAe,GACrB,KAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "file": "useEffectEvent.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAKM,SAAS,0CAAmC,EAAM;IACvD,MAAM,MAAM,CAAA,yMAAA,SAAK,EAAwB;IACzC,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,aAAa;IACb,OAAO,CAAA,yMAAA,cAAU,EAAK,CAAC,GAAG;QACxB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,KAAO;IAChB,GAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "file": "isFocusable.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["const focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR);\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC;AACzB;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "file": "domHelpers.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,4CAAmB,CAAC;QACxB;IAAP,OAAO,CAAA,oBAAA,OAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAI,aAAa,MAAA,QAAjB,sBAAA,KAAA,IAAA,oBAAqB;AAC9B;AAEO,MAAM,4CAAiB,CAC5B;IAEA,IAAI,MAAM,YAAY,MAAM,GAAG,MAAM,KAAK,IACxC,OAAO;IAGT,MAAM,MAAM,0CAAiB;IAC7B,OAAO,IAAI,WAAW,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAc;IAC5B,OAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,QAAQ,KAAK;AACxC;AAKO,SAAS,0CAAa,IAAiB;IAC5C,OAAO,6BAAO,SACZ,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "file": "focusWithoutScrolling.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/focusWithoutScrolling.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAoBM,SAAS,0CAAsB,OAAyB;IAC7D,IAAI,+CACF,QAAQ,KAAK,CAAC;QAAC,eAAe;IAAI;SAC7B;QACL,IAAI,qBAAqB,4CAAsB;QAC/C,QAAQ,KAAK;QACb,4CAAsB;IACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS;IACP,IAAI,qDAA+B,MAAM;QACvC,oDAA8B;QAC9B,IAAI;YACF,IAAI,YAAY,SAAS,aAAa,CAAC;YACvC,UAAU,KAAK,CAAC;gBACd,IAAI,iBAAgB;oBAClB,oDAA8B;oBAC9B,OAAO;gBACT;YACF;QACF,EAAE,OAAM;QACN,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,OAAyB;IACtD,IAAI,SAAS,QAAQ,UAAU;IAC/B,IAAI,qBAA0C,EAAE;IAChD,IAAI,uBAAuB,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAEhF,MAAO,kBAAkB,eAAe,WAAW,qBAAsB;QACvE,IACE,OAAO,YAAY,GAAG,OAAO,YAAY,IACzC,OAAO,WAAW,GAAG,OAAO,WAAW,EAEvC,mBAAmB,IAAI,CAAC;YACtB,SAAS;YACT,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;QAC/B;QAEF,SAAS,OAAO,UAAU;IAC5B;IAEA,IAAI,gCAAgC,aAClC,mBAAmB,IAAI,CAAC;QACtB,SAAS;QACT,WAAW,qBAAqB,SAAS;QACzC,YAAY,qBAAqB,UAAU;IAC7C;IAGF,OAAO;AACT;AAEA,SAAS,4CAAsB,kBAAuC;IACpE,KAAK,IAAI,EAAA,SAAC,OAAO,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAC,IAAI,mBAAoB;QAC/D,QAAQ,SAAS,GAAG;QACpB,QAAQ,UAAU,GAAG;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOM,SAAS,yCAA+C,WAAkB;IAC/E,IAAI,QAAQ;IACZ,MAAM,WAAW,GAAG;IACpB,MAAM,kBAAkB,GAAG,IAAM,MAAM,gBAAgB;IACvD,2FAA2F;IAC3F,MAAM,oBAAoB,GAAG,IAAO,MAAc,YAAY;IAC9D,MAAM,OAAO,GAAG,KAAO;IACvB,OAAO;AACT;AAEO,SAAS,0CAAe,KAAY,EAAE,MAAe;IAC1D,OAAO,cAAc,CAAC,OAAO,UAAU;QAAC,OAAO;IAAM;IACrD,OAAO,cAAc,CAAC,OAAO,iBAAiB;QAAC,OAAO;IAAM;AAC9D;AAEO,SAAS,0CAAwD,MAA4C;IAClH,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAE;QACpB,WAAW;QACX,UAAU;IACZ;IAEA,mDAAmD;IAEnD,CAAA,wKAAA,kBAAc,EAAE;QACd,MAAM,QAAQ,SAAS,OAAO;QAC9B,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,QAAQ,CAAC,UAAU;gBACzB,MAAM,QAAQ,GAAG;YACnB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,eAAe,CAAA,uKAAA,iBAAa,EAAE,CAAC;QACjC,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAA,OAAS;IACX;IAEA,wDAAwD;IACxD,OAAO,CAAA,yMAAA,cAAU,EAAE,CAAC;QAClB,wGAAwG;QACxG,sGAAsG;QACtG,6FAA6F;QAC7F,qGAAqG;QACrG,IACE,EAAE,MAAM,YAAY,qBACpB,EAAE,MAAM,YAAY,oBACpB,EAAE,MAAM,YAAY,uBACpB,EAAE,MAAM,YAAY,mBACpB;YACA,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,IAAI,SAAS,EAAE,MAAM;YACrB,IAAI,gBAA2D,CAAC;gBAC9D,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,IAAI,OAAO,QAAQ,EAAE;oBACnB,uEAAuE;oBACvE,IAAI,QAAQ,yCAA8C;oBAC1D,aAAa;gBACf;gBAEA,qEAAqE;gBACrE,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;oBAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAC9B;YACF;YAEA,OAAO,gBAAgB,CAAC,YAAY,eAAe;gBAAC,MAAM;YAAI;YAE9D,SAAS,OAAO,CAAC,QAAQ,GAAG,IAAI,iBAAiB;gBAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE;wBACjD;qBAAA,6BAAA,SAAS,OAAO,CAAC,QAAQ,MAAA,QAAzB,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAA2B,UAAU;oBACrC,IAAI,kBAAkB,WAAW,SAAS,aAAa,GAAG,OAAO,SAAS,aAAa;oBACvF,OAAO,aAAa,CAAC,IAAI,WAAW,QAAQ;wBAAC,eAAe;oBAAe;oBAC3E,OAAO,aAAa,CAAC,IAAI,WAAW,YAAY;wBAAC,SAAS;wBAAM,eAAe;oBAAe;gBAChG;YACF;YAEA,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAAC,YAAY;gBAAM,iBAAiB;oBAAC;iBAAW;YAAA;QAC5F;IACF,GAAG;QAAC;KAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,MAA+B;IAC1D,uEAAuE;IACvE,MAAO,UAAU,CAAC,CAAA,oKAAA,cAAU,EAAE,QAC5B,SAAS,OAAO,aAAa;IAG/B,IAAI,SAAS,CAAA,mKAAA,iBAAa,EAAE;IAC5B,IAAI,gBAAgB,OAAO,QAAQ,CAAC,aAAa;IACjD,IAAI,CAAC,iBAAiB,kBAAkB,QACtC;IAGF,4CAAmB;IACnB,IAAI,eAAe;IACnB,IAAI,SAAS,CAAC;QACZ,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAChC,EAAE,wBAAwB;IAE9B;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAAc;YAC9C,EAAE,wBAAwB;YAE1B,qEAAqE;YACrE,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC5B,eAAe;gBACf,CAAA,8KAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,EAAE,MAAM,KAAK,UAAU,cACzB,EAAE,wBAAwB;IAE9B;IAEA,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,MAAM,KAAK,UAAU,cAAc;YACvC,EAAE,wBAAwB;YAE1B,IAAI,CAAC,cAAc;gBACjB,eAAe;gBACf,CAAA,8KAAA,wBAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,QAAQ,QAAQ;IACxC,OAAO,gBAAgB,CAAC,YAAY,YAAY;IAChD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAC9C,OAAO,gBAAgB,CAAC,SAAS,SAAS;IAE1C,IAAI,UAAU;QACZ,qBAAqB;QACrB,OAAO,mBAAmB,CAAC,QAAQ,QAAQ;QAC3C,OAAO,mBAAmB,CAAC,YAAY,YAAY;QACnD,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,SAAS,SAAS;QAC7C,4CAAmB;QACnB,eAAe;IACjB;IAEA,IAAI,MAAM,sBAAsB;IAChC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "file": "platform.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  return (\n    window.navigator['userAgentData']?.brands.some((brand: {brand: string, version: string}) => re.test(brand.brand))\n  ) ||\n  re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n  \n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;AAED,SAAS,oCAAc,EAAU;QAK7B;IAJF,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,MACvD,OAAO;IAET,OAAO,CAAA,CACL,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,MAAM,CAAC,IAAI,CAAC,CAAC,QAA4C,GAAG,IAAI,CAAC,MAAM,KAAK,EAAA,KAEjH,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AACpC;AAEA,SAAS,mCAAa,EAAU;QAElB;IADZ,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OACxD,GAAG,IAAI,CAAC,CAAA,CAAA,kCAAA,OAAO,SAAS,CAAC,gBAAgB,MAAA,QAAjC,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAmC,QAAQ,KAAI,OAAO,SAAS,CAAC,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,EAAiB;IAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAC3B,OAAO;;IAAA;IAGT,IAAI,MAAsB;IAC1B,OAAO;QACL,IAAI,OAAO,MACT,MAAM;QAER,OAAO;IACT;AACF;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,mCAAa;AACtB;AAEO,MAAM,2CAAW,6BAAO;IAC7B,OAAO,mCAAa;AACtB;AAEO,MAAM,4CAAS,6BAAO;IAC3B,OAAO,mCAAa,aAClB,yFAAyF;IACxF,+CAAW,UAAU,cAAc,GAAG;AAC3C;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,8CAAc;AACvB;AAEO,MAAM,4CAAgB,6BAAO;IAClC,OAAO,+CAAW;AACpB;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc,mBAAmB,CAAC;AAC3C;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "file": "isVirtualEvent.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isVirtualEvent.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAeM,SAAS,0CAAe,KAAgC;IAC7D,0BAA0B;IAC1B,IAAK,MAAc,cAAc,KAAK,KAAK,MAAM,SAAS,EACxD,OAAO;IAGT,oIAAoI;IACpI,oIAAoI;IACpI,qCAAqC;IACrC,IAAI,CAAA,iKAAA,YAAQ,OAAQ,MAAuB,WAAW,EACpD,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IAGrD,OAAO,MAAM,MAAM,KAAK,KAAK,CAAE,MAAuB,WAAW;AACnE;AAEO,SAAS,0CAAsB,KAAmB;IACvD,yEAAyE;IACzE,uFAAuF;IACvF,kGAAkG;IAClG,mHAAmH;IACnH,oHAAoH;IACpH,8DAA8D;IAC9D,OACG,CAAC,CAAA,iKAAA,YAAQ,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KACtD,MAAM,KAAK,KAAK,KACf,MAAM,MAAM,KAAK,KACjB,MAAM,QAAQ,KAAK,KACnB,MAAM,MAAM,KAAK,KACjB,MAAM,WAAW,KAAK;AAG5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "file": "SSRProvider.module.js.map", "sourceRoot": "../../../../", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/ssr/dist/packages/%40react-aria/ssr/src/SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,0FAA0F;AAC1F,2DAA2D;AAC3D,wDAAwD;AAcxD,iFAAiF;AACjF,kFAAkF;AAClF,+EAA+E;AAC/E,+EAA+E;AAC/E,2DAA2D;AAC3D,MAAM,uCAAkC;IACtC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,SAAS;AACX;AAEA,MAAM,mCAAA,WAAA,GAAa,CAAA,yMAAA,UAAI,EAAE,aAAa,CAAkB;AACxD,MAAM,qCAAA,WAAA,GAAe,CAAA,yMAAA,UAAI,EAAE,aAAa,CAAC;AAOzC,mCAAmC;AACnC,SAAS,wCAAkB,KAAuB;IAChD,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IACrB,IAAI,UAAU,iCAAW,QAAQ;IACjC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,yMAAA,WAAO,EAAE;IACjC,IAAI,QAAyB,CAAA,yMAAA,UAAM,EAAE,IAAO,CAAA;YAC1C,iFAAiF;YACjF,oCAAoC;YACpC,QAAQ,QAAQ,uCAAiB,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS;YAChE,SAAS;QACX,CAAA,GAAI;QAAC;QAAK;KAAQ;IAElB,qEAAqE;IACrE,yEAAyE;IACzE,IAAI,OAAO,aAAa,aAEtB,AADA,sDACsD,iBADiB;IAEvE,sDAAsD;IACtD,CAAA,yMAAA,kBAAc,EAAE;QACd,SAAS;IACX,GAAG,EAAE;IAGP,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,iCAAW,QAAQ,EAAA;QAAC,OAAO;qBAC1B,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,mCAAa,QAAQ,EAAA;QAAC,OAAO;OAC3B,MAAM,QAAQ;AAIvB;AAEA,IAAI,+CAAyB;AAMtB,SAAS,0CAAY,KAAuB;IACjD,IAAI,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,YAAY;QACxC,IAAI,QAAQ,GAAG,CAAC,QAAQ,gCAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,gBAAgB,CAAC,8CAAwB;YACvG,QAAQ,IAAI,CAAC;YACb,+CAAyB;QAC3B;QACA,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAA,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,QAAA,EAAA,MAAG,MAAM,QAAQ;IAC1B;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,yCAAsB;AAChC;AAEA,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAG/B,IAAI,qCAAe,IAAI;AAEvB,SAAS,iCAAW,aAAa,KAAK;IACpC,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IACrB,IAAI,MAAM,CAAA,yMAAA,SAAK,EAAiB;IAChC,gDAAgD;IAChD,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC,YAAY;YAWpB,6EAAA;QAVnB,0GAA0G;QAC1G,wGAAwG;QACxG,uGAAuG;QACvG,+GAA+G;QAC/G,gHAAgH;QAChH,uHAAuH;QACvH,2GAA2G;QAC3G,yGAAyG;QACzG,gFAAgF;QAChF,aAAa;QACb,IAAI,eAAA,CAAe,4DAAA,CAAA,yMAAA,UAAI,EAAE,kDAAkD,MAAA,QAAxD,8DAAA,KAAA,IAAA,KAAA,IAAA,CAAA,8EAAA,0DAA0D,iBAAiB,MAAA,QAA3E,gFAAA,KAAA,IAAA,KAAA,IAAA,4EAA6E,OAAO;QACvG,IAAI,cAAc;YAChB,IAAI,qBAAqB,mCAAa,GAAG,CAAC;YAC1C,IAAI,sBAAsB,MACxB,AACA,mCAAa,GAAG,CAAC,cAAc,mCADyD;gBAEtF,IAAI,IAAI,OAAO;gBACf,OAAO,aAAa,aAAa;YACnC;iBACK,IAAI,aAAa,aAAa,KAAK,mBAAmB,KAAK,EAAE;gBAClE,+DAA+D;gBAC/D,8DAA8D;gBAC9D,sCAAsC;gBACtC,IAAI,OAAO,GAAG,mBAAmB,EAAE;gBACnC,mCAAa,MAAM,CAAC;YACtB;QACF;QAEA,gDAAgD;QAChD,IAAI,OAAO,GAAG,EAAE,IAAI,OAAO;IAC7B;IAEA,gDAAgD;IAChD,OAAO,IAAI,OAAO;AACpB;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IAErB,4EAA4E;IAC5E,yDAAyD;IACzD,IAAI,QAAQ,wCAAkB,CAAC,mCAAa,QAAQ,GAAG,CAAC,QAAQ,gCAAK,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,iCAAW,CAAC,CAAC;IAC3B,IAAI,SAAS,QAAQ,wCAAkB,QAAQ,GAAG,CAAC,QAAQ,KAAK,IAAwB,CAAC,IAAhB,MAA0B,EAAE,IAAI,MAAM,EAAE;IACjH,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,SAAS;AAC5C;AAEA,SAAS,yCAAmB,SAAkB;IAC5C,IAAI,KAAK,CAAA,yMAAA,UAAI,EAAE,KAAK;IACpB,IAAI,CAAC,OAAO,GAAG,CAAA,yMAAA,WAAO,EAAE;IACxB,IAAI,SAAS,UAAU,QAAQ,GAAG,CAAC,QAAQ,gCAAK,SAAS,eAAe,CAAC,UAAU,EAAE,qCAAe,MAAM,EAAE;IAC5G,OAAO,aAAa,GAAG,OAAO,CAAC,EAAE,IAAI;AACvC;AAIO,MAAM,4CAAe,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,QAAQ,KAAK,aAAa,2CAAqB;AAExF,SAAS;IACP,OAAO;AACT;AAEA,SAAS;IACP,OAAO;AACT;AAEA,6DAA6D;AAC7D,SAAS,gCAAU,aAAyB;IAC1C,OAAO;IACP,OAAO,KAAO;AAChB;AAOO,SAAS;IACd,iGAAiG;IACjG,IAAI,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,uBAAuB,KAAK,YAC3C,OAAO,CAAA,yMAAA,UAAI,CAAC,CAAC,uBAAuB,CAAC,iCAAW,mCAAa;IAG/D,sDAAsD;IACtD,OAAO,CAAA,yMAAA,aAAS,EAAE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "file": "useFocusVisible.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusVisible.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {getOwnerDocument, getOwnerWindow, isMac, isVirtualClick} from '@react-aria/utils';\nimport {ignoreFocusEvent} from './utils';\nimport {useEffect, useState} from 'react';\nimport {useIsSSR} from '@react-aria/ssr';\n\nexport type Modality = 'keyboard' | 'pointer' | 'virtual';\ntype HandlerEvent = PointerEvent | MouseEvent | KeyboardEvent | FocusEvent | null;\ntype Handler = (modality: Modality, e: HandlerEvent) => void;\nexport type FocusVisibleHandler = (isFocusVisible: boolean) => void;\nexport interface FocusVisibleProps {\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusVisibleResult {\n  /** Whether keyboard focus is visible globally. */\n  isFocusVisible: boolean\n}\n\nlet currentModality: null | Modality = null;\nlet changeHandlers = new Set<Handler>();\ninterface GlobalListenerData {\n  focus: () => void\n}\nexport let hasSetupGlobalListeners = new Map<Window, GlobalListenerData>(); // We use a map here to support setting event listeners across multiple document objects.\nlet hasEventBeforeFocus = false;\nlet hasBlurredWindowRecently = false;\n\n// Only Tab or Esc keys will make focus visible on text input elements\nconst FOCUS_VISIBLE_INPUT_KEYS = {\n  Tab: true,\n  Escape: true\n};\n\nfunction triggerChangeHandlers(modality: Modality, e: HandlerEvent) {\n  for (let handler of changeHandlers) {\n    handler(modality, e);\n  }\n}\n\n/**\n * Helper function to determine if a KeyboardEvent is unmodified and could make keyboard focus styles visible.\n */\nfunction isValidKey(e: KeyboardEvent) {\n  // Control and Shift keys trigger when navigating back to the tab with keyboard.\n  return !(e.metaKey || (!isMac() && e.altKey) || e.ctrlKey || e.key === 'Control' || e.key === 'Shift' || e.key === 'Meta');\n}\n\n\nfunction handleKeyboardEvent(e: KeyboardEvent) {\n  hasEventBeforeFocus = true;\n  if (isValidKey(e)) {\n    currentModality = 'keyboard';\n    triggerChangeHandlers('keyboard', e);\n  }\n}\n\nfunction handlePointerEvent(e: PointerEvent | MouseEvent) {\n  currentModality = 'pointer';\n  if (e.type === 'mousedown' || e.type === 'pointerdown') {\n    hasEventBeforeFocus = true;\n    triggerChangeHandlers('pointer', e);\n  }\n}\n\nfunction handleClickEvent(e: MouseEvent) {\n  if (isVirtualClick(e)) {\n    hasEventBeforeFocus = true;\n    currentModality = 'virtual';\n  }\n}\n\nfunction handleFocusEvent(e: FocusEvent) {\n  // Firefox fires two extra focus events when the user first clicks into an iframe:\n  // first on the window, then on the document. We ignore these events so they don't\n  // cause keyboard focus rings to appear.\n  if (e.target === window || e.target === document || ignoreFocusEvent || !e.isTrusted) {\n    return;\n  }\n\n  // If a focus event occurs without a preceding keyboard or pointer event, switch to virtual modality.\n  // This occurs, for example, when navigating a form with the next/previous buttons on iOS.\n  if (!hasEventBeforeFocus && !hasBlurredWindowRecently) {\n    currentModality = 'virtual';\n    triggerChangeHandlers('virtual', e);\n  }\n\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = false;\n}\n\nfunction handleWindowBlur() {\n  if (ignoreFocusEvent) {\n    return;\n  }\n\n  // When the window is blurred, reset state. This is necessary when tabbing out of the window,\n  // for example, since a subsequent focus event won't be fired.\n  hasEventBeforeFocus = false;\n  hasBlurredWindowRecently = true;\n}\n\n/**\n * Setup global event listeners to control when keyboard focus style should be visible.\n */\nfunction setupGlobalFocusEvents(element?: HTMLElement | null) {\n  if (typeof window === 'undefined' || hasSetupGlobalListeners.get(getOwnerWindow(element))) {\n    return;\n  }\n\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n\n  // Programmatic focus() calls shouldn't affect the current input modality.\n  // However, we need to detect other cases when a focus event occurs without\n  // a preceding user event (e.g. screen reader focus). Overriding the focus\n  // method on HTMLElement.prototype is a bit hacky, but works.\n  let focus = windowObject.HTMLElement.prototype.focus;\n  windowObject.HTMLElement.prototype.focus = function () {\n    hasEventBeforeFocus = true;\n    focus.apply(this, arguments as unknown as [options?: FocusOptions | undefined]);\n  };\n\n  documentObject.addEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.addEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.addEventListener('click', handleClickEvent, true);\n\n  // Register focus events on the window so they are sure to happen\n  // before React's event listeners (registered on the document).\n  windowObject.addEventListener('focus', handleFocusEvent, true);\n  windowObject.addEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.addEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.addEventListener('pointermove', handlePointerEvent, true);\n    documentObject.addEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.addEventListener('mousedown', handlePointerEvent, true);\n    documentObject.addEventListener('mousemove', handlePointerEvent, true);\n    documentObject.addEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  // Add unmount handler\n  windowObject.addEventListener('beforeunload', () => {\n    tearDownWindowFocusTracking(element);\n  }, {once: true});\n\n  hasSetupGlobalListeners.set(windowObject, {focus});\n}\n\nconst tearDownWindowFocusTracking = (element, loadListener?: () => void) => {\n  const windowObject = getOwnerWindow(element);\n  const documentObject = getOwnerDocument(element);\n  if (loadListener) {\n    documentObject.removeEventListener('DOMContentLoaded', loadListener);\n  }\n  if (!hasSetupGlobalListeners.has(windowObject)) {\n    return;\n  }\n  windowObject.HTMLElement.prototype.focus = hasSetupGlobalListeners.get(windowObject)!.focus;\n\n  documentObject.removeEventListener('keydown', handleKeyboardEvent, true);\n  documentObject.removeEventListener('keyup', handleKeyboardEvent, true);\n  documentObject.removeEventListener('click', handleClickEvent, true);\n\n  windowObject.removeEventListener('focus', handleFocusEvent, true);\n  windowObject.removeEventListener('blur', handleWindowBlur, false);\n\n  if (typeof PointerEvent !== 'undefined') {\n    documentObject.removeEventListener('pointerdown', handlePointerEvent, true);\n    documentObject.removeEventListener('pointermove', handlePointerEvent, true);\n    documentObject.removeEventListener('pointerup', handlePointerEvent, true);\n  } else if (process.env.NODE_ENV === 'test') {\n    documentObject.removeEventListener('mousedown', handlePointerEvent, true);\n    documentObject.removeEventListener('mousemove', handlePointerEvent, true);\n    documentObject.removeEventListener('mouseup', handlePointerEvent, true);\n  }\n\n  hasSetupGlobalListeners.delete(windowObject);\n};\n\n/**\n * EXPERIMENTAL\n * Adds a window (i.e. iframe) to the list of windows that are being tracked for focus visible.\n *\n * Sometimes apps render portions of their tree into an iframe. In this case, we cannot accurately track if the focus\n * is visible because we cannot see interactions inside the iframe. If you have this in your application's architecture,\n * then this function will attach event listeners inside the iframe. You should call `addWindowFocusTracking` with an\n * element from inside the window you wish to add. We'll retrieve the relevant elements based on that.\n * Note, you do not need to call this for the default window, as we call it for you.\n *\n * When you are ready to stop listening, but you do not wish to unmount the iframe, you may call the cleanup function\n * returned by `addWindowFocusTracking`. Otherwise, when you unmount the iframe, all listeners and state will be cleaned\n * up automatically for you.\n *\n * @param element @default document.body - The element provided will be used to get the window to add.\n * @returns A function to remove the event listeners and cleanup the state.\n */\nexport function addWindowFocusTracking(element?: HTMLElement | null): () => void {\n  const documentObject = getOwnerDocument(element);\n  let loadListener;\n  if (documentObject.readyState !== 'loading') {\n    setupGlobalFocusEvents(element);\n  } else {\n    loadListener = () => {\n      setupGlobalFocusEvents(element);\n    };\n    documentObject.addEventListener('DOMContentLoaded', loadListener);\n  }\n\n  return () => tearDownWindowFocusTracking(element, loadListener);\n}\n\n// Server-side rendering does not have the document object defined\n// eslint-disable-next-line no-restricted-globals\nif (typeof document !== 'undefined') {\n  addWindowFocusTracking();\n}\n\n/**\n * If true, keyboard focus is visible.\n */\nexport function isFocusVisible(): boolean {\n  return currentModality !== 'pointer';\n}\n\nexport function getInteractionModality(): Modality | null {\n  return currentModality;\n}\n\nexport function setInteractionModality(modality: Modality): void {\n  currentModality = modality;\n  triggerChangeHandlers(modality, null);\n}\n\n/**\n * Keeps state of the current modality.\n */\nexport function useInteractionModality(): Modality | null {\n  setupGlobalFocusEvents();\n\n  let [modality, setModality] = useState(currentModality);\n  useEffect(() => {\n    let handler = () => {\n      setModality(currentModality);\n    };\n\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  }, []);\n\n  return useIsSSR() ? null : modality;\n}\n\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n/**\n * If this is attached to text input component, return if the event is a focus event (Tab/Escape keys pressed) so that\n * focus visible style can be properly set.\n */\nfunction isKeyboardFocusEvent(isTextInput: boolean, modality: Modality, e: HandlerEvent) {\n  let document = getOwnerDocument(e?.target as Element);\n  const IHTMLInputElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLInputElement : HTMLInputElement;\n  const IHTMLTextAreaElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLTextAreaElement : HTMLTextAreaElement;\n  const IHTMLElement = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).HTMLElement : HTMLElement;\n  const IKeyboardEvent = typeof window !== 'undefined' ? getOwnerWindow(e?.target as Element).KeyboardEvent : KeyboardEvent;\n\n  // For keyboard events that occur on a non-input element that will move focus into input element (aka ArrowLeft going from Datepicker button to the main input group)\n  // we need to rely on the user passing isTextInput into here. This way we can skip toggling focus visiblity for said input element\n  isTextInput = isTextInput ||\n    (document.activeElement instanceof IHTMLInputElement && !nonTextInputTypes.has(document.activeElement.type)) ||\n    document.activeElement instanceof IHTMLTextAreaElement ||\n    (document.activeElement instanceof IHTMLElement && document.activeElement.isContentEditable);\n  return !(isTextInput && modality === 'keyboard' && e instanceof IKeyboardEvent && !FOCUS_VISIBLE_INPUT_KEYS[e.key]);\n}\n\n/**\n * Manages focus visible state for the page, and subscribes individual components for updates.\n */\nexport function useFocusVisible(props: FocusVisibleProps = {}): FocusVisibleResult {\n  let {isTextInput, autoFocus} = props;\n  let [isFocusVisibleState, setFocusVisible] = useState(autoFocus || isFocusVisible());\n  useFocusVisibleListener((isFocusVisible) => {\n    setFocusVisible(isFocusVisible);\n  }, [isTextInput], {isTextInput});\n\n  return {isFocusVisible: isFocusVisibleState};\n}\n\n/**\n * Listens for trigger change and reports if focus is visible (i.e., modality is not pointer).\n */\nexport function useFocusVisibleListener(fn: FocusVisibleHandler, deps: ReadonlyArray<any>, opts?: {isTextInput?: boolean}): void {\n  setupGlobalFocusEvents();\n\n  useEffect(() => {\n    let handler = (modality: Modality, e: HandlerEvent) => {\n      // We want to early return for any keyboard events that occur inside text inputs EXCEPT for Tab and Escape\n      if (!isKeyboardFocusEvent(!!(opts?.isTextInput), modality, e)) {\n        return;\n      }\n      fn(isFocusVisible());\n    };\n    changeHandlers.add(handler);\n    return () => {\n      changeHandlers.delete(handler);\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAuBlH,IAAI,wCAAmC;AACvC,IAAI,uCAAiB,IAAI;AAIlB,IAAI,4CAA0B,IAAI,OAAmC,yFAAyF;AACrK,IAAI,4CAAsB;AAC1B,IAAI,iDAA2B;AAE/B,sEAAsE;AACtE,MAAM,iDAA2B;IAC/B,KAAK;IACL,QAAQ;AACV;AAEA,SAAS,4CAAsB,QAAkB,EAAE,CAAe;IAChE,KAAK,IAAI,WAAW,qCAClB,QAAQ,UAAU;AAEtB;AAEA;;CAEC,GACD,SAAS,iCAAW,CAAgB;IAClC,gFAAgF;IAChF,OAAO,CAAE,CAAA,EAAE,OAAO,IAAK,CAAC,CAAA,iKAAA,QAAI,OAAO,EAAE,MAAM,IAAK,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,MAAK;AAC1H;AAGA,SAAS,0CAAoB,CAAgB;IAC3C,4CAAsB;IACtB,IAAI,iCAAW,IAAI;QACjB,wCAAkB;QAClB,4CAAsB,YAAY;IACpC;AACF;AAEA,SAAS,yCAAmB,CAA4B;IACtD,wCAAkB;IAClB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,eAAe;QACtD,4CAAsB;QACtB,4CAAsB,WAAW;IACnC;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,IAAI,CAAA,uKAAA,iBAAa,EAAE,IAAI;QACrB,4CAAsB;QACtB,wCAAkB;IACpB;AACF;AAEA,SAAS,uCAAiB,CAAa;IACrC,kFAAkF;IAClF,kFAAkF;IAClF,wCAAwC;IACxC,IAAI,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,YAAY,CAAA,qKAAA,mBAAe,KAAK,CAAC,EAAE,SAAS,EAClF;IAGF,qGAAqG;IACrG,0FAA0F;IAC1F,IAAI,CAAC,6CAAuB,CAAC,gDAA0B;QACrD,wCAAkB;QAClB,4CAAsB,WAAW;IACnC;IAEA,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA,SAAS;IACP,IAAI,qKAAA,mBAAA,EACF;IAGF,6FAA6F;IAC7F,8DAA8D;IAC9D,4CAAsB;IACtB,iDAA2B;AAC7B;AAEA;;CAEC,GACD,SAAS,6CAAuB,OAA4B;IAC1D,IAAI,OAAO,WAAW,eAAe,0CAAwB,GAAG,CAAC,CAAA,mKAAA,iBAAa,EAAE,WAC9E;IAGF,MAAM,eAAe,CAAA,mKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IAExC,0EAA0E;IAC1E,2EAA2E;IAC3E,0EAA0E;IAC1E,6DAA6D;IAC7D,IAAI,QAAQ,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK;IACpD,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;QACzC,4CAAsB;QACtB,MAAM,KAAK,CAAC,IAAI,EAAE;IACpB;IAEA,eAAe,gBAAgB,CAAC,WAAW,2CAAqB;IAChE,eAAe,gBAAgB,CAAC,SAAS,2CAAqB;IAC9D,eAAe,gBAAgB,CAAC,SAAS,wCAAkB;IAE3D,iEAAiE;IACjE,+DAA+D;IAC/D,aAAa,gBAAgB,CAAC,SAAS,wCAAkB;IACzD,aAAa,gBAAgB,CAAC,QAAQ,wCAAkB;IAExD,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,eAAe,0CAAoB;QACnE,eAAe,gBAAgB,CAAC,aAAa,0CAAoB;IACnE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;IAI5C;IAEA,sBAAsB;IACtB,aAAa,gBAAgB,CAAC,gBAAgB;QAC5C,kDAA4B;IAC9B,GAAG;QAAC,MAAM;IAAI;IAEd,0CAAwB,GAAG,CAAC,cAAc;eAAC;IAAK;AAClD;AAEA,MAAM,oDAA8B,CAAC,SAAS;IAC5C,MAAM,eAAe,CAAA,mKAAA,iBAAa,EAAE;IACpC,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IACxC,IAAI,cACF,eAAe,mBAAmB,CAAC,oBAAoB;IAEzD,IAAI,CAAC,0CAAwB,GAAG,CAAC,eAC/B;IAEF,aAAa,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,0CAAwB,GAAG,CAAC,cAAe,KAAK;IAE3F,eAAe,mBAAmB,CAAC,WAAW,2CAAqB;IACnE,eAAe,mBAAmB,CAAC,SAAS,2CAAqB;IACjE,eAAe,mBAAmB,CAAC,SAAS,wCAAkB;IAE9D,aAAa,mBAAmB,CAAC,SAAS,wCAAkB;IAC5D,aAAa,mBAAmB,CAAC,QAAQ,wCAAkB;IAE3D,IAAI,OAAO,iBAAiB,aAAa;QACvC,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,eAAe,0CAAoB;QACtE,eAAe,mBAAmB,CAAC,aAAa,0CAAoB;IACtE,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;IAI5C;IAEA,0CAAwB,MAAM,CAAC;AACjC;AAmBO,SAAS,0CAAuB,OAA4B;IACjE,MAAM,iBAAiB,CAAA,mKAAA,mBAAe,EAAE;IACxC,IAAI;IACJ,IAAI,eAAe,UAAU,KAAK,WAChC,6CAAuB;SAClB;QACL,eAAe;YACb,6CAAuB;QACzB;QACA,eAAe,gBAAgB,CAAC,oBAAoB;IACtD;IAEA,OAAO,IAAM,kDAA4B,SAAS;AACpD;AAEA,kEAAkE;AAClE,iDAAiD;AACjD,IAAI,OAAO,aAAa,aACtB;AAMK,SAAS;IACd,OAAO,0CAAoB;AAC7B;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS,0CAAuB,QAAkB;IACvD,wCAAkB;IAClB,4CAAsB,UAAU;AAClC;AAKO,SAAS;IACd;IAEA,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;QACd;QAEA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,GAAG,EAAE;IAEL,OAAO,CAAA,kKAAA,WAAO,MAAM,OAAO;AAC7B;AAEA,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED;;;CAGC,GACD,SAAS,2CAAqB,WAAoB,EAAE,QAAkB,EAAE,CAAe;IACrF,IAAI,YAAW,CAAA,mKAAA,mBAAe,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM;IACzC,MAAM,oBAAoB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,gBAAgB,GAAG;IAClH,MAAM,uBAAuB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,mBAAmB,GAAG;IACxH,MAAM,eAAe,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,WAAW,GAAG;IACxG,MAAM,iBAAiB,OAAO,WAAW,cAAc,CAAA,mKAAA,iBAAa,EAAE,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,EAAG,MAAM,EAAa,aAAa,GAAG;IAE5G,qKAAqK;IACrK,kIAAkI;IAClI,cAAc,eACX,UAAS,aAAa,YAAY,qBAAqB,CAAC,wCAAkB,GAAG,CAAC,UAAS,aAAa,CAAC,IAAI,KAC1G,UAAS,aAAa,YAAY,wBACjC,UAAS,aAAa,YAAY,gBAAgB,UAAS,aAAa,CAAC,iBAAiB;IAC7F,OAAO,CAAE,CAAA,eAAe,aAAa,cAAc,aAAa,kBAAkB,CAAC,8CAAwB,CAAC,EAAE,GAAG,CAAA;AACnH;AAKO,SAAS,0CAAgB,QAA2B,CAAC,CAAC;IAC3D,IAAI,EAAA,aAAC,WAAW,EAAA,WAAE,SAAS,EAAC,GAAG;IAC/B,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,yMAAA,WAAO,EAAE,aAAa;IACnE,0CAAwB,CAAC;QACvB,gBAAgB;IAClB,GAAG;QAAC;KAAY,EAAE;qBAAC;IAAW;IAE9B,OAAO;QAAC,gBAAgB;IAAmB;AAC7C;AAKO,SAAS,0CAAwB,EAAuB,EAAE,IAAwB,EAAE,IAA8B;IACvH;IAEA,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,UAAU,CAAC,UAAoB;YACjC,0GAA0G;YAC1G,IAAI,CAAC,2CAAqB,CAAC,CAAA,CAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,WAAW,GAAG,UAAU,IACzD;YAEF,GAAG;QACL;QACA,qCAAe,GAAG,CAAC;QACnB,OAAO;YACL,qCAAe,MAAM,CAAC;QACxB;IACF,uDAAuD;IACvD,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "file": "module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-stately/flags/dist/packages/%40react-stately/flags/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAED,IAAI,yCAAmB;AACvB,IAAI,mCAAa;AAEV,SAAS;IACd,yCAAmB;AACrB;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,mCAAa;AACf;AAEO,SAAS;IACd,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "file": "DOMFunctions.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,kIAAkI;AAQ3H,SAAS,0CACd,IAA6B,EAC7B,SAAkC;IAElC,IAAI,CAAC,CAAA,kKAAA,YAAQ,KACX,OAAO,aAAa,OAAO,KAAK,QAAQ,CAAC,aAAa;IAGxD,IAAI,CAAC,QAAQ,CAAC,WACZ,OAAO;IAGT,IAAI,cAAqD;IAEzD,MAAO,gBAAgB,KAAM;QAC3B,IAAI,gBAAgB,MAClB,OAAO;QAGT,IAAK,YAAgC,OAAO,KAAK,UAC9C,YAAgC,YAAY,EAC7C,AACA,cAAe,OADM,KAC0B,YAAY,CAAE,UAAU;aAClE,IAAI,CAAA,mKAAA,eAAW,EAAE,cACtB,AACA,cAAc,YAAY,EADE,EACE;aAE9B,cAAc,YAAY,UAAU;IAExC;IAEA,OAAO;AACT;AAKO,MAAM,4CAAmB,CAAC,MAAgB,QAAQ;QAOvD;IANA,IAAI,CAAC,CAAA,kKAAA,YAAQ,KACX,OAAO,IAAI,aAAa;IAE1B,IAAI,gBAAgC,IAAI,aAAa;IAErD,MAAO,iBAAiB,gBAAgB,iBAAA,CAAA,CACxC,4BAAA,cAAc,UAAU,MAAA,QAAxB,8BAAA,KAAA,IAAA,KAAA,IAAA,0BAA0B,aAAa,EACrC,gBAAgB,cAAc,UAAU,CAAC,aAAa;IAGxD,OAAO;AACT;AAKO,SAAS,0CAAgC,KAAQ;IACtD,IAAI,CAAA,kKAAA,YAAQ,OAAQ,MAAM,MAAM,CAAiB,UAAU,EAAE;QAC3D,IAAI,MAAM,YAAY,EACpB,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAElC;IACA,OAAO,MAAM,MAAM;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "file": "useFocus.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocus.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, FocusableElement, FocusEvents} from '@react-types/shared';\nimport {FocusEvent, useCallback} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument} from '@react-aria/utils';\nimport {useSyntheticBlurEvent} from './utils';\n\nexport interface FocusProps<Target = FocusableElement> extends FocusEvents<Target> {\n  /** Whether the focus events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusResult<Target = FocusableElement> {\n  /** Props to spread onto the target element. */\n  focusProps: DOMAttributes<Target>\n}\n\n/**\n * Handles focus events for the immediate target.\n * Focus events on child elements will be ignored.\n */\nexport function useFocus<Target extends FocusableElement = FocusableElement>(props: FocusProps<Target>): FocusResult<Target> {\n  let {\n    isDisabled,\n    onFocus: onFocusProp,\n    onBlur: onBlurProp,\n    onFocusChange\n  } = props;\n\n  const onBlur: FocusProps<Target>['onBlur'] = useCallback((e: FocusEvent<Target>) => {\n    if (e.target === e.currentTarget) {\n      if (onBlurProp) {\n        onBlurProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(false);\n      }\n\n      return true;\n    }\n  }, [onBlurProp, onFocusChange]);\n\n\n  const onSyntheticFocus = useSyntheticBlurEvent<Target>(onBlur);\n\n  const onFocus: FocusProps<Target>['onFocus'] = useCallback((e: FocusEvent<Target>) => {\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = ownerDocument ? getActiveElement(ownerDocument) : getActiveElement();\n    if (e.target === e.currentTarget && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusProp) {\n        onFocusProp(e);\n      }\n\n      if (onFocusChange) {\n        onFocusChange(true);\n      }\n\n      onSyntheticFocus(e);\n    }\n  }, [onFocusChange, onFocusProp, onSyntheticFocus]);\n\n  return {\n    focusProps: {\n      onFocus: (!isDisabled && (onFocusProp || onFocusChange || onBlurProp)) ? onFocus : undefined,\n      onBlur: (!isDisabled && (onBlurProp || onFocusChange)) ? onBlur : undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAqB3G,SAAS,0CAA6D,KAAyB;IACpG,IAAI,EAAA,YACF,UAAU,EACV,SAAS,WAAW,EACpB,QAAQ,UAAU,EAAA,eAClB,aAAa,EACd,GAAG;IAEJ,MAAM,SAAuC,CAAA,yMAAA,cAAU,EAAE,CAAC;QACxD,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC,IAAI,YACF,WAAW;YAGb,IAAI,eACF,cAAc;YAGhB,OAAO;QACT;IACF,GAAG;QAAC;QAAY;KAAc;IAG9B,MAAM,mBAAmB,CAAA,qKAAA,wBAAoB,EAAU;IAEvD,MAAM,UAAyC,CAAA,yMAAA,cAAU,EAAE,CAAC;QAC1D,kGAAkG;QAClG,oDAAoD;QAEpD,MAAM,gBAAgB,CAAA,mKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,gBAAgB,CAAA,qKAAA,mBAAe,EAAE,iBAAiB,CAAA,qKAAA,mBAAe;QACvF,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,kBAAkB,CAAA,qKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,aACF,YAAY;YAGd,IAAI,eACF,cAAc;YAGhB,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAe;QAAa;KAAiB;IAEjD,OAAO;QACL,YAAY;YACV,SAAU,CAAC,cAAe,CAAA,eAAe,iBAAiB,UAAS,IAAM,UAAU;YACnF,QAAS,CAAC,cAAe,CAAA,cAAc,aAAY,IAAM,SAAS;QACpE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "file": "useGlobalListeners.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useGlobalListeners.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS;IACd,IAAI,kBAAkB,CAAA,yMAAA,SAAK,EAAE,IAAI;IACjC,IAAI,oBAAoB,CAAA,yMAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;QAChE,8EAA8E;QAC9E,IAAI,KAAK,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,IAAI,IAAG,CAAC,GAAG;YAC3B,gBAAgB,OAAO,CAAC,MAAM,CAAC;YAC/B,YAAY;QACd,IAAI;QACJ,gBAAgB,OAAO,CAAC,GAAG,CAAC,UAAU;kBAAC;yBAAM;gBAAa;qBAAI;QAAO;QACrE,YAAY,gBAAgB,CAAC,MAAM,IAAI;IACzC,GAAG,EAAE;IACL,IAAI,uBAAuB,CAAA,yMAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;YAC1D;QAAT,IAAI,KAAK,CAAA,CAAA,+BAAA,gBAAgB,OAAO,CAAC,GAAG,CAAC,SAAA,MAAA,QAA5B,iCAAA,KAAA,IAAA,KAAA,IAAA,6BAAuC,EAAE,KAAI;QACtD,YAAY,mBAAmB,CAAC,MAAM,IAAI;QAC1C,gBAAgB,OAAO,CAAC,MAAM,CAAC;IACjC,GAAG,EAAE;IACL,IAAI,2BAA2B,CAAA,yMAAA,cAAU,EAAE;QACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YACtC,qBAAqB,MAAM,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,MAAM,OAAO;QACxE;IACF,GAAG;QAAC;KAAqB;IAGzB,CAAA,yMAAA,YAAQ,EAAE;QACR,OAAO;IACT,GAAG;QAAC;KAAyB;IAE7B,OAAO;2BAAC;8BAAmB;kCAAsB;IAAwB;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "file": "useFocusWithin.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useFocusWithin.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {createSyntheticEvent, setEventTarget, useSyntheticBlurEvent} from './utils';\nimport {DOMAttributes} from '@react-types/shared';\nimport {FocusEvent, useCallback, useRef} from 'react';\nimport {getActiveElement, getEventTarget, getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\n\nexport interface FocusWithinProps {\n  /** Whether the focus within events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when the target element or a descendant receives focus. */\n  onFocusWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the target element and all descendants lose focus. */\n  onBlurWithin?: (e: FocusEvent) => void,\n  /** Handler that is called when the the focus within state changes. */\n  onFocusWithinChange?: (isFocusWithin: boolean) => void\n}\n\nexport interface FocusWithinResult {\n  /** Props to spread onto the target element. */\n  focusWithinProps: DOMAttributes\n}\n\n/**\n * Handles focus events for the target and its descendants.\n */\nexport function useFocusWithin(props: FocusWithinProps): FocusWithinResult {\n  let {\n    isDisabled,\n    onBlurWithin,\n    onFocusWithin,\n    onFocusWithinChange\n  } = props;\n  let state = useRef({\n    isFocusWithin: false\n  });\n\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let onBlur = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // We don't want to trigger onBlurWithin and then immediately onFocusWithin again\n    // when moving focus inside the element. Only trigger if the currentTarget doesn't\n    // include the relatedTarget (where focus is moving).\n    if (state.current.isFocusWithin && !(e.currentTarget as Element).contains(e.relatedTarget as Element)) {\n      state.current.isFocusWithin = false;\n      removeAllGlobalListeners();\n\n      if (onBlurWithin) {\n        onBlurWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(false);\n      }\n    }\n  }, [onBlurWithin, onFocusWithinChange, state, removeAllGlobalListeners]);\n\n  let onSyntheticFocus = useSyntheticBlurEvent(onBlur);\n  let onFocus = useCallback((e: FocusEvent) => {\n    // Ignore events bubbling through portals.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    // Double check that document.activeElement actually matches e.target in case a previously chained\n    // focus handler already moved focus somewhere else.\n    const ownerDocument = getOwnerDocument(e.target);\n    const activeElement = getActiveElement(ownerDocument);\n    if (!state.current.isFocusWithin && activeElement === getEventTarget(e.nativeEvent)) {\n      if (onFocusWithin) {\n        onFocusWithin(e);\n      }\n\n      if (onFocusWithinChange) {\n        onFocusWithinChange(true);\n      }\n\n      state.current.isFocusWithin = true;\n      onSyntheticFocus(e);\n\n      // Browsers don't fire blur events when elements are removed from the DOM.\n      // However, if a focus event occurs outside the element we're tracking, we\n      // can manually fire onBlur.\n      let currentTarget = e.currentTarget;\n      addGlobalListener(ownerDocument, 'focus', e => {\n        if (state.current.isFocusWithin && !nodeContains(currentTarget, e.target as Element)) {\n          let nativeEvent = new ownerDocument.defaultView!.FocusEvent('blur', {relatedTarget: e.target});\n          setEventTarget(nativeEvent, currentTarget);\n          let event = createSyntheticEvent<FocusEvent>(nativeEvent);\n          onBlur(event);\n        }\n      }, {capture: true});\n    }\n  }, [onFocusWithin, onFocusWithinChange, onSyntheticFocus, addGlobalListener, onBlur]);\n\n  if (isDisabled) {\n    return {\n      focusWithinProps: {\n        // These cannot be null, that would conflict in mergeProps\n        onFocus: undefined,\n        onBlur: undefined\n      }\n    };\n  }\n\n  return {\n    focusWithinProps: {\n      onFocus,\n      onBlur\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AA0B3G,SAAS,0CAAe,KAAuB;IACpD,IAAI,EAAA,YACF,UAAU,EAAA,cACV,YAAY,EAAA,eACZ,aAAa,EAAA,qBACb,mBAAmB,EACpB,GAAG;IACJ,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,eAAe;IACjB;IAEA,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,2KAAA,qBAAiB;IAErE,IAAI,SAAS,CAAA,yMAAA,cAAU,EAAE,CAAC;QACxB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,iFAAiF;QACjF,kFAAkF;QAClF,qDAAqD;QACrD,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAE,EAAE,aAAa,CAAa,QAAQ,CAAC,EAAE,aAAa,GAAc;YACrG,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B;YAEA,IAAI,cACF,aAAa;YAGf,IAAI,qBACF,oBAAoB;QAExB;IACF,GAAG;QAAC;QAAc;QAAqB;QAAO;KAAyB;IAEvE,IAAI,mBAAmB,CAAA,qKAAA,wBAAoB,EAAE;IAC7C,IAAI,UAAU,CAAA,yMAAA,cAAU,EAAE,CAAC;QACzB,0CAA0C;QAC1C,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,kGAAkG;QAClG,oDAAoD;QACpD,MAAM,gBAAgB,CAAA,mKAAA,mBAAe,EAAE,EAAE,MAAM;QAC/C,MAAM,gBAAgB,CAAA,qKAAA,mBAAe,EAAE;QACvC,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,IAAI,kBAAkB,CAAA,qKAAA,iBAAa,EAAE,EAAE,WAAW,GAAG;YACnF,IAAI,eACF,cAAc;YAGhB,IAAI,qBACF,oBAAoB;YAGtB,MAAM,OAAO,CAAC,aAAa,GAAG;YAC9B,iBAAiB;YAEjB,0EAA0E;YAC1E,0EAA0E;YAC1E,4BAA4B;YAC5B,IAAI,gBAAgB,EAAE,aAAa;YACnC,kBAAkB,eAAe,SAAS,CAAA;gBACxC,IAAI,MAAM,OAAO,CAAC,aAAa,IAAI,CAAC,CAAA,qKAAA,eAAW,EAAE,eAAe,EAAE,MAAM,GAAc;oBACpF,IAAI,cAAc,IAAI,cAAc,WAAW,CAAE,UAAU,CAAC,QAAQ;wBAAC,eAAe,EAAE,MAAM;oBAAA;oBAC5F,CAAA,qKAAA,iBAAa,EAAE,aAAa;oBAC5B,IAAI,QAAQ,CAAA,qKAAA,uBAAmB,EAAc;oBAC7C,OAAO;gBACT;YACF,GAAG;gBAAC,SAAS;YAAI;QACnB;IACF,GAAG;QAAC;QAAe;QAAqB;QAAkB;QAAmB;KAAO;IAEpF,IAAI,YACF,OAAO;QACL,kBAAkB;YAChB,0DAA0D;YAC1D,SAAS;YACT,QAAQ;QACV;IACF;IAGF,OAAO;QACL,kBAAkB;qBAChB;oBACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "file": "useFocusRing.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoCO,SAAS,0CAAa,QAA4B,CAAC,CAAC;IACzD,IAAI,EAAA,WACF,YAAY,KAAA,EAAA,aACZ,WAAW,EAAA,QACX,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,WAAW;QACX,gBAAgB,aAAa,CAAA,+KAAA,iBAAa;IAC5C;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,yMAAA,WAAO,EAAE,IAAM,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc;IAEnH,IAAI,cAAc,CAAA,yMAAA,cAAU,EAAE,IAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc,GAAG,EAAE;IAEhH,IAAI,gBAAgB,CAAA,yMAAA,cAAU,EAAE,CAAA;QAC9B,MAAM,OAAO,CAAC,SAAS,GAAG;QAC1B,WAAW;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,+KAAA,0BAAsB,EAAE,CAAC;QACvB,MAAM,OAAO,CAAC,cAAc,GAAG;QAC/B;IACF,GAAG,EAAE,EAAE;qBAAC;IAAW;IAEnB,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAC1B,YAAY;uBACZ;IACF;IAEA,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,8KAAA,iBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB;IACvB;IAEA,OAAO;mBACL;QACA,gBAAgB;QAChB,YAAY,SAAS,mBAAmB;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "file": "useHover.module.js.map", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40react-aria/interactions/dist/packages/%40react-aria/interactions/src/useHover.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;AAiBlH,oGAAoG;AACpG,iFAAiF;AACjF,sDAAsD;AACtD,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS;IACP,wDAAkC;IAElC,wFAAwF;IACxF,sFAAsF;IACtF,wFAAwF;IACxF,oEAAoE;IACpE,WAAW;QACT,wDAAkC;IACpC,GAAG;AACL;AAEA,SAAS,+CAAyB,CAAC;IACjC,IAAI,EAAE,WAAW,KAAK,SACpB;AAEJ;AAEA,SAAS;IACP,IAAI,OAAO,aAAa,aACtB;IAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,gBAAgB,CAAC,aAAa;SAClC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAClC,SAAS,gBAAgB,CAAC,YAAY;;IAAA;IAGxC;IACA,OAAO;QACL;QACA,IAAI,mCAAa,GACf;QAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,mBAAmB,CAAC,aAAa;aACrC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAClC,SAAS,mBAAmB,CAAC,YAAY;;QAAA;IAE7C;AACF;AAMO,SAAS,0CAAS,KAAiB;IACxC,IAAI,EAAA,cACF,YAAY,EAAA,eACZ,aAAa,EAAA,YACb,UAAU,EAAA,YACV,UAAU,EACX,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,IAAI,QAAQ,CAAA,yMAAA,SAAK,EAAE;QACjB,WAAW;QACX,2BAA2B;QAC3B,aAAa;QACb,QAAQ;IACV,GAAG,OAAO;IAEV,CAAA,yMAAA,YAAQ,EAAE,8CAAwB,EAAE;IACpC,IAAI,EAAA,mBAAC,iBAAiB,EAAA,0BAAE,wBAAwB,EAAC,GAAG,CAAA,2KAAA,qBAAiB;IAErE,IAAI,EAAA,YAAC,UAAU,EAAA,iBAAE,eAAe,EAAC,GAAG,CAAA,yMAAA,UAAM,EAAE;QAC1C,IAAI,oBAAoB,CAAC,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,IAAI,cAAc,gBAAgB,WAAW,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxG;YAGF,MAAM,SAAS,GAAG;YAClB,IAAI,SAAS,MAAM,aAAa;YAChC,MAAM,MAAM,GAAG;YAEf,kGAAkG;YAClG,gGAAgG;YAChG,kFAAkF;YAClF,yGAAyG;YACzG,kBAAkB,CAAA,mKAAA,mBAAe,EAAE,MAAM,MAAM,GAAG,eAAe,CAAA;gBAC/D,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,CAAC,CAAA,qKAAA,eAAW,EAAE,MAAM,MAAM,EAAE,EAAE,MAAM,GACzE,gBAAgB,GAAG,EAAE,WAAW;YAEpC,GAAG;gBAAC,SAAS;YAAI;YAEjB,IAAI,cACF,aAAa;gBACX,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,kBAAkB,CAAC,OAAO;YAC5B,IAAI,SAAS,MAAM,MAAM;YACzB,MAAM,WAAW,GAAG;YACpB,MAAM,MAAM,GAAG;YAEf,IAAI,gBAAgB,WAAW,CAAC,MAAM,SAAS,IAAI,CAAC,QAClD;YAGF,MAAM,SAAS,GAAG;YAClB;YAEA,IAAI,YACF,WAAW;gBACT,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,aAA4B,CAAC;QAEjC,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,yDAAmC,EAAE,WAAW,KAAK,SACvD;gBAGF,kBAAkB,GAAG,EAAE,WAAW;YACpC;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClD,gBAAgB,GAAG,EAAE,WAAW;YAEpC;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,UAAQ;;QAkB5C;QACA,OAAO;wBAAC;6BAAY;QAAe;IACrC,GAAG;QAAC;QAAc;QAAe;QAAY;QAAY;QAAO;QAAmB;KAAyB;IAE5G,CAAA,yMAAA,YAAQ,EAAE;QACR,iEAAiE;QACjE,qFAAqF;QACrF,IAAI,YACF,gBAAgB;YAAC,eAAe,MAAM,MAAM;QAAA,GAAG,MAAM,WAAW;IAEpE,uDAAuD;IACvD,GAAG;QAAC;KAAW;IAEf,OAAO;oBACL;mBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/env.js"], "sourcesContent": ["var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,KAAK,IAAE,EAAE,GAAE,GAAE;QAAC,YAAW,CAAC;QAAE,cAAa,CAAC;QAAE,UAAS,CAAC;QAAE,OAAM;IAAC,KAAG,CAAC,CAAC,EAAE,GAAC;AAAE,IAAI,IAAE,CAAC,GAAE,GAAE,IAAI,CAAC,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,IAAG,CAAC;AAAE,MAAM;IAAE,aAAa;QAAC,EAAE,IAAI,EAAC,WAAU,IAAI,CAAC,MAAM;QAAI,EAAE,IAAI,EAAC,gBAAe;QAAW,EAAE,IAAI,EAAC,aAAY;IAAE;IAAC,IAAI,CAAC,EAAC;QAAC,IAAI,CAAC,OAAO,KAAG,KAAG,CAAC,IAAI,CAAC,YAAY,GAAC,WAAU,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,OAAO,GAAC,CAAC;IAAC;IAAC,QAAO;QAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;IAAG;IAAC,SAAQ;QAAC,OAAM,EAAE,IAAI,CAAC,SAAS;IAAA;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,IAAI,WAAU;QAAC,OAAO,IAAI,CAAC,OAAO,KAAG;IAAQ;IAAC,SAAQ;QAAC,OAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;IAAQ;IAAC,UAAS;QAAC,IAAI,CAAC,YAAY,KAAG,aAAW,CAAC,IAAI,CAAC,YAAY,GAAC,UAAU;IAAC;IAAC,IAAI,oBAAmB;QAAC,OAAO,IAAI,CAAC,YAAY,KAAG;IAAU;AAAC;AAAC,IAAI,IAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/owner.js"], "sourcesContent": ["import{env as t}from'./env.js';function o(n){var e,r;return t.isServer?null:n?\"ownerDocument\"in n?n.ownerDocument:\"current\"in n?(r=(e=n.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}export{o as getOwnerDocument};\n"], "names": [], "mappings": ";;;AAAA;;AAA+B,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE;IAAE,OAAO,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,OAAK,IAAE,mBAAkB,IAAE,EAAE,aAAa,GAAC,aAAY,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,OAAK,IAAE,WAAS,OAAK;AAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/micro-task.js"], "sourcesContent": ["function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,OAAO,kBAAgB,aAAW,eAAe,KAAG,QAAQ,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA,IAAG,WAAW;YAAK,MAAM;QAAC;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/disposables.js"], "sourcesContent": ["import{microTask as i}from'./micro-task.js';function o(){let n=[],r={addEventListener(e,t,s,a){return e.addEventListener(t,s,a),r.add(()=>e.removeEventListener(t,s,a))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return i(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,s){let a=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:s}),this.add(()=>{Object.assign(e.style,{[t]:a})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return n.includes(e)||n.push(e),()=>{let t=n.indexOf(e);if(t>=0)for(let s of n.splice(t,1))s()}},dispose(){for(let e of n.splice(0))e()}};return r}export{o as disposables};\n"], "names": [], "mappings": ";;;AAAA;;AAA4C,SAAS;IAAI,IAAI,IAAE,EAAE,EAAC,IAAE;QAAC,kBAAiB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,OAAO,EAAE,gBAAgB,CAAC,GAAE,GAAE,IAAG,EAAE,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,GAAE,GAAE;QAAG;QAAE,uBAAsB,GAAG,CAAC;YAAE,IAAI,IAAE,yBAAyB;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,qBAAqB;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,OAAO,EAAE,qBAAqB,CAAC,IAAI,EAAE,qBAAqB,IAAI;QAAG;QAAE,YAAW,GAAG,CAAC;YAAE,IAAI,IAAE,cAAc;YAAG,OAAO,EAAE,GAAG,CAAC,IAAI,aAAa;QAAG;QAAE,WAAU,GAAG,CAAC;YAAE,IAAI,IAAE;gBAAC,SAAQ,CAAC;YAAC;YAAE,OAAO,CAAA,GAAA,uKAAA,CAAA,YAAC,AAAD,EAAE;gBAAK,EAAE,OAAO,IAAE,CAAC,CAAC,EAAE;YAAE,IAAG,EAAE,GAAG,CAAC;gBAAK,EAAE,OAAO,GAAC,CAAC;YAAC;QAAE;QAAE,OAAM,CAAC,EAAC,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,EAAE,KAAK,CAAC,gBAAgB,CAAC;YAAG,OAAO,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;gBAAC,CAAC,EAAE,EAAC;YAAC,IAAG,IAAI,CAAC,GAAG,CAAC;gBAAK,OAAO,MAAM,CAAC,EAAE,KAAK,EAAC;oBAAC,CAAC,EAAE,EAAC;gBAAC;YAAE;QAAE;QAAE,OAAM,CAAC;YAAE,IAAI,IAAE;YAAI,OAAO,EAAE,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO;QAAG;QAAE,KAAI,CAAC;YAAE,OAAO,EAAE,QAAQ,CAAC,MAAI,EAAE,IAAI,CAAC,IAAG;gBAAK,IAAI,IAAE,EAAE,OAAO,CAAC;gBAAG,IAAG,KAAG,GAAE,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAE,GAAG;YAAG;QAAC;QAAE;YAAU,KAAI,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG;QAAG;IAAC;IAAE,OAAO;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-disposables.js"], "sourcesContent": ["import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n"], "names": [], "mappings": ";;;AAAA;AAAgD;;;AAAsD,SAAS;IAAI,IAAG,CAAC,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,qKAAA,CAAA,cAAC;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,IAAI,IAAI,EAAE,OAAO,IAAG;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-iso-morphic-effect.js"], "sourcesContent": ["import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n"], "names": [], "mappings": ";;;AAAA;AAAuD;;;AAAsC,IAAI,IAAE,CAAC,GAAE;IAAK,6JAAA,CAAA,MAAC,CAAC,QAAQ,GAAC,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,GAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,kBAAC,AAAD,EAAE,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-latest-value.js"], "sourcesContent": ["import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n"], "names": [], "mappings": ";;;AAAA;AAA+B;;;AAAkE,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAG,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE,GAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-event.js"], "sourcesContent": ["import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n"], "names": [], "mappings": ";;;AAAA;AAAqB;;;AAAuD,IAAI,IAAE,SAAS,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;IAAG,OAAO,qMAAA,CAAA,UAAC,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,EAAE,OAAO,IAAI,IAAG;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1787, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-active-press.js"], "sourcesContent": ["import{useRef as a,useState as m}from\"react\";import{getOwnerDocument as d}from'../utils/owner.js';import{useDisposables as g}from'./use-disposables.js';import{useEvent as u}from'./use-event.js';function E(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function P(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function w({disabled:e=!1}={}){let t=a(null),[n,l]=m(!1),r=g(),o=u(()=>{t.current=null,l(!1),r.dispose()}),f=u(s=>{if(r.dispose(),t.current===null){t.current=s.currentTarget,l(!0);{let i=d(s.currentTarget);r.addEventListener(i,\"pointerup\",o,!1),r.addEventListener(i,\"pointermove\",c=>{if(t.current){let p=E(c);l(P(p,t.current.getBoundingClientRect()))}},!1),r.addEventListener(i,\"pointercancel\",o,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:f,onPointerUp:o,onClick:o}}}export{w as useActivePress};\n"], "names": [], "mappings": ";;;AAAA;AAA6C;AAAqD;AAAsD;;;;;AAA0C,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE,KAAK,GAAC,GAAE,IAAE,EAAE,MAAM,GAAC;IAAE,OAAM;QAAC,KAAI,EAAE,OAAO,GAAC;QAAE,OAAM,EAAE,OAAO,GAAC;QAAE,QAAO,EAAE,OAAO,GAAC;QAAE,MAAK,EAAE,OAAO,GAAC;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAM,CAAC,CAAC,CAAC,KAAG,CAAC,KAAG,EAAE,KAAK,GAAC,EAAE,IAAI,IAAE,EAAE,IAAI,GAAC,EAAE,KAAK,IAAE,EAAE,MAAM,GAAC,EAAE,GAAG,IAAE,EAAE,GAAG,GAAC,EAAE,MAAM;AAAC;AAAC,SAAS,EAAE,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,GAAC,CAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,4KAAA,CAAA,iBAAC,AAAD,KAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC,MAAK,EAAE,CAAC,IAAG,EAAE,OAAO;IAAE,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,EAAE,OAAO,IAAG,EAAE,OAAO,KAAG,MAAK;YAAC,EAAE,OAAO,GAAC,EAAE,aAAa,EAAC,EAAE,CAAC;YAAG;gBAAC,IAAI,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAC,AAAD,EAAE,EAAE,aAAa;gBAAE,EAAE,gBAAgB,CAAC,GAAE,aAAY,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,eAAc,CAAA;oBAAI,IAAG,EAAE,OAAO,EAAC;wBAAC,IAAI,IAAE,EAAE;wBAAG,EAAE,EAAE,GAAE,EAAE,OAAO,CAAC,qBAAqB;oBAAI;gBAAC,GAAE,CAAC,IAAG,EAAE,gBAAgB,CAAC,GAAE,iBAAgB,GAAE,CAAC;YAAE;QAAC;IAAC;IAAG,OAAM;QAAC,SAAQ;QAAE,YAAW,IAAE,CAAC,IAAE;YAAC,eAAc;YAAE,aAAY;YAAE,SAAQ;QAAC;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-resolve-button-type.js"], "sourcesContent": ["import{useMemo as a}from\"react\";function e(t,u){return a(()=>{var n;if(t.type)return t.type;let r=(n=t.as)!=null?n:\"button\";if(typeof r==\"string\"&&r.toLowerCase()===\"button\"||(u==null?void 0:u.tagName)===\"BUTTON\"&&!u.hasAttribute(\"type\"))return\"button\"},[t.type,t.as,u])}export{e as useResolveButtonType};\n"], "names": [], "mappings": ";;;AAAA;;AAAgC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,EAAE,IAAI,EAAC,OAAO,EAAE,IAAI;QAAC,IAAI,IAAE,CAAC,IAAE,EAAE,EAAE,KAAG,OAAK,IAAE;QAAS,IAAG,OAAO,KAAG,YAAU,EAAE,WAAW,OAAK,YAAU,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,MAAI,YAAU,CAAC,EAAE,YAAY,CAAC,SAAQ,OAAM;IAAQ,GAAE;QAAC,EAAE,IAAI;QAAC,EAAE,EAAE;QAAC;KAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "names": [], "mappings": ";;;;AAAA;AAA8C;;;AAA0C,IAAI,IAAE;AAAS,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC;IAAE,OAAO,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC;IAAC;AAAE;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,EAAE,OAAO,GAAC;IAAC,GAAE;QAAC;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;IAAG,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,CAAC,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK,IAAE;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as r,useState as b}from\"react\";function c(u=0){let[t,l]=b(u),g=r(e=>l(e),[t]),s=r(e=>l(a=>a|e),[t]),m=r(e=>(t&e)===e,[t]),n=r(e=>l(a=>a&~e),[l]),F=r(e=>l(a=>a^e),[l]);return{flags:t,setFlag:g,addFlag:s,hasFlag:m,removeFlag:n,toggleFlag:F}}export{c as useFlags};\n"], "names": [], "mappings": ";;;AAAA;;AAAkD,SAAS,EAAE,IAAE,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,CAAC,IAAE,CAAC,MAAI,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,CAAC,IAAG;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE,CAAA,IAAG,IAAE,IAAG;QAAC;KAAE;IAAE,OAAM;QAAC,OAAM;QAAE,SAAQ;QAAE,SAAQ;QAAE,SAAQ;QAAE,YAAW;QAAE,YAAW;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n"], "names": [], "mappings": ";;;;AAAQ;AAA6C;AAAsD;AAAsD;AAA0C;AAA3M,IAAI,GAAE;;;;;;AAAuQ,OAAO,WAAS,eAAa,OAAO,cAAY,eAAa,OAAO,WAAS,eAAa,CAAC,CAAC,IAAE,WAAS,OAAK,KAAK,IAAE,QAAQ,GAAG,KAAG,OAAK,KAAK,IAAE,CAAC,CAAC,WAAW,MAAI,UAAQ,OAAM,CAAC,CAAC,IAAE,WAAS,OAAK,KAAK,IAAE,QAAQ,SAAS,KAAG,OAAK,KAAK,IAAE,EAAE,aAAa,KAAG,eAAa,CAAC,QAAQ,SAAS,CAAC,aAAa,GAAC;IAAW,OAAO,QAAQ,IAAI,CAAC;QAAC;QAA+E;QAA0F;QAAG;QAAiB;QAAQ;QAA0D;QAAsB;KAAM,CAAC,IAAI,CAAC,CAAC;AACp3B,CAAC,IAAG,EAAE;AAAA,CAAC;AAAE,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAK,GAAC,EAAE,GAAC,SAAQ,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC,EAAE;IAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,IAAG,EAAC,SAAQ,CAAC,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,KAAG,IAAE,IAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,CAAC,IAAG,IAAE,CAAA,GAAA,4KAAA,CAAA,iBAAC,AAAD;IAAI,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAC,AAAD,EAAE;QAAK,IAAI;QAAE,IAAG,GAAE;YAAC,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,GAAE;gBAAC,KAAG,EAAE;gBAAG;YAAM;YAAC,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,KAAK,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,IAAG,EAAE,GAAE;gBAAC,UAAS;gBAAE;oBAAU,EAAE,OAAO,GAAC,EAAE,OAAO,GAAC,CAAC,IAAE,EAAE,OAAO,GAAC,EAAE,OAAO,EAAC,EAAE,OAAO,GAAC,CAAC,GAAE,CAAC,EAAE,OAAO,IAAE,CAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,CAAC;gBAAC;gBAAE;oBAAM,EAAE,OAAO,GAAC,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,IAAE,IAAE,EAAE,KAAG,EAAE;gBAAE;gBAAE;oBAAO,IAAI;oBAAE,EAAE,OAAO,IAAE,OAAO,EAAE,aAAa,IAAE,cAAY,EAAE,aAAa,GAAG,MAAM,GAAC,KAAG,CAAC,EAAE,OAAO,GAAC,CAAC,GAAE,EAAE,IAAG,KAAG,EAAE,CAAC,IAAG,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,GAAG,KAAG,QAAM,EAAE,IAAI,CAAC,GAAE,EAAE;gBAAC;YAAC;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE;QAAC;QAAE;YAAC,QAAO,EAAE;YAAG,OAAM,EAAE;YAAG,OAAM,EAAE;YAAG,YAAW,EAAE,MAAI,EAAE;QAAE;KAAE,GAAC;QAAC;QAAE;YAAC,QAAO,KAAK;YAAE,OAAM,KAAK;YAAE,OAAM,KAAK;YAAE,YAAW,KAAK;QAAC;KAAE;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,SAAQ,CAAC,EAAC,KAAI,CAAC,EAAC,MAAK,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;IAAI,OAAO,EAAE,GAAE;QAAC,SAAQ;QAAE,UAAS;IAAC,IAAG,EAAE,SAAS,CAAC;QAAK,KAAI,EAAE,qBAAqB,CAAC;YAAK,EAAE,GAAG,CAAC,EAAE,GAAE;QAAG;IAAE,IAAG,EAAE,OAAO;AAAA;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qKAAA,CAAA,cAAC,AAAD;IAAI,IAAG,CAAC,GAAE,OAAO,EAAE,OAAO;IAAC,IAAI,IAAE,CAAC;IAAE,EAAE,GAAG,CAAC;QAAK,IAAE,CAAC;IAAC;IAAG,IAAI,IAAE,CAAC,IAAE,CAAC,IAAE,EAAE,aAAa,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,CAAA,IAAG,aAAa,cAAc,KAAG,OAAK,IAAE,EAAE;IAAC,OAAO,EAAE,MAAM,KAAG,IAAE,CAAC,KAAI,EAAE,OAAO,IAAE,CAAC,QAAQ,UAAU,CAAC,EAAE,GAAG,CAAC,CAAA,IAAG,EAAE,QAAQ,GAAG,IAAI,CAAC;QAAK,KAAG;IAAG,IAAG,EAAE,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,CAAC,EAAC;IAAE,IAAG,KAAG,QAAM,EAAE,OAAO,EAAC;QAAC;QAAI;IAAM;IAAC,IAAI,IAAE,EAAE,KAAK,CAAC,UAAU;IAAC,EAAE,KAAK,CAAC,UAAU,GAAC,QAAO,KAAI,EAAE,YAAY,EAAC,EAAE,KAAK,CAAC,UAAU,GAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/internal/close-provider.js"], "sourcesContent": ["\"use client\";import r,{createContext as n,useContext as i}from\"react\";let e=n(()=>{});function u(){return i(e)}function C({value:t,children:o}){return r.createElement(e.Provider,{value:t},o)}export{C as CloseProvider,u as useClose};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAsE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,KAAK;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2075, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/internal/open-closed.js"], "sourcesContent": ["import r,{createContext as l,useContext as d}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAAyD,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,EAAE,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE;AAAE;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAI,GAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/bugs.js"], "sourcesContent": ["function r(n){let e=n.parentElement,l=null;for(;e&&!(e instanceof HTMLFieldSetElement);)e instanceof HTMLLegendElement&&(l=e),e=e.parentElement;let t=(e==null?void 0:e.getAttribute(\"disabled\"))===\"\";return t&&i(l)?!1:t}function i(n){if(!n)return!1;let e=n.previousElementSibling;for(;e!==null;){if(e instanceof HTMLLegendElement)return!1;e=e.previousElementSibling}return!0}export{r as isDisabledReactIssue7711};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE,aAAa,EAAC,IAAE;IAAK,MAAK,KAAG,CAAC,CAAC,aAAa,mBAAmB,GAAG,aAAa,qBAAmB,CAAC,IAAE,CAAC,GAAE,IAAE,EAAE,aAAa;IAAC,IAAI,IAAE,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,YAAY,CAAC,WAAW,MAAI;IAAG,OAAO,KAAG,EAAE,KAAG,CAAC,IAAE;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAG,CAAC,GAAE,OAAM,CAAC;IAAE,IAAI,IAAE,EAAE,sBAAsB;IAAC,MAAK,MAAI,MAAM;QAAC,IAAG,aAAa,mBAAkB,OAAM,CAAC;QAAE,IAAE,EAAE,sBAAsB;IAAA;IAAC,OAAM,CAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/match.js"], "sourcesContent": ["function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAE,IAAG,KAAK,GAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,OAAO,OAAO,KAAG,aAAW,KAAK,KAAG;IAAC;IAAC,IAAI,IAAE,IAAI,MAAM,CAAC,iBAAiB,EAAE,EAAE,8DAA8D,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/class-names.js"], "sourcesContent": ["function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAA,IAAG,OAAO,KAAG,WAAS,EAAE,KAAK,CAAC,OAAK,EAAE,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC;AAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2162, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/render.js"], "sourcesContent": ["import E,{Fragment as b,cloneElement as j,createElement as v,forwardRef as S,isValidElement as w,use<PERSON><PERSON>back as x,useRef as k}from\"react\";import{classNames as N}from'./class-names.js';import{match as M}from'./match.js';var O=(a=>(a[a.None=0]=\"None\",a[a.RenderStrategy=1]=\"RenderStrategy\",a[a.Static=2]=\"Static\",a))(O||{}),A=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(A||{});function L(){let n=U();return x(r=>C({mergeRefs:n,...r}),[n])}function C({ourProps:n,theirProps:r,slot:e,defaultTag:a,features:s,visible:t=!0,name:l,mergeRefs:i}){i=i!=null?i:$;let o=P(r,n);if(t)return F(o,e,a,l,i);let y=s!=null?s:0;if(y&2){let{static:f=!1,...u}=o;if(f)return F(u,e,a,l,i)}if(y&1){let{unmount:f=!0,...u}=o;return M(f?0:1,{[0](){return null},[1](){return F({...u,hidden:!0,style:{display:\"none\"}},e,a,l,i)}})}return F(o,e,a,l,i)}function F(n,r={},e,a,s){let{as:t=e,children:l,refName:i=\"ref\",...o}=h(n,[\"unmount\",\"static\"]),y=n.ref!==void 0?{[i]:n.ref}:{},f=typeof l==\"function\"?l(r):l;\"className\"in o&&o.className&&typeof o.className==\"function\"&&(o.className=o.className(r)),o[\"aria-labelledby\"]&&o[\"aria-labelledby\"]===o.id&&(o[\"aria-labelledby\"]=void 0);let u={};if(r){let d=!1,p=[];for(let[c,T]of Object.entries(r))typeof T==\"boolean\"&&(d=!0),T===!0&&p.push(c.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d){u[\"data-headlessui-state\"]=p.join(\" \");for(let c of p)u[`data-${c}`]=\"\"}}if(t===b&&(Object.keys(m(o)).length>0||Object.keys(m(u)).length>0))if(!w(f)||Array.isArray(f)&&f.length>1){if(Object.keys(m(o)).length>0)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${a} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(m(o)).concat(Object.keys(m(u))).map(d=>`  - ${d}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(d=>`  - ${d}`).join(`\n`)].join(`\n`))}else{let d=f.props,p=d==null?void 0:d.className,c=typeof p==\"function\"?(...R)=>N(p(...R),o.className):N(p,o.className),T=c?{className:c}:{},g=P(f.props,m(h(o,[\"ref\"])));for(let R in u)R in g&&delete u[R];return j(f,Object.assign({},g,u,y,{ref:s(H(f),y.ref)},T))}return v(t,Object.assign({},h(o,[\"ref\"]),t!==b&&y,t!==b&&u),f)}function U(){let n=k([]),r=x(e=>{for(let a of n.current)a!=null&&(typeof a==\"function\"?a(e):a.current=e)},[]);return(...e)=>{if(!e.every(a=>a==null))return n.current=e,r}}function $(...n){return n.every(r=>r==null)?void 0:r=>{for(let e of n)e!=null&&(typeof e==\"function\"?e(r):e.current=r)}}function P(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];if(r.disabled||r[\"aria-disabled\"])for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s)&&(e[s]=[t=>{var l;return(l=t==null?void 0:t.preventDefault)==null?void 0:l.call(t)}]);for(let s in e)Object.assign(r,{[s](t,...l){let i=e[s];for(let o of i){if((t instanceof Event||(t==null?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...l)}}});return r}function _(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];for(let s in e)Object.assign(r,{[s](...t){let l=e[s];for(let i of l)i==null||i(...t)}});return r}function K(n){var r;return Object.assign(S(n),{displayName:(r=n.displayName)!=null?r:n.name})}function m(n){let r=Object.assign({},n);for(let e in r)r[e]===void 0&&delete r[e];return r}function h(n,r=[]){let e=Object.assign({},n);for(let a of r)a in e&&delete e[a];return e}function H(n){return E.version.split(\".\")[0]>=\"19\"?n.props.ref:n.ref}export{O as RenderFeatures,A as RenderStrategy,m as compact,K as forwardRefWithAs,_ as mergeProps,L as useRender};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAA0I;AAA8C;;;;AAAmC,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,cAAc,GAAC,EAAE,GAAC,kBAAiB,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC,IAAG,IAAE,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,OAAO,GAAC,EAAE,GAAC,WAAU,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,KAAG,CAAC;AAAG,SAAS;IAAI,IAAI,IAAE;IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA,IAAG,EAAE;YAAC,WAAU;YAAE,GAAG,CAAC;QAAA,IAAG;QAAC;KAAE;AAAC;AAAC,SAAS,EAAE,EAAC,UAAS,CAAC,EAAC,YAAW,CAAC,EAAC,MAAK,CAAC,EAAC,YAAW,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,MAAK,CAAC,EAAC,WAAU,CAAC,EAAC;IAAE,IAAE,KAAG,OAAK,IAAE;IAAE,IAAI,IAAE,EAAE,GAAE;IAAG,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAG,IAAI,IAAE,KAAG,OAAK,IAAE;IAAE,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,QAAO,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,IAAG,GAAE,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;IAAE;IAAC,IAAG,IAAE,GAAE;QAAC,IAAG,EAAC,SAAQ,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC;QAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,IAAE,IAAE,GAAE;YAAC,CAAC,EAAE;gBAAG,OAAO;YAAI;YAAE,CAAC,EAAE;gBAAG,OAAO,EAAE;oBAAC,GAAG,CAAC;oBAAC,QAAO,CAAC;oBAAE,OAAM;wBAAC,SAAQ;oBAAM;gBAAC,GAAE,GAAE,GAAE,GAAE;YAAE;QAAC;IAAE;IAAC,OAAO,EAAE,GAAE,GAAE,GAAE,GAAE;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,IAAG,IAAE,CAAC,EAAC,UAAS,CAAC,EAAC,SAAQ,IAAE,KAAK,EAAC,GAAG,GAAE,GAAC,EAAE,GAAE;QAAC;QAAU;KAAS,GAAE,IAAE,EAAE,GAAG,KAAG,KAAK,IAAE;QAAC,CAAC,EAAE,EAAC,EAAE,GAAG;IAAA,IAAE,CAAC,GAAE,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;IAAE,eAAc,KAAG,EAAE,SAAS,IAAE,OAAO,EAAE,SAAS,IAAE,cAAY,CAAC,EAAE,SAAS,GAAC,EAAE,SAAS,CAAC,EAAE,GAAE,CAAC,CAAC,kBAAkB,IAAE,CAAC,CAAC,kBAAkB,KAAG,EAAE,EAAE,IAAE,CAAC,CAAC,CAAC,kBAAkB,GAAC,KAAK,CAAC;IAAE,IAAI,IAAE,CAAC;IAAE,IAAG,GAAE;QAAC,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE;QAAC,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,OAAO,OAAO,CAAC,GAAG,OAAO,KAAG,aAAW,CAAC,IAAE,CAAC,CAAC,GAAE,MAAI,CAAC,KAAG,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,YAAW,CAAA,IAAG,CAAC,CAAC,EAAE,EAAE,WAAW,IAAI;QAAG,IAAG,GAAE;YAAC,CAAC,CAAC,wBAAwB,GAAC,EAAE,IAAI,CAAC;YAAK,KAAI,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,GAAC;QAAE;IAAC;IAAC,IAAG,MAAI,qMAAA,CAAA,WAAC,IAAE,CAAC,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,KAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,CAAC,GAAE,IAAG,CAAC,CAAA,GAAA,qMAAA,CAAA,iBAAC,AAAD,EAAE,MAAI,MAAM,OAAO,CAAC,MAAI,EAAE,MAAM,GAAC,GAAE;QAAC,IAAG,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,GAAC,GAAE,MAAM,IAAI,MAAM;YAAC;YAA+B;YAAG,CAAC,uBAAuB,EAAE,EAAE,8BAA8B,CAAC;YAAC;YAAsD,OAAO,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACruD,CAAC;YAAE;YAAG;YAAiC;gBAAC;gBAA8F;aAA2F,CAAC,GAAG,CAAC,CAAA,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3P,CAAC;SAAE,CAAC,IAAI,CAAC,CAAC;AACV,CAAC;IAAE,OAAK;QAAC,IAAI,IAAE,EAAE,KAAK,EAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,SAAS,EAAC,IAAE,OAAO,KAAG,aAAW,CAAC,GAAG,IAAI,CAAA,GAAA,wKAAA,CAAA,aAAC,AAAD,EAAE,KAAK,IAAG,EAAE,SAAS,IAAE,CAAA,GAAA,wKAAA,CAAA,aAAC,AAAD,EAAE,GAAE,EAAE,SAAS,GAAE,IAAE,IAAE;YAAC,WAAU;QAAC,IAAE,CAAC,GAAE,IAAE,EAAE,EAAE,KAAK,EAAC,EAAE,EAAE,GAAE;YAAC;SAAM;QAAI,IAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;QAAC,OAAO,CAAA,GAAA,qMAAA,CAAA,eAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,GAAE,GAAE,GAAE;YAAC,KAAI,EAAE,EAAE,IAAG,EAAE,GAAG;QAAC,GAAE;IAAG;IAAC,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE,GAAE,OAAO,MAAM,CAAC,CAAC,GAAE,EAAE,GAAE;QAAC;KAAM,GAAE,MAAI,qMAAA,CAAA,WAAC,IAAE,GAAE,MAAI,qMAAA,CAAA,WAAC,IAAE,IAAG;AAAE;AAAC,SAAS;IAAI,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,EAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAC,AAAD,EAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,OAAO,CAAC,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC,GAAE,EAAE;IAAE,OAAM,CAAC,GAAG;QAAK,IAAG,CAAC,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,OAAM,OAAO,EAAE,OAAO,GAAC,GAAE;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,OAAO,EAAE,KAAK,CAAC,CAAA,IAAG,KAAG,QAAM,KAAK,IAAE,CAAA;QAAI,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,CAAC,OAAO,KAAG,aAAW,EAAE,KAAG,EAAE,OAAO,GAAC,CAAC;IAAC;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAG,EAAE,QAAQ,IAAE,CAAC,CAAC,gBAAgB,EAAC,IAAI,IAAI,KAAK,EAAE,sDAAsD,IAAI,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,GAAC;QAAC,CAAA;YAAI,IAAI;YAAE,OAAM,CAAC,IAAE,KAAG,OAAK,KAAK,IAAE,EAAE,cAAc,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,CAAC;QAAE;KAAE;IAAE,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,CAAC,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE;gBAAC,IAAG,CAAC,aAAa,SAAO,CAAC,KAAG,OAAK,KAAK,IAAE,EAAE,WAAW,aAAY,KAAK,KAAG,EAAE,gBAAgB,EAAC;gBAAO,EAAE,MAAK;YAAE;QAAC;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,GAAG,CAAC;IAAE,IAAI;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAM,CAAC;IAAE,IAAG,EAAE,MAAM,KAAG,GAAE,OAAO,CAAC,CAAC,EAAE;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,CAAC;IAAE,KAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,UAAU,CAAC,SAAO,OAAO,CAAC,CAAC,EAAE,IAAE,aAAW,CAAC,CAAC,IAAE,CAAC,CAAC,EAAE,KAAG,QAAM,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,IAAI,IAAI,KAAK,EAAE,OAAO,MAAM,CAAC,GAAE;QAAC,CAAC,EAAE,EAAC,GAAG,CAAC;YAAE,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,KAAI,IAAI,KAAK,EAAE,KAAG,QAAM,KAAK;QAAE;IAAC;IAAG,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI;IAAE,OAAO,OAAO,MAAM,CAAC,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG;QAAC,aAAY,CAAC,IAAE,EAAE,WAAW,KAAG,OAAK,IAAE,EAAE,IAAI;IAAA;AAAE;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,KAAG,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,EAAE;IAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE;IAAG,KAAI,IAAI,KAAK,EAAE,KAAK,KAAG,OAAO,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,qMAAA,CAAA,UAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAE,OAAK,EAAE,KAAK,CAAC,GAAG,GAAC,EAAE,GAAG;AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2339, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/utils/start-transition.js"], "sourcesContent": ["var t;import r from\"react\";let a=(t=r.startTransition)!=null?t:function(i){i()};export{a as startTransition};\n"], "names": [], "mappings": ";;;AAAM;AAAN,IAAI;;AAAuB,IAAI,IAAE,CAAC,IAAE,qMAAA,CAAA,UAAC,CAAC,eAAe,KAAG,OAAK,IAAE,SAAS,CAAC;IAAE;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/components/keyboard.js"], "sourcesContent": ["var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n"], "names": [], "mappings": ";;;AAAA,IAAI,IAAE,CAAC,CAAA,IAAG,CAAC,EAAE,KAAK,GAAC,KAAI,EAAE,KAAK,GAAC,SAAQ,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,MAAM,GAAC,UAAS,EAAE,SAAS,GAAC,aAAY,EAAE,OAAO,GAAC,WAAU,EAAE,UAAU,GAAC,cAAa,EAAE,SAAS,GAAC,aAAY,EAAE,IAAI,GAAC,QAAO,EAAE,GAAG,GAAC,OAAM,EAAE,MAAM,GAAC,UAAS,EAAE,QAAQ,GAAC,YAAW,EAAE,GAAG,GAAC,OAAM,CAAC,CAAC,EAAE,KAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/%40headlessui/react/dist/components/disclosure/disclosure.js"], "sourcesContent": ["\"use client\";import{useFocusRing as q}from\"@react-aria/focus\";import{useHover as z}from\"@react-aria/interactions\";import y,{Fragment as w,createContext as I,useContext as x,useEffect as G,useMemo as C,useReducer as Q,useRef as K,useState as Y}from\"react\";import{useActivePress as Z}from'../../hooks/use-active-press.js';import{useEvent as P}from'../../hooks/use-event.js';import{useId as W}from'../../hooks/use-id.js';import{useResolveButtonType as ee}from'../../hooks/use-resolve-button-type.js';import{optionalRef as te,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as ne,useTransition as oe}from'../../hooks/use-transition.js';import{CloseProvider as le}from'../../internal/close-provider.js';import{OpenClosedProvider as re,ResetOpenClosedProvider as se,State as R,useOpenClosed as ue}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{match as B}from'../../utils/match.js';import{getOwnerDocument as ae}from'../../utils/owner.js';import{RenderFeatures as j,forwardRefWithAs as v,mergeProps as $,useRender as O}from'../../utils/render.js';import{startTransition as pe}from'../../utils/start-transition.js';import{Keys as A}from'../keyboard.js';var ce=(l=>(l[l.Open=0]=\"Open\",l[l.Closed=1]=\"Closed\",l))(ce||{}),de=(n=>(n[n.ToggleDisclosure=0]=\"ToggleDisclosure\",n[n.CloseDisclosure=1]=\"CloseDisclosure\",n[n.SetButtonId=2]=\"SetButtonId\",n[n.SetPanelId=3]=\"SetPanelId\",n[n.SetButtonElement=4]=\"SetButtonElement\",n[n.SetPanelElement=5]=\"SetPanelElement\",n))(de||{});let Te={[0]:e=>({...e,disclosureState:B(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},[3](e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},[4](e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},[5](e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},_=I(null);_.displayName=\"DisclosureContext\";function M(e){let t=x(_);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,M),l}return t}let F=I(null);F.displayName=\"DisclosureAPIContext\";function J(e){let t=x(F);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,J),l}return t}let H=I(null);H.displayName=\"DisclosurePanelContext\";function fe(){return x(H)}function me(e,t){return B(t.type,Te,e,t)}let De=w;function ye(e,t){let{defaultOpen:l=!1,...p}=e,i=K(null),c=L(t,te(a=>{i.current=a},e.as===void 0||e.as===w)),n=Q(me,{disclosureState:l?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:o,buttonId:r},m]=n,s=P(a=>{m({type:1});let d=ae(i);if(!d||!r)return;let T=(()=>a?a instanceof HTMLElement?a:a.current instanceof HTMLElement?a.current:d.getElementById(r):d.getElementById(r))();T==null||T.focus()}),E=C(()=>({close:s}),[s]),f=C(()=>({open:o===0,close:s}),[o,s]),D={ref:c},S=O();return y.createElement(_.Provider,{value:n},y.createElement(F.Provider,{value:E},y.createElement(le,{value:s},y.createElement(re,{value:B(o,{[0]:R.Open,[1]:R.Closed})},S({ourProps:D,theirProps:p,slot:f,defaultTag:De,name:\"Disclosure\"})))))}let Pe=\"button\";function Ee(e,t){let l=W(),{id:p=`headlessui-disclosure-button-${l}`,disabled:i=!1,autoFocus:c=!1,...n}=e,[o,r]=M(\"Disclosure.Button\"),m=fe(),s=m===null?!1:m===o.panelId,E=K(null),f=L(E,t,P(u=>{if(!s)return r({type:4,element:u})}));G(()=>{if(!s)return r({type:2,buttonId:p}),()=>{r({type:2,buttonId:null})}},[p,r,s]);let D=P(u=>{var g;if(s){if(o.disclosureState===1)return;switch(u.key){case A.Space:case A.Enter:u.preventDefault(),u.stopPropagation(),r({type:0}),(g=o.buttonElement)==null||g.focus();break}}else switch(u.key){case A.Space:case A.Enter:u.preventDefault(),u.stopPropagation(),r({type:0});break}}),S=P(u=>{switch(u.key){case A.Space:u.preventDefault();break}}),a=P(u=>{var g;ie(u.currentTarget)||i||(s?(r({type:0}),(g=o.buttonElement)==null||g.focus()):r({type:0}))}),{isFocusVisible:d,focusProps:T}=q({autoFocus:c}),{isHovered:b,hoverProps:h}=z({isDisabled:i}),{pressed:U,pressProps:N}=Z({disabled:i}),X=C(()=>({open:o.disclosureState===0,hover:b,active:U,disabled:i,focus:d,autofocus:c}),[o,b,U,d,i,c]),k=ee(e,o.buttonElement),V=s?$({ref:f,type:k,disabled:i||void 0,autoFocus:c,onKeyDown:D,onClick:a},T,h,N):$({ref:f,id:p,type:k,\"aria-expanded\":o.disclosureState===0,\"aria-controls\":o.panelElement?o.panelId:void 0,disabled:i||void 0,autoFocus:c,onKeyDown:D,onKeyUp:S,onClick:a},T,h,N);return O()({ourProps:V,theirProps:n,slot:X,defaultTag:Pe,name:\"Disclosure.Button\"})}let Se=\"div\",ge=j.RenderStrategy|j.Static;function Ae(e,t){let l=W(),{id:p=`headlessui-disclosure-panel-${l}`,transition:i=!1,...c}=e,[n,o]=M(\"Disclosure.Panel\"),{close:r}=J(\"Disclosure.Panel\"),[m,s]=Y(null),E=L(t,P(b=>{pe(()=>o({type:5,element:b}))}),s);G(()=>(o({type:3,panelId:p}),()=>{o({type:3,panelId:null})}),[p,o]);let f=ue(),[D,S]=oe(i,m,f!==null?(f&R.Open)===R.Open:n.disclosureState===0),a=C(()=>({open:n.disclosureState===0,close:r}),[n.disclosureState,r]),d={ref:E,id:p,...ne(S)},T=O();return y.createElement(se,null,y.createElement(H.Provider,{value:n.panelId},T({ourProps:d,theirProps:c,slot:a,defaultTag:Se,features:ge,visible:D,name:\"Disclosure.Panel\"})))}let be=v(ye),Ce=v(Ee),Re=v(Ae),je=Object.assign(be,{Button:Ce,Panel:Re});export{je as Disclosure,Ce as DisclosureButton,Re as DisclosurePanel};\n"], "names": [], "mappings": ";;;;;AAAa;AAAiD;AAAoD;AAA6I;AAAiE;AAAkG;AAA+E;AAA6E;AAA8F;AAAkE;AAAiI;AAAgE;AAA6C;AAAyD;AAA4G;AAAmE;AAAprC;;;;;;;;;;;;;;;;;;AAA0tC,IAAI,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAC,EAAE,GAAC,QAAO,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,GAAC,UAAS,CAAC,CAAC,EAAE,MAAI,CAAC,IAAG,KAAG,CAAC,CAAA,IAAG,CAAC,CAAC,CAAC,EAAE,gBAAgB,GAAC,EAAE,GAAC,oBAAmB,CAAC,CAAC,EAAE,eAAe,GAAC,EAAE,GAAC,mBAAkB,CAAC,CAAC,EAAE,WAAW,GAAC,EAAE,GAAC,eAAc,CAAC,CAAC,EAAE,UAAU,GAAC,EAAE,GAAC,cAAa,CAAC,CAAC,EAAE,gBAAgB,GAAC,EAAE,GAAC,oBAAmB,CAAC,CAAC,EAAE,eAAe,GAAC,EAAE,GAAC,mBAAkB,CAAC,CAAC,EAAE,MAAI,CAAC;AAAG,IAAI,KAAG;IAAC,CAAC,EAAE,EAAC,CAAA,IAAG,CAAC;YAAC,GAAG,CAAC;YAAC,iBAAgB,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,EAAE,eAAe,EAAC;gBAAC,CAAC,EAAE,EAAC;gBAAE,CAAC,EAAE,EAAC;YAAC;QAAE,CAAC;IAAE,CAAC,EAAE,EAAC,CAAA,IAAG,EAAE,eAAe,KAAG,IAAE,IAAE;YAAC,GAAG,CAAC;YAAC,iBAAgB;QAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,QAAQ,KAAG,EAAE,QAAQ,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,UAAS,EAAE,QAAQ;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,OAAO,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,SAAQ,EAAE,OAAO;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,aAAa,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,eAAc,EAAE,OAAO;QAAA;IAAC;IAAE,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC;QAAE,OAAO,EAAE,YAAY,KAAG,EAAE,OAAO,GAAC,IAAE;YAAC,GAAG,CAAC;YAAC,cAAa,EAAE,OAAO;QAAA;IAAC;AAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAoB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,iDAAiD,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAuB,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;IAAG,IAAG,MAAI,MAAK;QAAC,IAAI,IAAE,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,iDAAiD,CAAC;QAAE,MAAM,MAAM,iBAAiB,IAAE,MAAM,iBAAiB,CAAC,GAAE,IAAG;IAAC;IAAC,OAAO;AAAC;AAAC,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAC,AAAD,EAAE;AAAM,EAAE,WAAW,GAAC;AAAyB,SAAS;IAAK,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,EAAE,IAAI,EAAC,IAAG,GAAE;AAAE;AAAC,IAAI,KAAG,qMAAA,CAAA,WAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAC,aAAY,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,CAAA,GAAA,6KAAA,CAAA,cAAE,AAAD,EAAE,CAAA;QAAI,EAAE,OAAO,GAAC;IAAC,GAAE,EAAE,EAAE,KAAG,KAAK,KAAG,EAAE,EAAE,KAAG,qMAAA,CAAA,WAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAC,AAAD,EAAE,IAAG;QAAC,iBAAgB,IAAE,IAAE;QAAE,eAAc;QAAK,cAAa;QAAK,UAAS;QAAK,SAAQ;IAAI,IAAG,CAAC,EAAC,iBAAgB,CAAC,EAAC,UAAS,CAAC,EAAC,EAAC,EAAE,GAAC,GAAE,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,EAAE;YAAC,MAAK;QAAC;QAAG,IAAI,IAAE,CAAA,GAAA,+JAAA,CAAA,mBAAE,AAAD,EAAE;QAAG,IAAG,CAAC,KAAG,CAAC,GAAE;QAAO,IAAI,IAAE,CAAC,IAAI,IAAE,aAAa,cAAY,IAAE,EAAE,OAAO,YAAY,cAAY,EAAE,OAAO,GAAC,EAAE,cAAc,CAAC,KAAG,EAAE,cAAc,CAAC,EAAE;QAAI,KAAG,QAAM,EAAE,KAAK;IAAE,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;QAAC,CAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,MAAI;YAAE,OAAM;QAAC,CAAC,GAAE;QAAC;QAAE;KAAE,GAAE,IAAE;QAAC,KAAI;IAAC,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,8KAAA,CAAA,gBAAE,EAAC;QAAC,OAAM;IAAC,GAAE,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,2KAAA,CAAA,qBAAE,EAAC;QAAC,OAAM,CAAA,GAAA,+JAAA,CAAA,QAAC,AAAD,EAAE,GAAE;YAAC,CAAC,EAAE,EAAC,2KAAA,CAAA,QAAC,CAAC,IAAI;YAAC,CAAC,EAAE,EAAC,2KAAA,CAAA,QAAC,CAAC,MAAM;QAAA;IAAE,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAY;AAAM;AAAC,IAAI,KAAG;AAAS,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,6BAA6B,EAAE,GAAG,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,WAAU,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,GAAE,EAAE,GAAC,EAAE,sBAAqB,IAAE,MAAK,IAAE,MAAI,OAAK,CAAC,IAAE,MAAI,EAAE,OAAO,EAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,SAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,GAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAG,CAAC,GAAE,OAAO,EAAE;YAAC,MAAK;YAAE,SAAQ;QAAC;IAAE;IAAI,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE;QAAK,IAAG,CAAC,GAAE,OAAO,EAAE;YAAC,MAAK;YAAE,UAAS;QAAC,IAAG;YAAK,EAAE;gBAAC,MAAK;gBAAE,UAAS;YAAI;QAAE;IAAC,GAAE;QAAC;QAAE;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI;QAAE,IAAG,GAAE;YAAC,IAAG,EAAE,eAAe,KAAG,GAAE;YAAO,OAAO,EAAE,GAAG;gBAAE,KAAK,uKAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,KAAK,uKAAA,CAAA,OAAC,CAAC,KAAK;oBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE;wBAAC,MAAK;oBAAC,IAAG,CAAC,IAAE,EAAE,aAAa,KAAG,QAAM,EAAE,KAAK;oBAAG;YAAK;QAAC,OAAM,OAAO,EAAE,GAAG;YAAE,KAAK,uKAAA,CAAA,OAAC,CAAC,KAAK;YAAC,KAAK,uKAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,EAAE,cAAc,IAAG,EAAE,eAAe,IAAG,EAAE;oBAAC,MAAK;gBAAC;gBAAG;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,OAAO,EAAE,GAAG;YAAE,KAAK,uKAAA,CAAA,OAAC,CAAC,KAAK;gBAAC,EAAE,cAAc;gBAAG;QAAK;IAAC,IAAG,IAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,IAAI;QAAE,CAAA,GAAA,8JAAA,CAAA,2BAAE,AAAD,EAAE,EAAE,aAAa,KAAG,KAAG,CAAC,IAAE,CAAC,EAAE;YAAC,MAAK;QAAC,IAAG,CAAC,IAAE,EAAE,aAAa,KAAG,QAAM,EAAE,KAAK,EAAE,IAAE,EAAE;YAAC,MAAK;QAAC,EAAE;IAAC,IAAG,EAAC,gBAAe,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,iKAAA,CAAA,eAAC,AAAD,EAAE;QAAC,WAAU;IAAC,IAAG,EAAC,WAAU,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,oKAAA,CAAA,WAAC,AAAD,EAAE;QAAC,YAAW;IAAC,IAAG,EAAC,SAAQ,CAAC,EAAC,YAAW,CAAC,EAAC,GAAC,CAAA,GAAA,gLAAA,CAAA,iBAAC,AAAD,EAAE;QAAC,UAAS;IAAC,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,EAAE,eAAe,KAAG;YAAE,OAAM;YAAE,QAAO;YAAE,UAAS;YAAE,OAAM;YAAE,WAAU;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,GAAE,IAAE,CAAA,GAAA,0LAAA,CAAA,uBAAE,AAAD,EAAE,GAAE,EAAE,aAAa,GAAE,IAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,aAAC,AAAD,EAAE;QAAC,KAAI;QAAE,MAAK;QAAE,UAAS,KAAG,KAAK;QAAE,WAAU;QAAE,WAAU;QAAE,SAAQ;IAAC,GAAE,GAAE,GAAE,KAAG,CAAA,GAAA,gKAAA,CAAA,aAAC,AAAD,EAAE;QAAC,KAAI;QAAE,IAAG;QAAE,MAAK;QAAE,iBAAgB,EAAE,eAAe,KAAG;QAAE,iBAAgB,EAAE,YAAY,GAAC,EAAE,OAAO,GAAC,KAAK;QAAE,UAAS,KAAG,KAAK;QAAE,WAAU;QAAE,WAAU;QAAE,SAAQ;QAAE,SAAQ;IAAC,GAAE,GAAE,GAAE;IAAG,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD,IAAI;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,MAAK;IAAmB;AAAE;AAAC,IAAI,KAAG,OAAM,KAAG,gKAAA,CAAA,iBAAC,CAAC,cAAc,GAAC,gKAAA,CAAA,iBAAC,CAAC,MAAM;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,QAAC,AAAD,KAAI,EAAC,IAAG,IAAE,CAAC,4BAA4B,EAAE,GAAG,EAAC,YAAW,IAAE,CAAC,CAAC,EAAC,GAAG,GAAE,GAAC,GAAE,CAAC,GAAE,EAAE,GAAC,EAAE,qBAAoB,EAAC,OAAM,CAAC,EAAC,GAAC,EAAE,qBAAoB,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAC,AAAD,EAAE,OAAM,IAAE,CAAA,GAAA,6KAAA,CAAA,cAAC,AAAD,EAAE,GAAE,CAAA,GAAA,sKAAA,CAAA,WAAC,AAAD,EAAE,CAAA;QAAI,CAAA,GAAA,6KAAA,CAAA,kBAAE,AAAD,EAAE,IAAI,EAAE;gBAAC,MAAK;gBAAE,SAAQ;YAAC;IAAG,IAAG;IAAG,CAAA,GAAA,qMAAA,CAAA,YAAC,AAAD,EAAE,IAAI,CAAC,EAAE;YAAC,MAAK;YAAE,SAAQ;QAAC,IAAG;YAAK,EAAE;gBAAC,MAAK;gBAAE,SAAQ;YAAI;QAAE,CAAC,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,2KAAA,CAAA,gBAAE,AAAD,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,2KAAA,CAAA,gBAAE,AAAD,EAAE,GAAE,GAAE,MAAI,OAAK,CAAC,IAAE,2KAAA,CAAA,QAAC,CAAC,IAAI,MAAI,2KAAA,CAAA,QAAC,CAAC,IAAI,GAAC,EAAE,eAAe,KAAG,IAAG,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC;YAAC,MAAK,EAAE,eAAe,KAAG;YAAE,OAAM;QAAC,CAAC,GAAE;QAAC,EAAE,eAAe;QAAC;KAAE,GAAE,IAAE;QAAC,KAAI;QAAE,IAAG;QAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,2BAAE,AAAD,EAAE,EAAE;IAAA,GAAE,IAAE,CAAA,GAAA,gKAAA,CAAA,YAAC,AAAD;IAAI,OAAO,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,2KAAA,CAAA,0BAAE,EAAC,MAAK,qMAAA,CAAA,UAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAC;QAAC,OAAM,EAAE,OAAO;IAAA,GAAE,EAAE;QAAC,UAAS;QAAE,YAAW;QAAE,MAAK;QAAE,YAAW;QAAG,UAAS;QAAG,SAAQ;QAAE,MAAK;IAAkB;AAAI;AAAC,IAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,CAAA,GAAA,gKAAA,CAAA,mBAAC,AAAD,EAAE,KAAI,KAAG,OAAO,MAAM,CAAC,IAAG;IAAC,QAAO;IAAG,OAAM;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "file": "purify.es.mjs", "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/dompurify/src/utils.ts", "file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/dompurify/src/tags.ts", "file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/dompurify/src/attrs.ts", "file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/dompurify/src/regexp.ts", "file:///D:/workspace/github/alltomarkdown_frontend/src/node_modules/dompurify/src/purify.ts"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : {};\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : {};\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      let value = name === 'value' ? attrValue : stringTrim(attrValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;AAAA,MAAM,EACJA,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,wBAAAA,EACD,GAAGC,MAAM,CAAA;AAEV,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAAA,EAAQ,GAAGH,MAAM,CAAC,CAAA,gDAAA;AACtC,IAAI,EAAEI,KAAK,EAAEC,SAAAA,EAAW,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAA;AAEpE,IAAI,CAACL,MAAM,EAAE;IACXA,MAAM,GAAG,SAAAA,MAAUM,CAAAA,CAAC,EAAA;QAClB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,IAAI,EAAE;IACTA,IAAI,GAAG,SAAAA,IAAUK,CAAAA,CAAC,EAAA;QAChB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACH,KAAK,EAAE;IACVA,KAAK,GAAG,SAAAA,KAAUI,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAA;QACpC,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,SAAS,EAAE;IACdA,SAAS,GAAG,SAAAA,SAAAA,CAAUM,IAAI,EAAED,IAAI,EAAA;QAC9B,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAA;KACzB,CAAA;AACH,CAAA;AAEA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;AAErD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,WAAW,CAAC,CAAA;AAC7D,MAAMC,QAAQ,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,GAAG,CAAC,CAAA;AAC7C,MAAMC,SAAS,GAAGR,OAAO,CAACC,KAAK,CAACC,SAAS,CAACO,IAAI,CAAC,CAAA;AAE/C,MAAMC,WAAW,GAAGV,OAAO,CAACC,KAAK,CAACC,SAAS,CAACS,MAAM,CAAC,CAAA;AAEnD,MAAMC,iBAAiB,GAAGZ,OAAO,CAACa,MAAM,CAACX,SAAS,CAACY,WAAW,CAAC,CAAA;AAC/D,MAAMC,cAAc,GAAGf,OAAO,CAACa,MAAM,CAACX,SAAS,CAACc,QAAQ,CAAC,CAAA;AACzD,MAAMC,WAAW,GAAGjB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACgB,KAAK,CAAC,CAAA;AACnD,MAAMC,aAAa,GAAGnB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACkB,OAAO,CAAC,CAAA;AACvD,MAAMC,aAAa,GAAGrB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACoB,OAAO,CAAC,CAAA;AACvD,MAAMC,UAAU,GAAGvB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACsB,IAAI,CAAC,CAAA;AAEjD,MAAMC,oBAAoB,GAAGzB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACwB,cAAc,CAAC,CAAA;AAErE,MAAMC,UAAU,GAAG3B,OAAO,CAAC4B,MAAM,CAAC1B,SAAS,CAAC2B,IAAI,CAAC,CAAA;AAEjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;AAE9C;;;;;CAKG,GACH,SAAShC,OAAOA,CACdiC,IAAyC,EAAA;IAEzC,OAAO,SAACC,OAAY,EAAuB;QACzC,IAAIA,OAAO,YAAYN,MAAM,EAAE;YAC7BM,OAAO,CAACC,SAAS,GAAG,CAAC,CAAA;QACvB,CAAA;QAAC,IAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHsBzC,IAAW,GAAA,IAAAI,KAAA,CAAAmC,IAAA,GAAAA,CAAAA,GAAAA,IAAA,GAAA,IAAA,IAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,CAAA;YAAX1C,IAAW,CAAA0C,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;QAAA,CAAA;QAKlC,OAAOhD,KAAK,CAAC0C,IAAI,EAAEC,OAAO,EAAErC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA;;;;;CAKG,GACH,SAASkC,WAAWA,CAAIE,IAA2B,EAAA;IACjD,OAAO,YAAA;QAAA,IAAA,IAAAO,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIzC,IAAW,GAAAI,IAAAA,KAAA,CAAAuC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,CAAA;YAAX5C,IAAW,CAAA4C,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;QAAA,CAAA;QAAA,OAAQjD,SAAS,CAACyC,IAAI,EAAEpC,IAAI,CAAC,CAAA;IAAA,CAAA,CAAA;AACrD,CAAA;AAEA;;;;;;;CAOG,GACH,SAAS6C,QAAQA,CACfC,GAAwB,EACxBC,KAAqB,EACoD;IAAA,IAAzEC,oBAAAA,UAAAA,MAAAA,GAAAA,KAAAA,SAAAA,CAAAA,EAAAA,KAAAA,YAAAA,SAAAA,CAAAA,EAAAA,GAAwDjC,iBAAiB,CAAA;IAEzE,IAAI7B,cAAc,EAAE;QAClB,4DAAA;QACA,6DAAA;QACA,mEAAA;QACAA,cAAc,CAAC4D,GAAG,EAAE,IAAI,CAAC,CAAA;IAC3B,CAAA;IAEA,IAAIG,CAAC,GAAGF,KAAK,CAACN,MAAM,CAAA;IACpB,MAAOQ,CAAC,EAAE,CAAE;QACV,IAAIC,OAAO,GAAGH,KAAK,CAACE,CAAC,CAAC,CAAA;QACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAMC,SAAS,GAAGH,iBAAiB,CAACE,OAAO,CAAC,CAAA;YAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;gBACzB,yDAAA;gBACA,IAAI,CAAC/D,QAAQ,CAAC4D,KAAK,CAAC,EAAE;oBACnBA,KAAe,CAACE,CAAC,CAAC,GAAGE,SAAS,CAAA;gBACjC,CAAA;gBAEAD,OAAO,GAAGC,SAAS,CAAA;YACrB,CAAA;QACF,CAAA;QAEAL,GAAG,CAACI,OAAO,CAAC,GAAG,IAAI,CAAA;IACrB,CAAA;IAEA,OAAOJ,GAAG,CAAA;AACZ,CAAA;AAEA;;;;;CAKG,GACH,SAASM,UAAUA,CAAIL,KAAU,EAAA;IAC/B,IAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,KAAK,CAACN,MAAM,EAAEY,KAAK,EAAE,CAAE;QACjD,MAAMC,eAAe,GAAG1B,oBAAoB,CAACmB,KAAK,EAAEM,KAAK,CAAC,CAAA;QAE1D,IAAI,CAACC,eAAe,EAAE;YACpBP,KAAK,CAACM,KAAK,CAAC,GAAG,IAAI,CAAA;QACrB,CAAA;IACF,CAAA;IAEA,OAAON,KAAK,CAAA;AACd,CAAA;AAEA;;;;;CAKG,GACH,SAASQ,KAAKA,CAAgCC,MAAS,EAAA;IACrD,MAAMC,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC,CAAA;IAE9B,KAAK,MAAM,CAACiE,QAAQ,EAAEC,KAAK,CAAC,IAAI1E,OAAO,CAACuE,MAAM,CAAC,CAAE;QAC/C,MAAMF,eAAe,GAAG1B,oBAAoB,CAAC4B,MAAM,EAAEE,QAAQ,CAAC,CAAA;QAE9D,IAAIJ,eAAe,EAAE;YACnB,IAAIlD,KAAK,CAACwD,OAAO,CAACD,KAAK,CAAC,EAAE;gBACxBF,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAA;YACzC,CAAC,MAAM,IACLA,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACE,WAAW,KAAKvE,MAAM,EAC5B;gBACAmE,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAA;YACpC,CAAC,MAAM;gBACLF,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAAA;YAC7B,CAAA;QACF,CAAA;IACF,CAAA;IAEA,OAAOF,SAAS,CAAA;AAClB,CAAA;AAEA;;;;;;CAMG,GACH,SAASK,YAAYA,CACnBN,MAAS,EACTO,IAAY,EAAA;IAEZ,MAAOP,MAAM,KAAK,IAAI,CAAE;QACtB,MAAMQ,IAAI,GAAG3E,wBAAwB,CAACmE,MAAM,EAAEO,IAAI,CAAC,CAAA;QAEnD,IAAIC,IAAI,EAAE;YACR,IAAIA,IAAI,CAACC,GAAG,EAAE;gBACZ,OAAO9D,OAAO,CAAC6D,IAAI,CAACC,GAAG,CAAC,CAAA;YAC1B,CAAA;YAEA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;gBACpC,OAAOxD,OAAO,CAAC6D,IAAI,CAACL,KAAK,CAAC,CAAA;YAC5B,CAAA;QACF,CAAA;QAEAH,MAAM,GAAGpE,cAAc,CAACoE,MAAM,CAAC,CAAA;IACjC,CAAA;IAEA,SAASU,aAAaA,GAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,OAAOA,aAAa,CAAA;AACtB;AC3MO,MAAMC,MAAI,GAAG5E,MAAM,CAAC;IACzB,GAAG;IACH,MAAM;IACN,SAAS;IACT,SAAS;IACT,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,GAAG;IACH,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,KAAK;IACL,SAAS;IACT,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,GAAG;IACH,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,KAAK;IACL,MAAM;IACN,SAAS;IACT,MAAM;IACN,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,IAAI;IACJ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,GAAG;IACH,SAAS;IACT,KAAK;IACL,UAAU;IACV,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,GAAG;IACH,MAAM;IACN,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,UAAU;IACV,UAAU;IACV,OAAO;IACP,IAAI;IACJ,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,KAAK;IACL,OAAO;IACP,KAAK;CACG,CAAC,CAAA;AAEJ,MAAM6E,KAAG,GAAG7E,MAAM,CAAC;IACxB,KAAK;IACL,GAAG;IACH,UAAU;IACV,aAAa;IACb,cAAc;IACd,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,MAAM;IACN,GAAG;IACH,OAAO;IACP,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,gBAAgB;IAChB,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;CACC,CAAC,CAAA;AAEJ,MAAM8E,UAAU,GAAG9E,MAAM,CAAC;IAC/B,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,aAAa;IACb,cAAc;IACd,UAAU;IACV,cAAc;IACd,oBAAoB;IACpB,aAAa;IACb,QAAQ;IACR,cAAc;CACN,CAAC,CAAA;AAEX,uDAAA;AACA,yDAAA;AACA,mDAAA;AACA,cAAA;AACO,MAAM+E,aAAa,GAAG/E,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,QAAQ;IACR,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,eAAe;IACf,OAAO;IACP,WAAW;IACX,MAAM;IACN,cAAc;IACd,WAAW;IACX,SAAS;IACT,eAAe;IACf,QAAQ;IACR,KAAK;IACL,YAAY;IACZ,SAAS;IACT,KAAK;CACG,CAAC,CAAA;AAEJ,MAAMgF,QAAM,GAAGhF,MAAM,CAAC;IAC3B,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,YAAY;IACZ,eAAe;IACf,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,aAAa;CACL,CAAC,CAAA;AAEX,yDAAA;AACA,0CAAA;AACO,MAAMiF,gBAAgB,GAAGjF,MAAM,CAAC;IACrC,SAAS;IACT,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,MAAM;CACE,CAAC,CAAA;AAEJ,MAAMkF,IAAI,GAAGlF,MAAM,CAAC;IAAC,OAAO;CAAU,CAAC;ACpRvC,MAAM4E,IAAI,GAAG5E,MAAM,CAAC;IACzB,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,UAAU;IACV,cAAc;IACd,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,KAAK;IACL,UAAU;IACV,yBAAyB;IACzB,uBAAuB;IACvB,UAAU;IACV,WAAW;IACX,SAAS;IACT,cAAc;IACd,MAAM;IACN,KAAK;IACL,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,WAAW;IACX,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,KAAK;IACL,KAAK;IACL,WAAW;IACX,OAAO;IACP,QAAQ;IACR,KAAK;IACL,WAAW;IACX,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,QAAQ;IACR,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,KAAK;IACL,UAAU;IACV,KAAK;IACL,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,YAAY;IACZ,OAAO;IACP,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;CACE,CAAC,CAAA;AAEJ,MAAM6E,GAAG,GAAG7E,MAAM,CAAC;IACxB,eAAe;IACf,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,WAAW;IACX,QAAQ;IACR,eAAe;IACf,eAAe;IACf,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,MAAM;IACN,eAAe;IACf,WAAW;IACX,WAAW;IACX,OAAO;IACP,qBAAqB;IACrB,6BAA6B;IAC7B,eAAe;IACf,iBAAiB;IACjB,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;IACT,KAAK;IACL,UAAU;IACV,WAAW;IACX,KAAK;IACL,UAAU;IACV,MAAM;IACN,cAAc;IACd,WAAW;IACX,QAAQ;IACR,aAAa;IACb,aAAa;IACb,eAAe;IACf,aAAa;IACb,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,cAAc;IACd,aAAa;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,YAAY;IACZ,UAAU;IACV,eAAe;IACf,mBAAmB;IACnB,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,iBAAiB;IACjB,IAAI;IACJ,KAAK;IACL,WAAW;IACX,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,WAAW;IACX,YAAY;IACZ,UAAU;IACV,MAAM;IACN,cAAc;IACd,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,WAAW;IACX,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,SAAS;IACT,OAAO;IACP,QAAQ;IACR,aAAa;IACb,QAAQ;IACR,UAAU;IACV,aAAa;IACb,MAAM;IACN,YAAY;IACZ,qBAAqB;IACrB,kBAAkB;IAClB,cAAc;IACd,QAAQ;IACR,eAAe;IACf,qBAAqB;IACrB,gBAAgB;IAChB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,aAAa;IACb,WAAW;IACX,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,iBAAiB;IACjB,OAAO;IACP,kBAAkB;IAClB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,gBAAgB;IAChB,QAAQ;IACR,cAAc;IACd,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;IACP,cAAc;IACd,MAAM;IACN,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,YAAY;CACJ,CAAC,CAAA;AAEJ,MAAMgF,MAAM,GAAGhF,MAAM,CAAC;IAC3B,QAAQ;IACR,aAAa;IACb,OAAO;IACP,UAAU;IACV,OAAO;IACP,cAAc;IACd,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,KAAK;IACL,SAAS;IACT,cAAc;IACd,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,eAAe;IACf,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,WAAW;IACX,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,eAAe;IACf,UAAU;IACV,UAAU;IACV,MAAM;IACN,UAAU;IACV,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,eAAe;IACf,sBAAsB;IACtB,WAAW;IACX,WAAW;IACX,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,SAAS;IACT,OAAO;IACP,OAAO;CACR,CAAC,CAAA;AAEK,MAAMmF,GAAG,GAAGnF,MAAM,CAAC;IACxB,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,WAAW;IACX,aAAa;CACL,CAAC;AChXX,gDAAA;AACO,MAAMoF,aAAa,GAAGnF,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAA,+DAAA;AACxD,MAAMoF,QAAQ,GAAGpF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC9C,MAAMqF,WAAW,GAAGrF,IAAI,CAAC,eAAe,CAAC,CAAC,CAAA,2CAAA;AAC1C,MAAMsF,SAAS,GAAGtF,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAA,wCAAA;AACvD,MAAMuF,SAAS,GAAGvF,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA,wCAAA;AACzC,MAAMwF,cAAc,GAAGxF,IAAI,CAChC,2FAA2F,CAAA,wCAAA;;AAEtF,MAAMyF,iBAAiB,GAAGzF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AACvD,MAAM0F,eAAe,GAAG1F,IAAI,CACjC,6DAA6D,CAAA,uCAAA;;AAExD,MAAM2F,YAAY,GAAG3F,IAAI,CAAC,SAAS,CAAC,CAAA;AACpC,MAAM4F,cAAc,GAAG5F,IAAI,CAAC,0BAA0B,CAAC;;;;;;;;;;;;;;AChB9D,4CAAA,GAkCA,iEAAA;AACA,MAAM6F,SAAS,GAAG;IAChBnC,OAAO,EAAE,CAAC;IACVoC,SAAS,EAAE,CAAC;IACZb,IAAI,EAAE,CAAC;IACPc,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAAE,aAAA;IACpBC,UAAU,EAAE,CAAC;IAAE,aAAA;IACfC,sBAAsB,EAAE,CAAC;IACzBC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,QAAQ,EAAE,EAAE,CAAA,aAAA;CACb,CAAA;AAED,MAAMC,SAAS,GAAG,SAAZA,SAASA,GAAG;IAChB,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;AACtD,CAAC,CAAA;AAED;;;;;;;CAOG,GACH,MAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAC7BC,YAAsC,EACtCC,iBAAoC,EAAA;IAEpC,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAC/C;QACA,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,sDAAA;IACA,8EAAA;IACA,gEAAA;IACA,IAAIC,MAAM,GAAG,IAAI,CAAA;IACjB,MAAMC,SAAS,GAAG,uBAAuB,CAAA;IACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;QAClED,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC,CAAA;IACpD,CAAA;IAEA,MAAMG,UAAU,GAAG,WAAW,GAAA,CAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;IAE7D,IAAI;QACF,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;YAC3CC,UAAUA,EAACxC,IAAI,EAAA;gBACb,OAAOA,IAAI,CAAA;aACZ;YACDyC,eAAeA,EAACC,SAAS,EAAA;gBACvB,OAAOA,SAAS,CAAA;YAClB,CAAA;QACD,CAAA,CAAC,CAAA;KACH,CAAC,OAAOC,CAAC,EAAE;QACV,mEAAA;QACA,yEAAA;QACA,sBAAA;QACAC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAGN,UAAU,GAAG,wBAAwB,CAC/D,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAA;AACF,CAAC,CAAA;AAED,MAAMO,eAAe,GAAG,SAAlBA,eAAeA,GAAG;IACtB,OAAO;QACLC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,sBAAsB,EAAE,EAAE;QAC1BC,wBAAwB,EAAE,EAAE;QAC5BC,sBAAsB,EAAE,EAAE;QAC1BC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,mBAAmB,EAAE,EAAE;QACvBC,sBAAsB,EAAE,EAAA;KACzB,CAAA;AACH,CAAC,CAAA;AAED,SAASC,eAAeA,GAAiC;IAAA,IAAhC1B,MAAqB,IAAAzD,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAoF,CAAAA,CAAAA,KAAAA,SAAA,GAAApF,SAAA,CAAAwD,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;IACvD,MAAM6B,SAAS,IAAeC,IAAgB,GAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;IAExED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;IAE3BH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;IAEtB,IACE,CAAChC,MAAM,KACP,CAACA,MAAM,EAACL,QAAQ,IAChBK,MAAM,EAACL,QAAQ,CAACsC,QAAQ,KAAK7C,SAAS,CAACO,QAAQ,IAC/C,CAACK,MAAM,EAACkC,OAAO,EACf;QACA,uDAAA;QACA,uCAAA;QACAN,SAAS,CAACO,WAAW,GAAG,KAAK,CAAA;QAE7B,OAAOP,SAAS,CAAA;IAClB,CAAA;IAEA,IAAI,EAAEjC,QAAAA,EAAU,GAAGK,MAAM,CAAA;IAEzB,MAAMoC,gBAAgB,GAAGzC,QAAQ,CAAA;IACjC,MAAM0C,aAAa,GACjBD,gBAAgB,CAACC,aAAkC,CAAA;IACrD,MAAM,EACJC,gBAAgB,EAChBC,mBAAmB,EACnBC,IAAI,EACJN,OAAO,EACPO,UAAU,EACVC,YAAY,GAAG1C,MAAM,EAAC0C,YAAY,IAAK1C,MAAc,EAAC2C,eAAe,EACrEC,eAAe,EACfC,SAAS,EACT3C,YAAAA,EACD,GAAGF,MAAM,CAAA;IAEV,MAAM8C,gBAAgB,GAAGZ,OAAO,CAAC9H,SAAS,CAAA;IAE1C,MAAM2I,SAAS,GAAGlF,YAAY,CAACiF,gBAAgB,EAAE,WAAW,CAAC,CAAA;IAC7D,MAAME,MAAM,GAAGnF,YAAY,CAACiF,gBAAgB,EAAE,QAAQ,CAAC,CAAA;IACvD,MAAMG,cAAc,GAAGpF,YAAY,CAACiF,gBAAgB,EAAE,aAAa,CAAC,CAAA;IACpE,MAAMI,aAAa,GAAGrF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAClE,MAAMK,aAAa,GAAGtF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAElE,kEAAA;IACA,+DAAA;IACA,oFAAA;IACA,uEAAA;IACA,oEAAA;IACA,gBAAA;IACA,IAAI,OAAOP,mBAAmB,KAAK,UAAU,EAAE;QAC7C,MAAMa,QAAQ,GAAGzD,QAAQ,CAAC0D,aAAa,CAAC,UAAU,CAAC,CAAA;QACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;YACtD5D,QAAQ,GAAGyD,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;QAC3C,CAAA;IACF,CAAA;IAEA,IAAIC,kBAAkB,CAAA;IACtB,IAAIC,SAAS,GAAG,EAAE,CAAA;IAElB,MAAM,EACJC,cAAc,EACdC,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAAA,EACD,GAAGlE,QAAQ,CAAA;IACZ,MAAM,EAAEmE,UAAAA,EAAY,GAAG1B,gBAAgB,CAAA;IAEvC,IAAI2B,KAAK,GAAG/C,eAAe,EAAE,CAAA;IAE7B;;GAEG,GACHY,SAAS,CAACO,WAAW,GACnB,OAAOnJ,OAAO,KAAK,UAAU,IAC7B,OAAOmK,aAAa,KAAK,UAAU,IACnCO,cAAc,IACdA,cAAc,CAACM,kBAAkB,KAAKrC,SAAS,CAAA;IAEjD,MAAM,EACJjD,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTE,iBAAiB,EACjBC,eAAe,EACfE,cAAAA,EACD,GAAG8E,WAAW,CAAA;IAEf,IAAI,EAAElF,gBAAAA,gBAAAA,EAAgB,GAAGkF,WAAW,CAAA;IAEpC;;;GAGG,GAEH,yBAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAGvH,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAGwH,MAAS,EACZ;WAAGA,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,QAAW,EACd;WAAGA,IAAS;KACb,CAAC,CAAA;IAEF,2BAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAG1H,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAG2H,IAAU,EACb;WAAGA,GAAS,EACZ;WAAGA,MAAY,EACf;WAAGA,GAAS;KACb,CAAC,CAAA;IAEF;;;;;GAKG,GACH,IAAIC,uBAAuB,GAAGnL,MAAM,CAACE,IAAI,CACvCC,MAAM,CAAC,IAAI,EAAE;QACXiL,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDmH,kBAAkB,EAAE;YAClBH,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDoH,8BAA8B,EAAE;YAC9BJ,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,KAAA;QACR,CAAA;IACF,CAAA,CAAC,CACH,CAAA;IAED,+DAAA,GACA,IAAIqH,WAAW,GAAG,IAAI,CAAA;IAEtB,qEAAA,GACA,IAAIC,WAAW,GAAG,IAAI,CAAA;IAEtB,sCAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,6CAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,wCAAA,GACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;IAEnC;uDACuD,GACvD,IAAIC,wBAAwB,GAAG,IAAI,CAAA;IAEnC;;GAEG,GACH,IAAIC,kBAAkB,GAAG,KAAK,CAAA;IAE9B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB,wDAAA,GACA,IAAIC,cAAc,GAAG,KAAK,CAAA;IAE1B,sEAAA,GACA,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;0EAC0E,GAC1E,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;;;GAGG,GACH,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;sEACsE,GACtE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;2CAC2C,GAC3C,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;;;;;;;;;;;;GAYG,GACH,IAAIC,oBAAoB,GAAG,KAAK,CAAA;IAChC,MAAMC,2BAA2B,GAAG,eAAe,CAAA;IAEnD,+CAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;wEACwE,GACxE,IAAIC,QAAQ,GAAG,KAAK,CAAA;IAEpB,qDAAA,GACA,IAAIC,YAAY,GAA8B,CAAA,CAAE,CAAA;IAEhD,uDAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAC1B,MAAMC,uBAAuB,GAAGxJ,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC3C,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,MAAM;QACN,eAAe;QACf,MAAM;QACN,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,QAAQ;QACR,OAAO;QACP,KAAK;QACL,UAAU;QACV,OAAO;QACP,OAAO;QACP,OAAO;QACP,KAAK;KACN,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIyJ,aAAa,GAAG,IAAI,CAAA;IACxB,MAAMC,qBAAqB,GAAG1J,QAAQ,CAAC,CAAA,CAAE,EAAE;QACzC,OAAO;QACP,OAAO;QACP,KAAK;QACL,QAAQ;QACR,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,iDAAA,GACA,IAAI2J,mBAAmB,GAAG,IAAI,CAAA;IAC9B,MAAMC,2BAA2B,GAAG5J,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC/C,KAAK;QACL,OAAO;QACP,KAAK;QACL,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,MAAM6J,gBAAgB,GAAG,oCAAoC,CAAA;IAC7D,MAAMC,aAAa,GAAG,4BAA4B,CAAA;IAClD,MAAMC,cAAc,GAAG,8BAA8B,CAAA;IACrD,sBAAA,GACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;IAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;IAE1B,gCAAA,GACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;IAC7B,MAAMC,0BAA0B,GAAGnK,QAAQ,CACzC,CAAA,CAAE,EACF;QAAC6J,gBAAgB;QAAEC,aAAa;QAAEC,cAAc;KAAC,EACjD1L,cAAc,CACf,CAAA;IAED,IAAI+L,8BAA8B,GAAGpK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;KACR,CAAC,CAAA;IAEF,IAAIqK,uBAAuB,GAAGrK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAAC,gBAAgB;KAAC,CAAC,CAAA;IAE9D,oDAAA;IACA,gDAAA;IACA,kDAAA;IACA,kBAAA;IACA,MAAMsK,4BAA4B,GAAGtK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,OAAO;QACP,OAAO;QACP,MAAM;QACN,GAAG;QACH,QAAQ;KACT,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIuK,iBAAiB,GAAkC,IAAI,CAAA;IAC3D,MAAMC,4BAA4B,GAAG;QAAC,uBAAuB;QAAE,WAAW;KAAC,CAAA;IAC3E,MAAMC,yBAAyB,GAAG,WAAW,CAAA;IAC7C,IAAItK,iBAAiB,GAA0C,IAAI,CAAA;IAEnE,+CAAA,GACA,IAAIuK,MAAM,GAAkB,IAAI,CAAA;IAEhC,kDAAA,GACA,kDAAA,GAEA,MAAMC,WAAW,GAAG5H,QAAQ,CAAC0D,aAAa,CAAC,MAAM,CAAC,CAAA;IAElD,MAAMmE,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,SAAkB,EAAA;QAElB,OAAOA,SAAS,YAAY3L,MAAM,IAAI2L,SAAS,YAAYC,QAAQ,CAAA;KACpE,CAAA;IAED;;;;GAIG,GACH,sCAAA;IACA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,GAA6B;QAAA,IAAhBC,GAAA,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAc,CAAA,CAAE,CAAA;QAC7C,IAAI+K,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;YAC5B,OAAA;QACF,CAAA;QAEA,8CAAA,GACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACnCA,GAAG,GAAG,CAAA,CAAE,CAAA;QACV,CAAA;QAEA,wDAAA,GACAA,GAAG,GAAGtK,KAAK,CAACsK,GAAG,CAAC,CAAA;QAEhBT,iBAAiB,GACf,mDAAA;QACAC,4BAA4B,CAAC5L,OAAO,CAACoM,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC9DE,yBAAyB,GACzBO,GAAG,CAACT,iBAAiB,CAAA;QAE3B,iGAAA;QACApK,iBAAiB,GACfoK,iBAAiB,KAAK,uBAAuB,GACzClM,cAAc,GACdH,iBAAiB,CAAA;QAEvB,gCAAA,GACAoJ,YAAY,GAAGvI,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC1D,YAAY,EAAEnH,iBAAiB,CAAC,GACjDoH,oBAAoB,CAAA;QACxBE,YAAY,GAAG1I,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACvD,YAAY,EAAEtH,iBAAiB,CAAC,GACjDuH,oBAAoB,CAAA;QACxBwC,kBAAkB,GAAGnL,oBAAoB,CAACiM,GAAG,EAAE,oBAAoB,CAAC,GAChEhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACd,kBAAkB,EAAE7L,cAAc,CAAC,GACpD8L,0BAA0B,CAAA;QAC9BR,mBAAmB,GAAG5K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAChEhL,QAAQ,CACNU,KAAK,CAACkJ,2BAA2B,CAAC,EAClCoB,GAAG,CAACC,iBAAiB,EACrB9K,iBAAiB,CAClB,GACDyJ,2BAA2B,CAAA;QAC/BH,aAAa,GAAG1K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAC1DhL,QAAQ,CACNU,KAAK,CAACgJ,qBAAqB,CAAC,EAC5BsB,GAAG,CAACE,iBAAiB,EACrB/K,iBAAiB,CAClB,GACDuJ,qBAAqB,CAAA;QACzBH,eAAe,GAAGxK,oBAAoB,CAACiM,GAAG,EAAE,iBAAiB,CAAC,GAC1DhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,GACpDqJ,uBAAuB,CAAA;QAC3BrB,WAAW,GAAGpJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC7C,WAAW,EAAEhI,iBAAiB,CAAC,GAChD,CAAA,CAAE,CAAA;QACNiI,WAAW,GAAGrJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC5C,WAAW,EAAEjI,iBAAiB,CAAC,GAChD,CAAA,CAAE,CAAA;QACNmJ,YAAY,GAAGvK,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDA,GAAG,CAAC1B,YAAY,GAChB,KAAK,CAAA;QACTjB,eAAe,GAAG2C,GAAG,CAAC3C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,eAAe,GAAG0C,GAAG,CAAC1C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,uBAAuB,GAAGyC,GAAG,CAACzC,uBAAuB,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC/DC,wBAAwB,GAAGwC,GAAG,CAACxC,wBAAwB,KAAK,KAAK,CAAC,CAAA,eAAA;QAClEC,kBAAkB,GAAGuC,GAAG,CAACvC,kBAAkB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrDC,YAAY,GAAGsC,GAAG,CAACtC,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,cAAc,GAAGqC,GAAG,CAACrC,cAAc,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC7CG,UAAU,GAAGkC,GAAG,CAAClC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCC,mBAAmB,GAAGiC,GAAG,CAACjC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDC,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDH,UAAU,GAAGmC,GAAG,CAACnC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCI,YAAY,GAAG+B,GAAG,CAAC/B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,oBAAoB,GAAG8B,GAAG,CAAC9B,oBAAoB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACzDE,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,QAAQ,GAAG2B,GAAG,CAAC3B,QAAQ,IAAI,KAAK,CAAC,CAAA,gBAAA;QACjClH,gBAAc,GAAG6I,GAAG,CAACG,kBAAkB,IAAI9D,cAA0B,CAAA;QACrE2C,SAAS,GAAGgB,GAAG,CAAChB,SAAS,IAAID,cAAc,CAAA;QAC3CK,8BAA8B,GAC5BY,GAAG,CAACZ,8BAA8B,IAAIA,8BAA8B,CAAA;QACtEC,uBAAuB,GACrBW,GAAG,CAACX,uBAAuB,IAAIA,uBAAuB,CAAA;QAExDzC,uBAAuB,GAAGoD,GAAG,CAACpD,uBAAuB,IAAI,CAAA,CAAE,CAAA;QAC3D,IACEoD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;YACAD,uBAAuB,CAACC,YAAY,GAClCmD,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAA;QAC5C,CAAA;QAEA,IACEmD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;YACAL,uBAAuB,CAACK,kBAAkB,GACxC+C,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAA;QAClD,CAAA;QAEA,IACE+C,GAAG,CAACpD,uBAAuB,IAC3B,OAAOoD,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;YACAN,uBAAuB,CAACM,8BAA8B,GACpD8C,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,CAAA;QAC9D,CAAA;QAEA,IAAIO,kBAAkB,EAAE;YACtBH,eAAe,GAAG,KAAK,CAAA;QACzB,CAAA;QAEA,IAAIS,mBAAmB,EAAE;YACvBD,UAAU,GAAG,IAAI,CAAA;QACnB,CAAA;QAEA,sBAAA,GACA,IAAIQ,YAAY,EAAE;YAChBhC,YAAY,GAAGtH,QAAQ,CAAC,CAAA,CAAE,EAAEwH,IAAS,CAAC,CAAA;YACtCC,YAAY,GAAG,EAAE,CAAA;YACjB,IAAI6B,YAAY,CAAChI,IAAI,KAAK,IAAI,EAAE;gBAC9BtB,QAAQ,CAACsH,YAAY,EAAEE,MAAS,CAAC,CAAA;gBACjCxH,QAAQ,CAACyH,YAAY,EAAEE,IAAU,CAAC,CAAA;YACpC,CAAA;YAEA,IAAI2B,YAAY,CAAC/H,GAAG,KAAK,IAAI,EAAE;gBAC7BvB,QAAQ,CAACsH,YAAY,EAAEE,KAAQ,CAAC,CAAA;gBAChCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC9H,UAAU,KAAK,IAAI,EAAE;gBACpCxB,QAAQ,CAACsH,YAAY,EAAEE,UAAe,CAAC,CAAA;gBACvCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC5H,MAAM,KAAK,IAAI,EAAE;gBAChC1B,QAAQ,CAACsH,YAAY,EAAEE,QAAW,CAAC,CAAA;gBACnCxH,QAAQ,CAACyH,YAAY,EAAEE,MAAY,CAAC,CAAA;gBACpC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,kCAAA,GACA,IAAIqD,GAAG,CAACI,QAAQ,EAAE;YAChB,IAAI9D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG5G,KAAK,CAAC4G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAtH,QAAQ,CAACsH,YAAY,EAAE0D,GAAG,CAACI,QAAQ,EAAEjL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACK,QAAQ,EAAE;YAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG/G,KAAK,CAAC+G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAzH,QAAQ,CAACyH,YAAY,EAAEuD,GAAG,CAACK,QAAQ,EAAElL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACC,iBAAiB,EAAE;YACzBjL,QAAQ,CAAC2J,mBAAmB,EAAEqB,GAAG,CAACC,iBAAiB,EAAE9K,iBAAiB,CAAC,CAAA;QACzE,CAAA;QAEA,IAAI6K,GAAG,CAACzB,eAAe,EAAE;YACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;gBAC/CD,eAAe,GAAG7I,KAAK,CAAC6I,eAAe,CAAC,CAAA;YAC1C,CAAA;YAEAvJ,QAAQ,CAACuJ,eAAe,EAAEyB,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,CAAA;QACnE,CAAA;QAEA,iDAAA,GACA,IAAIiJ,YAAY,EAAE;YAChB9B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QAC9B,CAAA;QAEA,0EAAA,GACA,IAAIqB,cAAc,EAAE;YAClB3I,QAAQ,CAACsH,YAAY,EAAE;gBAAC,MAAM;gBAAE,MAAM;gBAAE,MAAM;aAAC,CAAC,CAAA;QAClD,CAAA;QAEA,0EAAA,GACA,IAAIA,YAAY,CAACgE,KAAK,EAAE;YACtBtL,QAAQ,CAACsH,YAAY,EAAE;gBAAC,OAAO;aAAC,CAAC,CAAA;YACjC,OAAOa,WAAW,CAACoD,KAAK,CAAA;QAC1B,CAAA;QAEA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;YAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAAC1H,UAAU,KAAK,UAAU,EAAE;gBAC7D,MAAM1E,eAAe,CACnB,6EAA6E,CAC9E,CAAA;YACH,CAAA;YAEA,IAAI,OAAO4L,GAAG,CAACQ,oBAAoB,CAACzH,eAAe,KAAK,UAAU,EAAE;gBAClE,MAAM3E,eAAe,CACnB,kFAAkF,CACnF,CAAA;YACH,CAAA;YAEA,0CAAA;YACAwH,kBAAkB,GAAGoE,GAAG,CAACQ,oBAAoB,CAAA;YAE7C,+CAAA;YACA3E,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;QAC/C,CAAC,MAAM;YACL,6EAAA;YACA,IAAI8C,kBAAkB,KAAK7B,SAAS,EAAE;gBACpC6B,kBAAkB,GAAGvD,yBAAyB,CAC5CC,YAAY,EACZmC,aAAa,CACd,CAAA;YACH,CAAA;YAEA,qEAAA;YACA,IAAImB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;gBAChEA,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;YAC/C,CAAA;QACF,CAAA;QAEA,iDAAA;QACA,uCAAA;QACA,IAAIpH,MAAM,EAAE;YACVA,MAAM,CAACsO,GAAG,CAAC,CAAA;QACb,CAAA;QAEAN,MAAM,GAAGM,GAAG,CAAA;KACb,CAAA;IAED;;gBAEgB,GAChB,MAAMS,YAAY,GAAGzL,QAAQ,CAAC,CAAA,CAAE,EAAE,CAChC;WAAGwH,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,aAAkB;KACtB,CAAC,CAAA;IACF,MAAMkE,eAAe,GAAG1L,QAAQ,CAAC,CAAA,CAAE,EAAE,CACnC;WAAGwH,QAAW,EACd;WAAGA,gBAAqB;KACzB,CAAC,CAAA;IAEF;;;;;GAKG,GACH,MAAMmE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAatL,OAAgB,EAAA;QACrD,IAAIuL,MAAM,GAAGrF,aAAa,CAAClG,OAAO,CAAC,CAAA;QAEnC,wDAAA;QACA,qDAAA;QACA,IAAI,CAACuL,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;YAC9BD,MAAM,GAAG;gBACPE,YAAY,EAAE9B,SAAS;gBACvB6B,OAAO,EAAE,UAAA;aACV,CAAA;QACH,CAAA;QAEA,MAAMA,OAAO,GAAG3N,iBAAiB,CAACmC,OAAO,CAACwL,OAAO,CAAC,CAAA;QAClD,MAAME,aAAa,GAAG7N,iBAAiB,CAAC0N,MAAM,CAACC,OAAO,CAAC,CAAA;QAEvD,IAAI,CAAC3B,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAA;QACd,CAAA;QAEA,IAAIzL,OAAO,CAACyL,YAAY,KAAKhC,aAAa,EAAE;YAC1C,oDAAA;YACA,sDAAA;YACA,uBAAA;YACA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,KAAK,CAAA;YAC1B,CAAA;YAEA,oDAAA;YACA,qDAAA;YACA,2BAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;gBAC5C,OACEgC,OAAO,KAAK,KAAK,IAAA,CAChBE,aAAa,KAAK,gBAAgB,IACjC3B,8BAA8B,CAAC2B,aAAa,CAAC,CAAC,CAAA;YAEpD,CAAA;YAEA,iDAAA;YACA,oDAAA;YACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QACvC,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAKjC,gBAAgB,EAAE;YAC7C,uDAAA;YACA,uDAAA;YACA,uBAAA;YACA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,MAAM,CAAA;YAC3B,CAAA;YAEA,mDAAA;YACA,qCAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;gBACzC,OAAO+B,OAAO,KAAK,MAAM,IAAIxB,uBAAuB,CAAC0B,aAAa,CAAC,CAAA;YACrE,CAAA;YAEA,oDAAA;YACA,uDAAA;YACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;QAC1C,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAK/B,cAAc,EAAE;YAC3C,iDAAA;YACA,mDAAA;YACA,wCAAA;YACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACO,uBAAuB,CAAC0B,aAAa,CAAC,EACvC;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACO,8BAA8B,CAAC2B,aAAa,CAAC,EAC9C;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,gDAAA;YACA,mDAAA;YACA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,IAAA,CACxBvB,4BAA4B,CAACuB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QAErE,CAAA;QAEA,6DAAA;QACA,IACEtB,iBAAiB,KAAK,uBAAuB,IAC7CL,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EACxC;YACA,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,qDAAA;QACA,qDAAA;QACA,wDAAA;QACA,6BAAA;QACA,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;GAIG,GACH,MAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAU,EAAA;QACvCpO,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;YAAE/E,OAAO,EAAE6L,IAAAA;QAAM,CAAA,CAAC,CAAA;QAE/C,IAAI;YACF,0DAAA;YACA3F,aAAa,CAAC2F,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC,CAAA;SACtC,CAAC,OAAOjI,CAAC,EAAE;YACVmC,MAAM,CAAC8F,IAAI,CAAC,CAAA;QACd,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAY,EAAEhM,OAAgB,EAAA;QAC/D,IAAI;YACFvC,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAEpC,OAAO,CAACiM,gBAAgB,CAACD,IAAI,CAAC;gBACzCE,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;SACH,CAAC,OAAO4D,CAAC,EAAE;YACVnG,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAE,IAAI;gBACf8J,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;QACJ,CAAA;QAEAA,OAAO,CAACmM,eAAe,CAACH,IAAI,CAAC,CAAA;QAE7B,2DAAA;QACA,IAAIA,IAAI,KAAK,IAAI,EAAE;YACjB,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;gBACrC,IAAI;oBACFkD,YAAY,CAAC5L,OAAO,CAAC,CAAA;gBACvB,CAAC,CAAC,OAAO4D,CAAC,EAAE,CAAA,CAAC;YACf,CAAC,MAAM;gBACL,IAAI;oBACF5D,OAAO,CAACoM,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC,CAAA;gBAChC,CAAC,CAAC,OAAOpI,CAAC,EAAE,CAAA,CAAC;YACf,CAAA;QACF,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAMyI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAa,EAAA;QAC3C,0BAAA,GACA,IAAIC,GAAG,GAAG,IAAI,CAAA;QACd,IAAIC,iBAAiB,GAAG,IAAI,CAAA;QAE5B,IAAIhE,UAAU,EAAE;YACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;QACrC,CAAC,MAAM;YACL,+EAAA,GACA,MAAMG,OAAO,GAAGvO,WAAW,CAACoO,KAAK,EAAE,aAAa,CAAC,CAAA;YACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;QAC3C,CAAA;QAEA,IACEvC,iBAAiB,KAAK,uBAAuB,IAC7CP,SAAS,KAAKD,cAAc,EAC5B;YACA,4GAAA;YACA4C,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;QACpB,CAAA;QAEA,MAAMI,YAAY,GAAGnG,kBAAkB,GACnCA,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;QACT;;;KAGG,GACH,IAAI3C,SAAS,KAAKD,cAAc,EAAE;YAChC,IAAI;gBACF6C,GAAG,GAAG,IAAI3G,SAAS,EAAE,CAAC+G,eAAe,CAACD,YAAY,EAAExC,iBAAiB,CAAC,CAAA;YACxE,CAAC,CAAC,OAAOtG,CAAC,EAAE,CAAA,CAAC;QACf,CAAA;QAEA,6DAAA,GACA,IAAI,CAAC2I,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;YAChCL,GAAG,GAAG9F,cAAc,CAACoG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAChE,IAAI;gBACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAC1CpD,SAAS,GACTkG,YAAY,CAAA;aACjB,CAAC,OAAO9I,CAAC,EAAE;YACV,8CAAA;YAAA,CAAA;QAEJ,CAAA;QAEA,MAAMmJ,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;QAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;YAC9BO,IAAI,CAACC,YAAY,CACftK,QAAQ,CAACuK,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAC3B,CAAA;QACH,CAAA;QAEA,2CAAA,GACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;YAChC,OAAO9C,oBAAoB,CAACuG,IAAI,CAC9BZ,GAAG,EACHjE,cAAc,GAAG,MAAM,GAAG,MAAM,CACjC,CAAC,CAAC,CAAC,CAAA;QACN,CAAA;QAEA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;KACnD,CAAA;IAED;;;;;GAKG,GACH,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAaxI,IAAU,EAAA;QAC9C,OAAO8B,kBAAkB,CAACyG,IAAI,CAC5BvI,IAAI,CAAC0B,aAAa,IAAI1B,IAAI,EAC1BA,IAAI,EACJ,sCAAA;QACAY,UAAU,CAAC6H,YAAY,GACrB7H,UAAU,CAAC8H,YAAY,GACvB9H,UAAU,CAAC+H,SAAS,GACpB/H,UAAU,CAACgI,2BAA2B,GACtChI,UAAU,CAACiI,kBAAkB,EAC/B,IAAI,CACL,CAAA;KACF,CAAA;IAED;;;;;GAKG,GACH,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAa1N,OAAgB,EAAA;QAC7C,OACEA,OAAO,YAAY2F,eAAe,IAAA,CACjC,OAAO3F,OAAO,CAAC2N,QAAQ,KAAK,QAAQ,IACnC,OAAO3N,OAAO,CAAC4N,WAAW,KAAK,QAAQ,IACvC,OAAO5N,OAAO,CAAC8L,WAAW,KAAK,UAAU,IACzC,CAAA,CAAE9L,OAAO,CAAC6N,UAAU,YAAYpI,YAAY,CAAC,IAC7C,OAAOzF,OAAO,CAACmM,eAAe,KAAK,UAAU,IAC7C,OAAOnM,OAAO,CAACoM,YAAY,KAAK,UAAU,IAC1C,OAAOpM,OAAO,CAACyL,YAAY,KAAK,QAAQ,IACxC,OAAOzL,OAAO,CAACgN,YAAY,KAAK,UAAU,IAC1C,OAAOhN,OAAO,CAAC8N,aAAa,KAAK,UAAU,CAAC,CAAA;KAEjD,CAAA;IAED;;;;;GAKG,GACH,MAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAatN,KAAc,EAAA;QACtC,OAAO,OAAO8E,IAAI,KAAK,UAAU,IAAI9E,KAAK,YAAY8E,IAAI,CAAA;KAC3D,CAAA;IAED,SAASyI,aAAaA,CAOpBlH,KAAU,EAAEmH,WAA6B,EAAEC,IAAsB,EAAA;QACjElR,YAAY,CAAC8J,KAAK,GAAGqH,IAAI,IAAI;YAC3BA,IAAI,CAAChB,IAAI,CAACxI,SAAS,EAAEsJ,WAAW,EAAEC,IAAI,EAAE7D,MAAM,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACJ,CAAA;IAEA;;;;;;;;GAQG,GACH,MAAM+D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAgB,EAAA;QAClD,IAAI5H,OAAO,GAAG,IAAI,CAAA;QAElB,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC1C,sBAAsB,EAAE6J,WAAW,EAAE,IAAI,CAAC,CAAA;QAE9D,gDAAA,GACA,IAAIP,YAAY,CAACO,WAAW,CAAC,EAAE;YAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,MAAMzC,OAAO,GAAG1L,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;QAEvD,6BAAA,GACAK,aAAa,CAAClH,KAAK,CAACvC,mBAAmB,EAAE0J,WAAW,EAAE;YACpDzC,OAAO;YACP6C,WAAW,EAAEpH,YAAAA;QACd,CAAA,CAAC,CAAA;QAEF,oDAAA,GACA,IACEgH,WAAW,CAACH,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACE,WAAW,CAACK,iBAAiB,CAAC,IACvC1P,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACnB,SAAS,CAAC,IAC7ClO,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACL,WAAW,CAAC,EAC/C;YACAhC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,oDAAA,GACA,IAAIA,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACK,sBAAsB,EAAE;YAC7DoJ,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IACE5F,YAAY,IACZ4F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACM,OAAO,IAC1C7D,UAAU,CAAC,SAAS,EAAEqP,WAAW,CAACC,IAAI,CAAC,EACvC;YACAtC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,mDAAA,GACA,IAAI,CAAChH,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;YAClD,+CAAA,GACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAI+C,qBAAqB,CAAC/C,OAAO,CAAC,EAAE;gBAC3D,IACEjE,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EACzD;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;gBAEA,IACEjE,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACxDlD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAC7C;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;YACF,CAAA;YAEA,+CAAA,GACA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;gBAC7C,MAAMgD,UAAU,GAAGtI,aAAa,CAAC+H,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU,CAAA;gBACvE,MAAMtB,UAAU,GAAGjH,aAAa,CAACgI,WAAW,CAAC,IAAIA,WAAW,CAACf,UAAU,CAAA;gBAEvE,IAAIA,UAAU,IAAIsB,UAAU,EAAE;oBAC5B,MAAMC,UAAU,GAAGvB,UAAU,CAAC3N,MAAM,CAAA;oBAEpC,IAAK,IAAImP,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,CAAE;wBACxC,MAAMC,UAAU,GAAG7I,SAAS,CAACoH,UAAU,CAACwB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;wBACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;wBACjEJ,UAAU,CAACxB,YAAY,CAAC2B,UAAU,EAAE3I,cAAc,CAACiI,WAAW,CAAC,CAAC,CAAA;oBAClE,CAAA;gBACF,CAAA;YACF,CAAA;YAEArC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,IAAIA,WAAW,YAAYhJ,OAAO,IAAI,CAACqG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;YACxErC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,6DAAA,GACA,IACE,CAACzC,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxB5M,UAAU,CAAC,6BAA6B,EAAEqP,WAAW,CAACnB,SAAS,CAAC,EAChE;YACAlB,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IAAI7F,kBAAkB,IAAI6F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACZ,IAAI,EAAE;YACjE,kCAAA,GACA8E,OAAO,GAAG4H,WAAW,CAACL,WAAW,CAAA;YAEjC5Q,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5DxI,OAAO,GAAGjI,aAAa,CAACiI,OAAO,EAAEwI,IAAI,EAAE,GAAG,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;YAEF,IAAIZ,WAAW,CAACL,WAAW,KAAKvH,OAAO,EAAE;gBACvC5I,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;oBAAE/E,OAAO,EAAEiO,WAAW,CAACnI,SAAS,EAAE;gBAAA,CAAE,CAAC,CAAA;gBAClEmI,WAAW,CAACL,WAAW,GAAGvH,OAAO,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC7C,qBAAqB,EAAEgK,WAAW,EAAE,IAAI,CAAC,CAAA;QAE7D,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;;;;GAOG,GACH,sCAAA;IACA,MAAMa,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,KAAa,EACbC,MAAc,EACdvO,KAAa,EAAA;QAEb,sCAAA,GACA,IACEmI,YAAY,IAAA,CACXoG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,IAAA,CACrCvO,KAAK,IAAIiC,QAAQ,IAAIjC,KAAK,IAAI6J,WAAW,CAAC,EAC3C;YACA,OAAO,KAAK,CAAA;QACd,CAAA;QAEA;;;gEAG8D,GAC9D,IACErC,eAAe,IACf,CAACF,WAAW,CAACiH,MAAM,CAAC,IACpBpQ,UAAU,CAACgD,SAAS,EAAEoN,MAAM,CAAC,EAC7B,CAED;aAAM,IAAIhH,eAAe,IAAIpJ,UAAU,CAACiD,SAAS,EAAEmN,MAAM,CAAC,EAAE,CAG5D;aAAM,IAAI,CAAC5H,YAAY,CAAC4H,MAAM,CAAC,IAAIjH,WAAW,CAACiH,MAAM,CAAC,EAAE;YACvD,IACE,kGAAA;YACA,qGAAA;YACA,sHAAA;YACCT,qBAAqB,CAACQ,KAAK,CAAC,IAAA,CACzBxH,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEuH,KAAK,CAAC,IACtDxH,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAACuH,KAAK,CAAE,CAAC,IAAA,CAC/CxH,uBAAuB,CAACK,kBAAkB,YAAY/I,MAAM,IAC5DD,UAAU,CAAC2I,uBAAuB,CAACK,kBAAkB,EAAEoH,MAAM,CAAC,IAC7DzH,uBAAuB,CAACK,kBAAkB,YAAY6C,QAAQ,IAC7DlD,uBAAuB,CAACK,kBAAkB,CAACoH,MAAM,CAAE,CAAC,IAC1D,sEAAA;YACA,6FAAA;YACCA,MAAM,KAAK,IAAI,IACdzH,uBAAuB,CAACM,8BAA8B,IAAA,CACpDN,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAE/G,KAAK,CAAC,IACtD8G,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAAC/G,KAAK,CAAE,CAAE,EACpD,CAGD;iBAAM;gBACL,OAAO,KAAK,CAAA;YACd,CAAA;QACA,6DAAA,GACF,CAAC,MAAM,IAAI6I,mBAAmB,CAAC0F,MAAM,CAAC,EAAE,CAIvC;aAAM,IACLpQ,UAAU,CAACkD,gBAAc,EAAE1D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID;aAAM,IACL,CAACgN,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBzQ,aAAa,CAACmC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnC2I,aAAa,CAAC2F,KAAK,CAAC,EACpB,CAKD;aAAM,IACL7G,uBAAuB,IACvB,CAACtJ,UAAU,CAACmD,iBAAiB,EAAE3D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD;aAAM,IAAIvB,KAAK,EAAE;YAChB,OAAO,KAAK,CAAA;QACd,CAAC,MAAM,CAEL;QAGF,OAAO,IAAI,CAAA;KACZ,CAAA;IAED;;;;;;;GAOG,GACH,MAAM8N,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAa/C,OAAe,EAAA;QACrD,OAAOA,OAAO,KAAK,gBAAgB,IAAItN,WAAW,CAACsN,OAAO,EAAEtJ,cAAc,CAAC,CAAA;KAC5E,CAAA;IAED;;;;;;;;;GASG,GACH,MAAM+M,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAahB,WAAoB,EAAA;QACxD,6BAAA,GACAD,aAAa,CAAClH,KAAK,CAAC3C,wBAAwB,EAAE8J,WAAW,EAAE,IAAI,CAAC,CAAA;QAEhE,MAAM,EAAEJ,UAAAA,EAAY,GAAGI,WAAW,CAAA;QAElC,iEAAA,GACA,IAAI,CAACJ,UAAU,IAAIH,YAAY,CAACO,WAAW,CAAC,EAAE;YAC5C,OAAA;QACF,CAAA;QAEA,MAAMiB,SAAS,GAAG;YAChBC,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,IAAI;YACdC,iBAAiB,EAAElI,YAAY;YAC/BmI,aAAa,EAAE7K,SAAAA;SAChB,CAAA;QACD,IAAI3E,CAAC,GAAG8N,UAAU,CAACtO,MAAM,CAAA;QAEzB,4DAAA,GACA,MAAOQ,CAAC,EAAE,CAAE;YACV,MAAMyP,IAAI,GAAG3B,UAAU,CAAC9N,CAAC,CAAC,CAAA;YAC1B,MAAM,EAAEiM,IAAI,EAAEP,YAAY,EAAEhL,KAAK,EAAE2O,SAAAA,EAAW,GAAGI,IAAI,CAAA;YACrD,MAAMR,MAAM,GAAGlP,iBAAiB,CAACkM,IAAI,CAAC,CAAA;YAEtC,IAAIvL,KAAK,GAAGuL,IAAI,KAAK,OAAO,GAAGoD,SAAS,GAAG5Q,UAAU,CAAC4Q,SAAS,CAAC,CAAA;YAEhE,6BAAA,GACAF,SAAS,CAACC,QAAQ,GAAGH,MAAM,CAAA;YAC3BE,SAAS,CAACE,SAAS,GAAG3O,KAAK,CAAA;YAC3ByO,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;YACzBH,SAAS,CAACK,aAAa,GAAG7K,SAAS,CAAC,CAAA,2DAAA;YACpCsJ,aAAa,CAAClH,KAAK,CAACxC,qBAAqB,EAAE2J,WAAW,EAAEiB,SAAS,CAAC,CAAA;YAClEzO,KAAK,GAAGyO,SAAS,CAACE,SAAS,CAAA;YAE3B;;OAEG,GACH,IAAIvG,oBAAoB,IAAA,CAAKmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;gBAClE,uCAAA;gBACAjD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBAEnC,8EAAA;gBACAxN,KAAK,GAAGqI,2BAA2B,GAAGrI,KAAK,CAAA;YAC7C,CAAA;YAEA,gEAAA,GACA,IAAI4H,YAAY,IAAIzJ,UAAU,CAAC,+BAA+B,EAAE6B,KAAK,CAAC,EAAE;gBACtEsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,2CAAA,GACA,IAAIiB,SAAS,CAACK,aAAa,EAAE;gBAC3B,SAAA;YACF,CAAA;YAEA,oBAAA,GACAxD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;YAEnC,2CAAA,GACA,IAAI,CAACiB,SAAS,CAACG,QAAQ,EAAE;gBACvB,SAAA;YACF,CAAA;YAEA,8CAAA,GACA,IAAI,CAAClH,wBAAwB,IAAIvJ,UAAU,CAAC,MAAM,EAAE6B,KAAK,CAAC,EAAE;gBAC1DsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,kDAAA,GACA,IAAI7F,kBAAkB,EAAE;gBACtBpL,YAAY,CAAC;oBAACyE,aAAa;oBAAEC,QAAQ;oBAAEC,WAAW;iBAAC,GAAGkN,IAAI,IAAI;oBAC5DpO,KAAK,GAAGrC,aAAa,CAACqC,KAAK,EAAEoO,IAAI,EAAE,GAAG,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;YACJ,CAAA;YAEA,wCAAA,GACA,MAAME,KAAK,GAAGjP,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;YACrD,IAAI,CAACmB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,EAAE;gBAC5C,SAAA;YACF,CAAA;YAEA,gDAAA,GACA,IACE8F,kBAAkB,IAClB,OAAOtD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACwM,gBAAgB,KAAK,UAAU,EACnD;gBACA,IAAIhE,YAAY,EAAE,CAEjB;qBAAM;oBACL,OAAQxI,YAAY,CAACwM,gBAAgB,CAACV,KAAK,EAAEC,MAAM,CAAC;wBAClD,KAAK,aAAa;4BAAE;gCAClBvO,KAAK,GAAG8F,kBAAkB,CAAC9C,UAAU,CAAChD,KAAK,CAAC,CAAA;gCAC5C,MAAA;4BACF,CAAA;wBAEA,KAAK,kBAAkB;4BAAE;gCACvBA,KAAK,GAAG8F,kBAAkB,CAAC7C,eAAe,CAACjD,KAAK,CAAC,CAAA;gCACjD,MAAA;4BACF,CAAA;oBAKF,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,0DAAA,GACA,IAAI;gBACF,IAAIgL,YAAY,EAAE;oBAChBwC,WAAW,CAACyB,cAAc,CAACjE,YAAY,EAAEO,IAAI,EAAEvL,KAAK,CAAC,CAAA;gBACvD,CAAC,MAAM;oBACL,mFAAA,GACAwN,WAAW,CAAC7B,YAAY,CAACJ,IAAI,EAAEvL,KAAK,CAAC,CAAA;gBACvC,CAAA;gBAEA,IAAIiN,YAAY,CAACO,WAAW,CAAC,EAAE;oBAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;gBAC3B,CAAC,MAAM;oBACL1Q,QAAQ,CAACoH,SAAS,CAACI,OAAO,CAAC,CAAA;gBAC7B,CAAA;YACF,CAAC,CAAC,OAAOnB,CAAC,EAAE,CAAA,CAAC;QACf,CAAA;QAEA,6BAAA,GACAoK,aAAa,CAAClH,KAAK,CAAC9C,uBAAuB,EAAEiK,WAAW,EAAE,IAAI,CAAC,CAAA;KAChE,CAAA;IAED;;;;GAIG,GACH,MAAM0B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAA0B,EAAA;QAC7D,IAAIC,UAAU,GAAG,IAAI,CAAA;QACrB,MAAMC,cAAc,GAAG1C,mBAAmB,CAACwC,QAAQ,CAAC,CAAA;QAEpD,6BAAA,GACA5B,aAAa,CAAClH,KAAK,CAACzC,uBAAuB,EAAEuL,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE5D,MAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,CAAG;YAC/C,6BAAA,GACA/B,aAAa,CAAClH,KAAK,CAACtC,sBAAsB,EAAEqL,UAAU,EAAE,IAAI,CAAC,CAAA;YAE7D,8BAAA,GACAzB,iBAAiB,CAACyB,UAAU,CAAC,CAAA;YAE7B,yBAAA,GACAZ,mBAAmB,CAACY,UAAU,CAAC,CAAA;YAE/B,4BAAA,GACA,IAAIA,UAAU,CAACxJ,OAAO,YAAYhB,gBAAgB,EAAE;gBAClDsK,kBAAkB,CAACE,UAAU,CAACxJ,OAAO,CAAC,CAAA;YACxC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC5C,sBAAsB,EAAE0L,QAAQ,EAAE,IAAI,CAAC,CAAA;KAC5D,CAAA;IAED,sCAAA;IACAjL,SAAS,CAACqL,QAAQ,GAAG,SAAU1D,KAAK,EAAU;QAAA,IAAR3B,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QAC5C,IAAIyN,IAAI,GAAG,IAAI,CAAA;QACf,IAAIkD,YAAY,GAAG,IAAI,CAAA;QACvB,IAAIhC,WAAW,GAAG,IAAI,CAAA;QACtB,IAAIiC,UAAU,GAAG,IAAI,CAAA;QACrB;;+DAE6D,GAC7DtG,cAAc,GAAG,CAAC0C,KAAK,CAAA;QACvB,IAAI1C,cAAc,EAAE;YAClB0C,KAAK,GAAG,OAAO,CAAA;QACjB,CAAA;QAEA,yCAAA,GACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACyB,OAAO,CAACzB,KAAK,CAAC,EAAE;YAChD,IAAI,OAAOA,KAAK,CAACrO,QAAQ,KAAK,UAAU,EAAE;gBACxCqO,KAAK,GAAGA,KAAK,CAACrO,QAAQ,EAAE,CAAA;gBACxB,IAAI,OAAOqO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,MAAMvN,eAAe,CAAC,iCAAiC,CAAC,CAAA;gBAC1D,CAAA;YACF,CAAC,MAAM;gBACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;YACrD,CAAA;QACF,CAAA;QAEA,6CAAA,GACA,IAAI,CAAC4F,SAAS,CAACO,WAAW,EAAE;YAC1B,OAAOoH,KAAK,CAAA;QACd,CAAA;QAEA,sBAAA,GACA,IAAI,CAAC/D,UAAU,EAAE;YACfmC,YAAY,CAACC,GAAG,CAAC,CAAA;QACnB,CAAA;QAEA,6BAAA,GACAhG,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;QAEtB,kDAAA,GACA,IAAI,OAAOuH,KAAK,KAAK,QAAQ,EAAE;YAC7BtD,QAAQ,GAAG,KAAK,CAAA;QAClB,CAAA;QAEA,IAAIA,QAAQ,EAAE;YACZ,6DAAA,GACA,IAAKsD,KAAc,CAACqB,QAAQ,EAAE;gBAC5B,MAAMnC,OAAO,GAAG1L,iBAAiB,CAAEwM,KAAc,CAACqB,QAAQ,CAAC,CAAA;gBAC3D,IAAI,CAAC1G,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;oBAClD,MAAMzM,eAAe,CACnB,yDAAyD,CAC1D,CAAA;gBACH,CAAA;YACF,CAAA;QACF,CAAC,MAAM,IAAIuN,KAAK,YAAY/G,IAAI,EAAE;YAChC;+CAC2C,GAC3CwH,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;YAC/B4D,YAAY,GAAGlD,IAAI,CAACzG,aAAa,CAACO,UAAU,CAACyF,KAAK,EAAE,IAAI,CAAC,CAAA;YACzD,IACE2D,YAAY,CAACjL,QAAQ,KAAK7C,SAAS,CAACnC,OAAO,IAC3CiQ,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAChC;gBACA,qCAAA,GACAZ,IAAI,GAAGkD,YAAY,CAAA;YACrB,CAAC,MAAM,IAAIA,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAAE;gBAC3CZ,IAAI,GAAGkD,YAAY,CAAA;YACrB,CAAC,MAAM;gBACL,0DAAA;gBACAlD,IAAI,CAACoD,WAAW,CAACF,YAAY,CAAC,CAAA;YAChC,CAAA;QACF,CAAC,MAAM;YACL,0CAAA,GACA,IACE,CAACxH,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc,IACf,mDAAA;YACAgE,KAAK,CAAC/N,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;gBACA,OAAOgI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;YACX,CAAA;YAEA,sCAAA,GACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;YAE3B,0CAAA,GACA,IAAI,CAACS,IAAI,EAAE;gBACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGnC,SAAS,GAAG,EAAE,CAAA;YACjE,CAAA;QACF,CAAA;QAEA,yDAAA,GACA,IAAIuG,IAAI,IAAIvE,UAAU,EAAE;YACtBoD,YAAY,CAACmB,IAAI,CAACqD,UAAU,CAAC,CAAA;QAC/B,CAAA;QAEA,qBAAA,GACA,MAAMC,YAAY,GAAGjD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC,CAAA;QAEjE,iDAAA,GACA,MAAQkB,WAAW,GAAGoC,YAAY,CAACN,QAAQ,EAAE,CAAG;YAC9C,8BAAA,GACA3B,iBAAiB,CAACH,WAAW,CAAC,CAAA;YAE9B,yBAAA,GACAgB,mBAAmB,CAAChB,WAAW,CAAC,CAAA;YAEhC,oCAAA,GACA,IAAIA,WAAW,CAAC5H,OAAO,YAAYhB,gBAAgB,EAAE;gBACnDsK,kBAAkB,CAAC1B,WAAW,CAAC5H,OAAO,CAAC,CAAA;YACzC,CAAA;QACF,CAAA;QAEA,gDAAA,GACA,IAAI2C,QAAQ,EAAE;YACZ,OAAOsD,KAAK,CAAA;QACd,CAAA;QAEA,kCAAA,GACA,IAAI7D,UAAU,EAAE;YACd,IAAIC,mBAAmB,EAAE;gBACvBwH,UAAU,GAAGvJ,sBAAsB,CAACwG,IAAI,CAACJ,IAAI,CAACzG,aAAa,CAAC,CAAA;gBAE5D,MAAOyG,IAAI,CAACqD,UAAU,CAAE;oBACtB,0DAAA;oBACAF,UAAU,CAACC,WAAW,CAACpD,IAAI,CAACqD,UAAU,CAAC,CAAA;gBACzC,CAAA;YACF,CAAC,MAAM;gBACLF,UAAU,GAAGnD,IAAI,CAAA;YACnB,CAAA;YAEA,IAAI3F,YAAY,CAACkJ,UAAU,IAAIlJ,YAAY,CAACmJ,cAAc,EAAE;gBAC1D;;;;;;QAME,GACFL,UAAU,GAAGrJ,UAAU,CAACsG,IAAI,CAAChI,gBAAgB,EAAE+K,UAAU,EAAE,IAAI,CAAC,CAAA;YAClE,CAAA;YAEA,OAAOA,UAAU,CAAA;QACnB,CAAA;QAEA,IAAIM,cAAc,GAAGlI,cAAc,GAAGyE,IAAI,CAAC0D,SAAS,GAAG1D,IAAI,CAACD,SAAS,CAAA;QAErE,gCAAA,GACA,IACExE,cAAc,IACdrB,YAAY,CAAC,UAAU,CAAC,IACxB8F,IAAI,CAACzG,aAAa,IAClByG,IAAI,CAACzG,aAAa,CAACoK,OAAO,IAC1B3D,IAAI,CAACzG,aAAa,CAACoK,OAAO,CAAC1E,IAAI,IAC/BpN,UAAU,CAACoI,YAAwB,EAAE+F,IAAI,CAACzG,aAAa,CAACoK,OAAO,CAAC1E,IAAI,CAAC,EACrE;YACAwE,cAAc,GACZ,YAAY,GAAGzD,IAAI,CAACzG,aAAa,CAACoK,OAAO,CAAC1E,IAAI,GAAG,KAAK,GAAGwE,cAAc,CAAA;QAC3E,CAAA;QAEA,uCAAA,GACA,IAAIpI,kBAAkB,EAAE;YACtBpL,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5D2B,cAAc,GAAGpS,aAAa,CAACoS,cAAc,EAAE3B,IAAI,EAAE,GAAG,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;QACJ,CAAA;QAEA,OAAOtI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAAC+M,cAAc,CAAC,GAC7CA,cAAc,CAAA;KACnB,CAAA;IAED7L,SAAS,CAACgM,SAAS,GAAG,YAAkB;QAAA,IAARhG,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QACtCoL,YAAY,CAACC,GAAG,CAAC,CAAA;QACjBpC,UAAU,GAAG,IAAI,CAAA;KAClB,CAAA;IAED5D,SAAS,CAACiM,WAAW,GAAG,YAAA;QACtBvG,MAAM,GAAG,IAAI,CAAA;QACb9B,UAAU,GAAG,KAAK,CAAA;KACnB,CAAA;IAED5D,SAAS,CAACkM,gBAAgB,GAAG,SAAUC,GAAG,EAAEtB,IAAI,EAAE/O,KAAK,EAAA;QACrD,+CAAA,GACA,IAAI,CAAC4J,MAAM,EAAE;YACXK,YAAY,CAAC,CAAA,CAAE,CAAC,CAAA;QAClB,CAAA;QAEA,MAAMqE,KAAK,GAAGjP,iBAAiB,CAACgR,GAAG,CAAC,CAAA;QACpC,MAAM9B,MAAM,GAAGlP,iBAAiB,CAAC0P,IAAI,CAAC,CAAA;QACtC,OAAOV,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,CAAA;KAC/C,CAAA;IAEDkE,SAAS,CAACoM,OAAO,GAAG,SAAUC,UAAU,EAAEC,YAAY,EAAA;QACpD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;YACtC,OAAA;QACF,CAAA;QAEAxT,SAAS,CAACqJ,KAAK,CAACkK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;KAC3C,CAAA;IAEDtM,SAAS,CAACuM,UAAU,GAAG,SAAUF,UAAU,EAAEC,YAAY,EAAA;QACvD,IAAIA,YAAY,KAAKvM,SAAS,EAAE;YAC9B,MAAMvE,KAAK,GAAG9C,gBAAgB,CAACyJ,KAAK,CAACkK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;YAE/D,OAAO9Q,KAAK,KAAK,CAAC,CAAC,GACfuE,SAAS,GACT/G,WAAW,CAACmJ,KAAK,CAACkK,UAAU,CAAC,EAAE7Q,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjD,CAAA;QAEA,OAAO5C,QAAQ,CAACuJ,KAAK,CAACkK,UAAU,CAAC,CAAC,CAAA;KACnC,CAAA;IAEDrM,SAAS,CAACwM,WAAW,GAAG,SAAUH,UAAU,EAAA;QAC1ClK,KAAK,CAACkK,UAAU,CAAC,GAAG,EAAE,CAAA;KACvB,CAAA;IAEDrM,SAAS,CAACyM,cAAc,GAAG,YAAA;QACzBtK,KAAK,GAAG/C,eAAe,EAAE,CAAA;KAC1B,CAAA;IAED,OAAOY,SAAS,CAAA;AAClB,CAAA;AAEA,IAAA,SAAeF,eAAe,EAAE", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}]}