import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { GlobeAltIcon } from '@heroicons/react/24/outline';

// Define CSS for language dropdown hover effect
const languageDropdownCSS = `
.language-dropdown-container:hover .language-dropdown {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
`;

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const LanguageSwitcher: React.FC = () => {
  const { t, i18n } = useTranslation('common');
  const router = useRouter();

  // Add CSS to document head for language dropdown hover effect
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = languageDropdownCSS;

    // Add it to the document head
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    localStorage.setItem('language', lng);
    // Refresh the page to apply language changes
    router.refresh();
  };

  const currentLanguage = i18n.language === 'zh' ? t('chinese') : t('english');

  return (
    <div
      className="relative block text-left language-dropdown-container"
    >
      <div>
        <button
          id="language-menu-button"
          type="button"
          className="inline-flex items-center gap-x-1 rounded-md bg-transparent px-2 py-1.5 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
          aria-label={t('languageSwitcher')}
          aria-haspopup="true"
        >
          <GlobeAltIcon className="h-5 w-5" aria-hidden="true" />
          <span className="hidden sm:inline">{currentLanguage}</span>
          <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
        </button>
      </div>

      <div
        className={classNames(
          'language-dropdown opacity-0 scale-95 pointer-events-none',
          'absolute right-0 z-10 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-blue-600 ring-opacity-5 focus:outline-none overflow-hidden transition-all duration-100 ease-out'
        )}
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="language-menu-button"
        style={{ top: '100%' }}
      >
        <div className="py-1">
          <button
            onClick={() => changeLanguage('en')}
            className="hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors"
            role="menuitem"
          >
            English
          </button>
          <button
            onClick={() => changeLanguage('zh')}
            className="hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors"
            role="menuitem"
          >
            中文
          </button>
        </div>
      </div>
    </div>
  );
};

export default LanguageSwitcher;