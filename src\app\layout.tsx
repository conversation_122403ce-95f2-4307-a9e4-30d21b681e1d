import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "All to Markdown - Convert Any Document to AI-Friendly Markdown",
  description: "Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Make your content truly understandable by AI models like ChatGPT and Claude.",
  keywords: "markdown converter, ai friendly format, chatgpt friendly, pdf to markdown, word to markdown, excel to markdown, html to markdown, image to markdown, website to markdown, url to markdown, doc to markdown, docx to markdown, xlsx to markdown, document conversion, ai readable",
  openGraph: {
    title: "All to Markdown - Convert Any Document to AI-Friendly Markdown",
    description: "Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.",
    url: "https://alltomarkdown.com",
    siteName: "All to Markdown",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "All to Markdown - Convert Any Document to AI-Friendly Markdown",
    description: "Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.",
  },
};

import ClientLayout from '../components/layout/ClientLayout';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <ClientLayout>{children}</ClientLayout>;
}
