'use client';

import React, { useEffect } from 'react'; // Added useEffect import

// Define a CSS keyframes animation for the auth loading pulse
const authLoadingPulseAnimation = `
@keyframes authLoadingPulse {
  0% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
  50% {
    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */
  }
  100% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
}

.auth-loading-pulse {
  animation: authLoadingPulse 1.5s ease-in-out infinite;
}

.dark .auth-loading-pulse {
  animation-name: authLoadingPulseDark;
}

@keyframes authLoadingPulseDark {
  0% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
  50% {
    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */
  }
  100% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
}
`;
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import I18nProvider from '../i18n/I18nProvider';
import LanguageSwitcher from '../i18n/LanguageSwitcher';
import UserProfileMenu from '../auth/UserProfileMenu';
import { useAuth } from '../../hooks/useAuth';

interface ClientLayoutProps {
  children: React.ReactNode;
}

function ClientLayoutContent({ children }: { children: React.ReactNode }) {
  const { t } = useTranslation('common');
  const { user, isLoading } = useAuth();
  const pathname = usePathname();

  // Add the auth loading pulse animation styles to the document
  useEffect(() => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.innerHTML = authLoadingPulseAnimation;

    // Add it to the document head
    document.head.appendChild(styleElement);

    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // For debugging purposes
  useEffect(() => {
    console.log('ClientLayout: isLoading:', isLoading);
    console.log('ClientLayout: user object:', user);
  }, [isLoading, user]);

  const getLinkClassName = (href: string) => {
    const isActive = pathname === href;
    return `px-3 py-2 rounded-md text-sm font-medium transition-colors ${
      isActive ? 'bg-gray-200 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-blue-600'
    }`;
  };

  return (
    <html lang="en">
      <body
        className="antialiased"
      >
        <header className="main-header shadow-md fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-white/90">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16 items-center">
              <div className="flex-shrink-0 flex items-center">
                <Link href="/" className="text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors">
                  {t('siteName')}
                </Link>
              </div>
              <div className="flex items-center justify-between flex-1 pl-6 md:pl-10 lg:pl-16"> {/* Responsive left padding */}
                <nav className="hidden md:flex space-x-6 lg:space-x-8 items-baseline"> {/* Responsive spacing between nav items */}
                  <Link href="/" className={getLinkClassName('/')}>
                    {t('home')}
                  </Link>
                  <Link href="/convert" className={getLinkClassName('/convert')}>
                    {t('convert')}
                  </Link>
                  <Link href="/pricing" className={getLinkClassName('/pricing')}>
                    {t('pricing')}
                  </Link>
                </nav>

                {/* Right side items: Language switcher and user/login */}
                <div className="flex items-center space-x-3 md:space-x-4 lg:space-x-5">
                  {/* Language Switcher - now positioned to the left of user/login */}
                  <LanguageSwitcher />

                  {/* UserProfileMenu rendering logic - now handles all states: loading, logged in, logged out */}
                  {isLoading ? (
                    <div className="h-10 w-10 rounded-full auth-loading-pulse"></div>
                  ) : (
                    <UserProfileMenu user={user} /> // Pass user (can be null) to UserProfileMenu
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>
        <main className="pt-16"> {/* Add padding-top to account for fixed header */}
          {children}
        </main>
        <footer className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="border-t border-gray-200 py-8 text-center text-sm text-gray-500">
              <p>{t('footer', { year: new Date().getFullYear() })}</p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}

const ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {
  return (
    <I18nProvider>
      <ClientLayoutContent>{children}</ClientLayoutContent>
    </I18nProvider>
  );
};

export default ClientLayout;
