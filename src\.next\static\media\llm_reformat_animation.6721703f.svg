<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Document -->
  <g id="originalDoc" transform="translate(0, 0)"> <!-- Centered in the left half (0-100) -->
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="8" fill="#424242" text-anchor="middle">doc</text>
    <line x1="20" y1="35" x2="80" y2="35" stroke="#757575" stroke-width="2"/>
    <line x1="20" y1="45" x2="80" y2="45" stroke="#757575" stroke-width="2"/>
    <line x1="20" y1="55" x2="80" y2="55" stroke="#757575" stroke-width="2"/>
    <line x1="20" y1="65" x2="60" y2="65" stroke="#757575" stroke-width="2"/>
    <line x1="20" y1="75" x2="80" y2="75" stroke="#757575" stroke-width="2"/>
    <line x1="20" y1="85" x2="70" y2="85" stroke="#757575" stroke-width="2"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="1.5s" fill="freeze" />
  </g>

  <!-- Transformed Document -->
  <!-- Initially positioned in the right half (100-200), effectively starting off-screen right of the final 0-100 view, then moves to be centered in 0-100 -->
  <g id="transformedDoc" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="8" fill="#424242" text-anchor="middle">md</text>

    <!-- Title -->
    <rect x="20" y="35" width="60" height="6" fill="#757575"/>
    <text x="50" y="41" font-size="7" fill="#FFFFFF" text-anchor="middle" font-weight="bold">Title</text>

    <!-- Subtitle -->
    <rect x="20" y="50" width="45" height="4" fill="#757575"/>
    <text x="42.5" y="54" font-size="5" fill="#FFFFFF" text-anchor="middle">Subtitle</text>

    <!-- Bullet points -->
    <circle cx="25" cy="65" r="1.5" fill="#757575"/>
    <line x1="30" y1="65" x2="70" y2="65" stroke="#757575" stroke-width="2"/>

    <circle cx="25" cy="75" r="1.5" fill="#757575"/>
    <line x1="30" y1="75" x2="65" y2="75" stroke="#757575" stroke-width="2"/>

    <circle cx="25" cy="85" r="1.5" fill="#757575"/>
    <line x1="30" y1="85" x2="75" y2="85" stroke="#757575" stroke-width="2"/>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="1.5s" dur="1.5s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="1.5s" dur="1.5s" fill="freeze" />
  </g>
</svg>
