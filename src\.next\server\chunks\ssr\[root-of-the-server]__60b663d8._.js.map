{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/i18n.js"], "sourcesContent": ["import { createInstance } from 'i18next';\nimport { initReactI18next } from 'react-i18next';\n\n// English translations\nconst enTranslations = {\n  common: {\n    siteName: 'All to Markdown',\n    home: 'Home',\n    convert: 'Convert',\n    pricing: 'Pricing',\n    login: 'Login',\n    logout: 'Logout',\n    profile: 'Profile',\n    subscription: 'Subscription',\n    user: 'User',\n    footer: '© {{year}} All to Markdown. All rights reserved.',\n    languageSwitcher: 'Language',\n    english: 'English',\n    chinese: '中文',\n    userGroupPlaceholder: 'N/A',\n    paymentStatusPlaceholder: 'N/A',\n    openUserMenu: 'Open user menu',\n    userGroup: 'User Group',\n    paymentStatus: 'Payment Status'\n  },\n  home: {\n    title: 'Convert Documents to AI-Friendly Markdown',\n    description: 'Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.',\n    startConverting: 'Start Converting',\n    viewPricing: 'View Pricing',\n    aiTitle: 'Why Markdown is Perfect for AI',\n    aiDescription: 'Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.',\n    clearStructure: 'Clear Structure',\n    clearStructureDesc: 'Markdown\\'s simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.',\n    enhancedResponses: 'Enhanced AI Responses',\n    enhancedResponsesDesc: 'When feeding Markdown to AI like ChatGPT or Claude, you\\'ll get more accurate responses because the AI can better understand context and relationships in your content.',\n    noFormatting: 'No Formatting Noise',\n    noFormattingDesc: 'Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.',\n    convertAnyFormat: 'Convert any document format to clean Markdown',\n    convertDesc: 'Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.',\n    supportedFormats: 'Supported File Formats',\n    easyUpload: 'Easy File Upload',\n    easyUploadDesc: 'Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.',\n    advancedOptions: 'Advanced Options',\n    advancedOptionsDesc: 'Customize your Markdown output with options for different Markdown flavors, image handling, and more.',\n    batchProcessing: 'Batch Processing',\n    batchProcessingDesc: 'Convert multiple files at once and download them individually or as a zip archive.',\n    fastConversion: 'Fast Conversion',\n    fastConversionDesc: 'Our optimized conversion engine processes your documents quickly, saving you time and effort.'\n  },\n  convert: {\n    title: 'Convert to AI-Friendly Markdown',\n    description: 'Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.',\n    aiTip: 'AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.',\n    startConversion: 'Start Conversion',\n    options: 'Conversion Options',\n    markdownFlavor: 'Markdown Flavor',\n    markdownFlavorDesc: 'Select the Markdown specification to follow',\n    aiOptimized: 'AI-Optimized Format',\n    aiOptimizedDesc: 'Optimize output for AI models like ChatGPT and Claude',\n    advancedOptions: 'Advanced Options',\n    imageHandling: 'Image Handling',\n    imageHandlingDesc: 'How to handle images in the converted Markdown',\n    enableImageDescription: 'Generate descriptive text for images',\n    enableImageDescriptionDesc: 'Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.',\n    imageDescriptionAttachmentMode: 'Description Attachment Method:',\n    attachmentModeKeepImage: 'Keep Image',\n    attachmentModeReplaceImage: 'Replace Image',\n    tableHandling: 'Table Handling',\n    tableHandlingDesc: 'Table formatting style in the converted Markdown',\n    successMessage: 'Your file has been successfully converted to Markdown!',\n    successMessagePlural: '{{count}} files have been successfully converted to Markdown!',\n    aiSuccessTip: 'Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.',\n    download: 'Download .md',\n    downloadAll: 'Download All (.zip)'\n  },\n  pricing: {\n    title: 'Pricing',\n    subtitle: 'Pricing plans for all needs',\n    description: 'Choose the perfect plan for your document conversion needs. All plans include our core conversion features.',\n    free: 'Free',\n    pro: 'Pro',\n    enterprise: 'Enterprise',\n    mostPopular: 'Most popular',\n    monthly: '/month',\n    yearly: '/year',\n    getStarted: 'Get started',\n    subscribe: 'Subscribe'\n  },\n  auth: {\n    loginTitle: 'Login',\n    loginDescription: 'Login to your account',\n    emailLabel: 'Email',\n    passwordLabel: 'Password',\n    loginButton: 'Login',\n    forgotPassword: 'Forgot password?',\n    noAccount: \"Don't have an account?\",\n    signUpLink: 'Sign up',\n    registerTitle: 'Sign Up',\n    registerDescription: 'Create a new account',\n    confirmPasswordLabel: 'Confirm Password',\n    registerButton: 'Sign Up',\n    hasAccount: 'Already have an account?',\n    signInLink: 'Sign in',\n    orContinueWith: 'Or continue with',\n    github: 'GitHub',\n    google: 'Google',\n    magicLinkSent: 'Magic link sent!',\n    checkYourEmail: 'Check your email for the magic link to login.'\n  }\n};\n\n// Chinese translations\nconst zhTranslations = {\n  common: {\n    siteName: 'All to Markdown',\n    home: '首页',\n    convert: '转换',\n    pricing: '价格',\n    login: '登录',\n    logout: '退出登录',\n    profile: '个人资料',\n    subscription: '订阅状态',\n    user: '用户',\n    footer: '© {{year}} All to Markdown. 保留所有权利。',\n    languageSwitcher: '语言',\n    english: 'English',\n    chinese: '中文',\n    userGroupPlaceholder: '暂无',\n    paymentStatusPlaceholder: '暂无',\n    openUserMenu: '打开用户菜单',\n    userGroup: '用户组',\n    paymentStatus: '支付状态',\n    register: '注册'\n  },\n  home: {\n    title: '将文档转换为AI友好的Markdown格式',\n    description: '将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。',\n    startConverting: '开始转换',\n    viewPricing: '查看价格',\n    aiTitle: '为什么Markdown对AI来说是完美的',\n    aiDescription: 'Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。',\n    clearStructure: '清晰的结构',\n    clearStructureDesc: 'Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。',\n    enhancedResponses: '增强的AI响应',\n    enhancedResponsesDesc: '当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。',\n    noFormatting: '没有格式噪音',\n    noFormattingDesc: '与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。',\n    convertAnyFormat: '将任何文档格式转换为清晰的Markdown',\n    convertDesc: '我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。',\n    supportedFormats: '支持的文件格式',\n    easyUpload: '轻松上传文件',\n    easyUploadDesc: '拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。',\n    advancedOptions: '高级选项',\n    advancedOptionsDesc: '使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。',\n    batchProcessing: '批量处理',\n    batchProcessingDesc: '一次转换多个文件，并单独下载或作为zip存档下载。',\n    fastConversion: '快速转换',\n    fastConversionDesc: '我们优化的转换引擎快速处理您的文档，节省您的时间和精力。'\n  },\n  convert: {\n    title: '转换为AI友好的Markdown',\n    description: '上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。',\n    aiTip: 'AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。',\n    startConversion: '开始转换',\n    options: '转换选项',\n    markdownFlavor: 'Markdown风格',\n    markdownFlavorDesc: '选择要遵循的Markdown规范',\n    aiOptimized: 'AI优化格式',\n    aiOptimizedDesc: '为ChatGPT和Claude等AI模型优化输出',\n    advancedOptions: '高级选项',\n    imageHandling: '图像处理',\n    imageHandlingDesc: '如何处理转换后的Markdown中的图像',\n    enableImageDescription: '为图片生成描述性文字',\n    enableImageDescriptionDesc: '为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。',\n    imageDescriptionAttachmentMode: '描述文字附加方式:',\n    attachmentModeKeepImage: '保留图片',\n    attachmentModeReplaceImage: '替换图片',\n    tableHandling: '表格处理',\n    tableHandlingDesc: '转换后的Markdown中的表格格式样式',\n    successMessage: '您的文件已成功转换为Markdown！',\n    successMessagePlural: '{{count}}个文件已成功转换为Markdown！',\n    aiSuccessTip: '您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。',\n    download: '下载.md',\n    downloadAll: '下载全部(.zip)'\n  },\n  pricing: {\n    title: '价格',\n    subtitle: '满足所有需求的价格计划',\n    description: '选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。',\n    free: '免费版',\n    pro: '专业版',\n    enterprise: '企业版',\n    mostPopular: '最受欢迎',\n    monthly: '/月',\n    yearly: '/年',\n    getStarted: '开始使用',\n    subscribe: '订阅'\n  },\n  auth: {\n    loginTitle: '登录',\n    loginDescription: '登录您的账户',\n    emailLabel: '邮箱',\n    passwordLabel: '密码',\n    loginButton: '登录',\n    forgotPassword: '忘记密码？',\n    noAccount: '还没有账户？',\n    signUpLink: '注册',\n    registerTitle: '注册',\n    registerDescription: '创建一个新账户',\n    confirmPasswordLabel: '确认密码',\n    registerButton: '注册',\n    hasAccount: '已有账户？',\n    signInLink: '登录',\n    orContinueWith: '或继续使用',\n    github: 'GitHub',\n    google: 'Google',\n    magicLinkSent: '魔法链接已发送！',\n    checkYourEmail: '请检查您的邮箱以获取魔法链接进行登录。'\n  }\n};\n\nconst createI18nInstance = (lng = 'en', ns = ['common', 'auth']) => {\n  const i18nInstance = createInstance();\n  i18nInstance\n    .use(initReactI18next)\n    .init({\n      lng,\n      ns,\n      fallbackLng: 'en',\n      interpolation: {\n        escapeValue: false,\n      },\n      resources: {\n        en: {\n          ...enTranslations,\n        },\n        zh: {\n          ...zhTranslations,\n        },\n      },\n    });\n\n  return i18nInstance;\n};\n\nexport default createI18nInstance;\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,uBAAuB;AACvB,MAAM,iBAAiB;IACrB,QAAQ;QACN,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;QACT,cAAc;QACd,MAAM;QACN,QAAQ;QACR,kBAAkB;QAClB,SAAS;QACT,SAAS;QACT,sBAAsB;QACtB,0BAA0B;QAC1B,cAAc;QACd,WAAW;QACX,eAAe;IACjB;IACA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,oBAAoB;QACpB,mBAAmB;QACnB,uBAAuB;QACvB,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAClB,aAAa;QACb,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,SAAS;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,iBAAiB;QACjB,SAAS;QACT,gBAAgB;QAChB,oBAAoB;QACpB,aAAa;QACb,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,mBAAmB;QACnB,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,yBAAyB;QACzB,4BAA4B;QAC5B,eAAe;QACf,mBAAmB;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,cAAc;QACd,UAAU;QACV,aAAa;IACf;IACA,SAAS;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,KAAK;QACL,YAAY;QACZ,aAAa;QACb,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,WAAW;IACb;IACA,MAAM;QACJ,YAAY;QACZ,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,aAAa;QACb,gBAAgB;QAChB,WAAW;QACX,YAAY;QACZ,eAAe;QACf,qBAAqB;QACrB,sBAAsB;QACtB,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,gBAAgB;IAClB;AACF;AAEA,uBAAuB;AACvB,MAAM,iBAAiB;IACrB,QAAQ;QACN,UAAU;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;QACT,cAAc;QACd,MAAM;QACN,QAAQ;QACR,kBAAkB;QAClB,SAAS;QACT,SAAS;QACT,sBAAsB;QACtB,0BAA0B;QAC1B,cAAc;QACd,WAAW;QACX,eAAe;QACf,UAAU;IACZ;IACA,MAAM;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,oBAAoB;QACpB,mBAAmB;QACnB,uBAAuB;QACvB,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAClB,aAAa;QACb,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,SAAS;QACP,OAAO;QACP,aAAa;QACb,OAAO;QACP,iBAAiB;QACjB,SAAS;QACT,gBAAgB;QAChB,oBAAoB;QACpB,aAAa;QACb,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,mBAAmB;QACnB,wBAAwB;QACxB,4BAA4B;QAC5B,gCAAgC;QAChC,yBAAyB;QACzB,4BAA4B;QAC5B,eAAe;QACf,mBAAmB;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,cAAc;QACd,UAAU;QACV,aAAa;IACf;IACA,SAAS;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,MAAM;QACN,KAAK;QACL,YAAY;QACZ,aAAa;QACb,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,WAAW;IACb;IACA,MAAM;QACJ,YAAY;QACZ,kBAAkB;QAClB,YAAY;QACZ,eAAe;QACf,aAAa;QACb,gBAAgB;QAChB,WAAW;QACX,YAAY;QACZ,eAAe;QACf,qBAAqB;QACrB,sBAAsB;QACtB,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,gBAAgB;IAClB;AACF;AAEA,MAAM,qBAAqB,CAAC,MAAM,IAAI,EAAE,KAAK;IAAC;IAAU;CAAO;IAC7D,MAAM,eAAe,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAClC,aACG,GAAG,CAAC,kKAAA,CAAA,mBAAgB,EACpB,IAAI,CAAC;QACJ;QACA;QACA,aAAa;QACb,eAAe;YACb,aAAa;QACf;QACA,WAAW;YACT,IAAI;gBACF,GAAG,cAAc;YACnB;YACA,IAAI;gBACF,GAAG,cAAc;YACnB;QACF;IACF;IAEF,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/i18n/I18nProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { I18nextProvider } from 'react-i18next';\nimport createI18nInstance from '../../i18n';\n\ninterface I18nProviderProps {\n  children: React.ReactNode;\n}\n\nconst I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {\n  const [instance] = useState(createI18nInstance());\n\n  useEffect(() => {\n    // Check if there's a language preference in localStorage\n    const storedLanguage = localStorage.getItem('language');\n    if (storedLanguage) {\n      instance.changeLanguage(storedLanguage);\n    } else {\n      // Try to detect browser language\n      const browserLanguage = navigator.language;\n      if (browserLanguage.startsWith('zh')) {\n        instance.changeLanguage('zh');\n        localStorage.setItem('language', 'zh');\n      }\n    }\n  }, [instance]);\n\n  return (\n    <I18nextProvider i18n={instance}>\n      {children}\n    </I18nextProvider>\n  );\n};\n\nexport default I18nProvider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,oGAAA,CAAA,UAAkB,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,SAAS,cAAc,CAAC;QAC1B,OAAO;YACL,iCAAiC;YACjC,MAAM,kBAAkB,UAAU,QAAQ;YAC1C,IAAI,gBAAgB,UAAU,CAAC,OAAO;gBACpC,SAAS,cAAc,CAAC;gBACxB,aAAa,OAAO,CAAC,YAAY;YACnC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC,iKAAA,CAAA,kBAAe;QAAC,MAAM;kBACpB;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/i18n/LanguageSwitcher.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useRouter } from 'next/navigation';\nimport { ChevronDownIcon } from '@heroicons/react/20/solid';\nimport { GlobeAltIcon } from '@heroicons/react/24/outline';\n\n// Define CSS for language dropdown hover effect\nconst languageDropdownCSS = `\n.language-dropdown-container:hover .language-dropdown {\n  opacity: 1;\n  transform: scale(1);\n  pointer-events: auto;\n}\n`;\n\nfunction classNames(...classes: string[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst LanguageSwitcher: React.FC = () => {\n  const { t, i18n } = useTranslation('common');\n  const router = useRouter();\n\n  // Add CSS to document head for language dropdown hover effect\n  useEffect(() => {\n    // Create a style element\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = languageDropdownCSS;\n\n    // Add it to the document head\n    document.head.appendChild(styleElement);\n\n    // Clean up on unmount\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  const changeLanguage = (lng: string) => {\n    i18n.changeLanguage(lng);\n    localStorage.setItem('language', lng);\n    // Refresh the page to apply language changes\n    router.refresh();\n  };\n\n  const currentLanguage = i18n.language === 'zh' ? t('chinese') : t('english');\n\n  return (\n    <div\n      className=\"relative block text-left language-dropdown-container\"\n    >\n      <div>\n        <button\n          id=\"language-menu-button\"\n          type=\"button\"\n          className=\"inline-flex items-center gap-x-1 rounded-md bg-transparent px-2 py-1.5 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2\"\n          aria-label={t('languageSwitcher')}\n          aria-haspopup=\"true\"\n        >\n          <GlobeAltIcon className=\"h-5 w-5\" aria-hidden=\"true\" />\n          <span className=\"hidden sm:inline\">{currentLanguage}</span>\n          <ChevronDownIcon className=\"h-5 w-5\" aria-hidden=\"true\" />\n        </button>\n      </div>\n\n      <div\n        className={classNames(\n          'language-dropdown opacity-0 scale-95 pointer-events-none',\n          'absolute right-0 z-10 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-blue-600 ring-opacity-5 focus:outline-none overflow-hidden transition-all duration-100 ease-out'\n        )}\n        role=\"menu\"\n        aria-orientation=\"vertical\"\n        aria-labelledby=\"language-menu-button\"\n        style={{ top: '100%' }}\n      >\n        <div className=\"py-1\">\n          <button\n            onClick={() => changeLanguage('en')}\n            className=\"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors\"\n            role=\"menuitem\"\n          >\n            English\n          </button>\n          <button\n            onClick={() => changeLanguage('zh')}\n            className=\"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors\"\n            role=\"menuitem\"\n          >\n            中文\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LanguageSwitcher;"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,gDAAgD;AAChD,MAAM,sBAAsB,CAAC;;;;;;AAM7B,CAAC;AAED,SAAS,WAAW,GAAG,OAAiB;IACtC,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEA,MAAM,mBAA6B;IACjC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,SAAS,GAAG;QAEzB,8BAA8B;QAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,sBAAsB;QACtB,OAAO;YACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,KAAK,cAAc,CAAC;QACpB,aAAa,OAAO,CAAC,YAAY;QACjC,6CAA6C;QAC7C,OAAO,OAAO;IAChB;IAEA,MAAM,kBAAkB,KAAK,QAAQ,KAAK,OAAO,EAAE,aAAa,EAAE;IAElE,qBACE,8OAAC;QACC,WAAU;;0BAEV,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,MAAK;oBACL,WAAU;oBACV,cAAY,EAAE;oBACd,iBAAc;;sCAEd,8OAAC,uNAAA,CAAA,eAAY;4BAAC,WAAU;4BAAU,eAAY;;;;;;sCAC9C,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;sCACpC,8OAAC,2NAAA,CAAA,kBAAe;4BAAC,WAAU;4BAAU,eAAY;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,WAAW,WACT,4DACA;gBAEF,MAAK;gBACL,oBAAiB;gBACjB,mBAAgB;gBAChB,OAAO;oBAAE,KAAK;gBAAO;0BAErB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,MAAK;sCACN;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/lib/supabaseClient.ts"], "sourcesContent": ["import { createClient, SupabaseClient } from '@supabase/supabase-js';\r\n\r\nlet supabaseInstance: SupabaseClient | null = null;\r\n\r\nexport const getSupabaseClient = (): SupabaseClient => {\r\n  if (supabaseInstance) {\r\n    return supabaseInstance;\r\n  }\r\n\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl) {\r\n    console.error(\"Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration.\");\r\n    throw new Error(\"Missing env.NEXT_PUBLIC_SUPABASE_URL\");\r\n  }\r\n  if (!supabaseAnonKey) {\r\n    console.error(\"Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration.\");\r\n    throw new Error(\"Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY\");\r\n  }\r\n\r\n  // 创建 Supabase 客户端并启用 debug 模式\r\n  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {\r\n    auth: {\r\n      debug: true, // 启用认证模块的调试日志\r\n      autoRefreshToken: true,\r\n      persistSession: true,\r\n      detectSessionInUrl: true\r\n    },\r\n    global: {\r\n      fetch: async (url, options) => {\r\n        // 记录所有请求的详细信息\r\n        console.log(`[Supabase Debug] Fetch request to: ${url}`);\r\n        console.log(`[Supabase Debug] Fetch options:`, options);\r\n\r\n        try {\r\n          // 使用原生 fetch 发送请求\r\n          const response = await fetch(url, options);\r\n          console.log(`[Supabase Debug] Response status: ${response.status}`);\r\n          return response;\r\n        } catch (error) {\r\n          console.error(`[Supabase Debug] Fetch error:`, error);\r\n          throw error;\r\n        }\r\n      }\r\n    }\r\n  });\r\n\r\n  // 添加额外的调试日志\r\n  console.log('[Supabase Debug] Client initialized with URL:', supabaseUrl);\r\n\r\n  return supabaseInstance;\r\n};\r\n\r\n// Optional: For components that might still expect a direct export,\r\n// you could provide a version that tries to initialize immediately,\r\n// but this might re-introduce the problem in some contexts.\r\n// It's generally better to update consuming code to use the function.\r\n\r\n// export const supabase = getSupabaseClient(); // Avoid this if it causes issues during build\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,mBAA0C;AAEvC,MAAM,oBAAoB;IAC/B,IAAI,kBAAkB;QACpB,OAAO;IACT;IAEA,MAAM;IACN,MAAM;IAEN,uCAAkB;;IAGlB;IACA,uCAAsB;;IAGtB;IAEA,8BAA8B;IAC9B,mBAAmB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;QAC5D,MAAM;YACJ,OAAO;YACP,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;QACA,QAAQ;YACN,OAAO,OAAO,KAAK;gBACjB,cAAc;gBACd,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,KAAK;gBACvD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC,EAAE;gBAE/C,IAAI;oBACF,kBAAkB;oBAClB,MAAM,WAAW,MAAM,MAAM,KAAK;oBAClC,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,SAAS,MAAM,EAAE;oBAClE,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE;oBAC/C,MAAM;gBACR;YACF;QACF;IACF;IAEA,YAAY;IACZ,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,OAAO;AACT,GAEA,oEAAoE;CACpE,oEAAoE;CACpE,4DAA4D;CAC5D,sEAAsE;CAEtE,8FAA8F", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getSupabaseClient } from '../lib/supabaseClient';\nimport { User, Session } from '@supabase/supabase-js';\n\n// Define a key for storing the local JWT in localStorage\nconst LOCAL_JWT_KEY = 'local_jwt_token';\n\n// Function to call the backend callback API\nasync function syncWithBackend(supabaseAccessToken: string): Promise<string | null> {\n  try {\n    console.log('[Auth Debug] syncWithBackend started with token:', supabaseAccessToken.substring(0, 10) + '...');\n\n    // Ensure this path is correct based on your API setup.\n    // If your Next.js app serves the API, it might be relative.\n    // If the API is on a different domain, use the full URL.\n    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL\n      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/auth/supabase/callback`\n      : '/api/v1/users/auth/supabase/callback';\n\n    console.log('[Auth Debug] Calling backend API at:', apiUrl);\n\n    const startTime = Date.now();\n    const response = await fetch(apiUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ access_token: supabaseAccessToken }),\n    });\n\n    const duration = Date.now() - startTime;\n    console.log(`[Auth Debug] Backend API call completed in ${duration}ms with status:`, response.status);\n\n    if (response.ok) {\n      const data = await response.json();\n      if (data.access_token) {\n        if (typeof window !== 'undefined') {\n          localStorage.setItem(LOCAL_JWT_KEY, data.access_token);\n        }\n        console.log('Local JWT stored successfully.');\n        return data.access_token;\n      } else {\n        console.error('Backend callback response missing access_token:', data);\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem(LOCAL_JWT_KEY);\n        }\n        return null;\n      }\n    } else {\n      // Attempt to parse error response, but fallback if it's not JSON\n      let errorData;\n      try {\n        errorData = await response.json();\n      } catch { // Removed unused _e parameter\n        errorData = { message: response.statusText };\n      }\n      console.error('Error syncing with backend:', response.status, errorData);\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem(LOCAL_JWT_KEY);\n      }\n      return null;\n    }\n  } catch (error) {\n    console.error('Network error or other issue syncing with backend:', error);\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(LOCAL_JWT_KEY);\n    }\n    return null;\n  }\n}\n\ninterface AuthState {\n  user: User | null;\n  session: Session | null;\n  isLoading: boolean;\n}\n\nexport function useAuth(): AuthState {\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    session: null,\n    isLoading: true,\n  });\n\n  useEffect(() => {\n    console.log('[Auth Debug] useAuth hook initialized');\n    const supabase = getSupabaseClient();\n\n    // 记录 Supabase 客户端状态\n    console.log('[Auth Debug] Supabase client obtained:', !!supabase);\n\n    // 初始化认证状态 - 避免在初始化过程中调用 getUser()\n    const initializeAuth = async () => {\n      console.log('useAuth: initializeAuth started');\n      try {\n        // 1. 获取会话\n        const { data: { session: initialSession }, error: sessionError } = await supabase.auth.getSession();\n        if (sessionError) {\n          console.error('useAuth: initializeAuth - error fetching session:', sessionError);\n          setAuthState({ user: null, session: null, isLoading: false });\n          console.log('useAuth: initializeAuth failed due to session error.');\n          return;\n        }\n        console.log('useAuth: initializeAuth - initialSession fetched:', initialSession ? 'exists' : 'null');\n\n        // 2. 如果会话存在，直接使用会话中的用户信息\n        if (initialSession && initialSession.access_token) {\n          console.log('useAuth: initializeAuth - session exists');\n\n          // 直接从 session 中获取用户信息\n          const user = initialSession.user;\n\n          console.log('useAuth: initializeAuth - user from session:', user ? user.id : 'null');\n\n          if (user) {\n            console.log('useAuth: initializeAuth - user exists, syncing with backend...');\n            await syncWithBackend(initialSession.access_token);\n            console.log('useAuth: initializeAuth - backend sync complete.');\n            setAuthState({ user, session: initialSession, isLoading: false });\n            console.log('useAuth: initializeAuth completed with session and user.');\n          } else {\n            console.warn('useAuth: initializeAuth - session exists but user is null in session.');\n            if (typeof window !== 'undefined') {\n              localStorage.removeItem(LOCAL_JWT_KEY);\n            }\n            setAuthState({ user: null, session: null, isLoading: false });\n            console.log('useAuth: initializeAuth completed with session but no user.');\n          }\n        } else {\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem(LOCAL_JWT_KEY);\n          }\n          setAuthState({ user: null, session: null, isLoading: false });\n          console.log('useAuth: initializeAuth completed without session.');\n        }\n      } catch (error) {\n        console.error('Error initializing auth:', error);\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem(LOCAL_JWT_KEY);\n        }\n        setAuthState({ user: null, session: null, isLoading: false });\n        console.log('useAuth: initializeAuth failed in catch block.');\n      }\n    };\n\n    // 启动初始化\n    initializeAuth();\n\n    // 设置认证状态变化监听 - 避免在回调中调用 getUser()\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, currentSession) => {\n        console.log('useAuth: onAuthStateChange event:', event, 'Session:', currentSession ? 'exists' : 'null');\n\n        try {\n          if (currentSession && currentSession.access_token) {\n            // 直接从 session 中获取用户信息，避免再次调用 API\n            const user = currentSession.user;\n\n            console.log('useAuth: onAuthStateChange - user from session:', user ? user.id : 'null');\n\n            if (user) {\n              console.log('useAuth: onAuthStateChange - User object exists. Attempting to sync with backend...');\n              try {\n                await syncWithBackend(currentSession.access_token);\n                console.log('useAuth: onAuthStateChange - backend sync complete.');\n              } catch (syncError) {\n                console.error('useAuth: onAuthStateChange - EXCEPTION syncing with backend:', syncError);\n              }\n              setAuthState({ user, session: currentSession, isLoading: false });\n              console.log('useAuth: onAuthStateChange updated state with session.');\n            } else {\n              console.warn('useAuth: onAuthStateChange - User is null in session.');\n              if (typeof window !== 'undefined') {\n                localStorage.removeItem(LOCAL_JWT_KEY);\n              }\n              setAuthState({ user: null, session: null, isLoading: false });\n              console.log('useAuth: onAuthStateChange updated state to no user.');\n            }\n          } else {\n            // 处理登出或会话为空的情况\n            if (typeof window !== 'undefined') {\n              localStorage.removeItem(LOCAL_JWT_KEY);\n            }\n            setAuthState({ user: null, session: null, isLoading: false });\n            console.log('useAuth: onAuthStateChange updated state without session (SIGNED_OUT or null session).');\n          }\n        } catch (error) {\n          console.error('useAuth: Error in onAuthStateChange callback:', error);\n          if (typeof window !== 'undefined') {\n            localStorage.removeItem(LOCAL_JWT_KEY);\n          }\n          setAuthState({ user: null, session: null, isLoading: false });\n          console.log('useAuth: onAuthStateChange failed in catch block, set isLoading to false.');\n        }\n      }\n    );\n\n    // 清理订阅\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  return authState;\n}\n\n// Export a function to get the local JWT for use in API calls\nexport function getLocalAuthToken(): string | null {\n  if (typeof window !== 'undefined') {\n    return localStorage.getItem(LOCAL_JWT_KEY);\n  }\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAMA,yDAAyD;AACzD,MAAM,gBAAgB;AAEtB,4CAA4C;AAC5C,eAAe,gBAAgB,mBAA2B;IACxD,IAAI;QACF,QAAQ,GAAG,CAAC,oDAAoD,oBAAoB,SAAS,CAAC,GAAG,MAAM;QAEvG,uDAAuD;QACvD,4DAA4D;QAC5D,yDAAyD;QACzD,MAAM,SAAS,uCACX,2DAAwC,oCAAoC,CAAC;QAGjF,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,MAAM,QAAQ;YACnC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,cAAc;YAAoB;QAC3D;QAEA,MAAM,WAAW,KAAK,GAAG,KAAK;QAC9B,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,SAAS,eAAe,CAAC,EAAE,SAAS,MAAM;QAEpG,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,YAAY,EAAE;gBACrB,uCAAmC;;gBAEnC;gBACA,QAAQ,GAAG,CAAC;gBACZ,OAAO,KAAK,YAAY;YAC1B,OAAO;gBACL,QAAQ,KAAK,CAAC,mDAAmD;gBACjE,uCAAmC;;gBAEnC;gBACA,OAAO;YACT;QACF,OAAO;YACL,iEAAiE;YACjE,IAAI;YACJ,IAAI;gBACF,YAAY,MAAM,SAAS,IAAI;YACjC,EAAE,OAAM;gBACN,YAAY;oBAAE,SAAS,SAAS,UAAU;gBAAC;YAC7C;YACA,QAAQ,KAAK,CAAC,+BAA+B,SAAS,MAAM,EAAE;YAC9D,uCAAmC;;YAEnC;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QACpE,uCAAmC;;QAEnC;QACA,OAAO;IACT;AACF;AAQO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,SAAS;QACT,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAEjC,oBAAoB;QACpB,QAAQ,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,kCAAkC;QAClC,MAAM,iBAAiB;YACrB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,UAAU;gBACV,MAAM,EAAE,MAAM,EAAE,SAAS,cAAc,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBACjG,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,qDAAqD;oBACnE,aAAa;wBAAE,MAAM;wBAAM,SAAS;wBAAM,WAAW;oBAAM;oBAC3D,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBACA,QAAQ,GAAG,CAAC,qDAAqD,iBAAiB,WAAW;gBAE7F,yBAAyB;gBACzB,IAAI,kBAAkB,eAAe,YAAY,EAAE;oBACjD,QAAQ,GAAG,CAAC;oBAEZ,sBAAsB;oBACtB,MAAM,OAAO,eAAe,IAAI;oBAEhC,QAAQ,GAAG,CAAC,gDAAgD,OAAO,KAAK,EAAE,GAAG;oBAE7E,IAAI,MAAM;wBACR,QAAQ,GAAG,CAAC;wBACZ,MAAM,gBAAgB,eAAe,YAAY;wBACjD,QAAQ,GAAG,CAAC;wBACZ,aAAa;4BAAE;4BAAM,SAAS;4BAAgB,WAAW;wBAAM;wBAC/D,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,IAAI,CAAC;wBACb,uCAAmC;;wBAEnC;wBACA,aAAa;4BAAE,MAAM;4BAAM,SAAS;4BAAM,WAAW;wBAAM;wBAC3D,QAAQ,GAAG,CAAC;oBACd;gBACF,OAAO;oBACL,uCAAmC;;oBAEnC;oBACA,aAAa;wBAAE,MAAM;wBAAM,SAAS;wBAAM,WAAW;oBAAM;oBAC3D,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,uCAAmC;;gBAEnC;gBACA,aAAa;oBAAE,MAAM;oBAAM,SAAS;oBAAM,WAAW;gBAAM;gBAC3D,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,QAAQ;QACR;QAEA,kCAAkC;QAClC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,qCAAqC,OAAO,YAAY,iBAAiB,WAAW;YAEhG,IAAI;gBACF,IAAI,kBAAkB,eAAe,YAAY,EAAE;oBACjD,iCAAiC;oBACjC,MAAM,OAAO,eAAe,IAAI;oBAEhC,QAAQ,GAAG,CAAC,mDAAmD,OAAO,KAAK,EAAE,GAAG;oBAEhF,IAAI,MAAM;wBACR,QAAQ,GAAG,CAAC;wBACZ,IAAI;4BACF,MAAM,gBAAgB,eAAe,YAAY;4BACjD,QAAQ,GAAG,CAAC;wBACd,EAAE,OAAO,WAAW;4BAClB,QAAQ,KAAK,CAAC,gEAAgE;wBAChF;wBACA,aAAa;4BAAE;4BAAM,SAAS;4BAAgB,WAAW;wBAAM;wBAC/D,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,IAAI,CAAC;wBACb,uCAAmC;;wBAEnC;wBACA,aAAa;4BAAE,MAAM;4BAAM,SAAS;4BAAM,WAAW;wBAAM;wBAC3D,QAAQ,GAAG,CAAC;oBACd;gBACF,OAAO;oBACL,eAAe;oBACf,uCAAmC;;oBAEnC;oBACA,aAAa;wBAAE,MAAM;wBAAM,SAAS;wBAAM,WAAW;oBAAM;oBAC3D,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iDAAiD;gBAC/D,uCAAmC;;gBAEnC;gBACA,aAAa;oBAAE,MAAM;oBAAM,SAAS;oBAAM,WAAW;gBAAM;gBAC3D,QAAQ,GAAG,CAAC;YACd;QACF;QAGF,OAAO;QACP,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,uCAAmC;;IAEnC;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/auth/UserProfileMenu.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Cog6ToothIcon } from '@heroicons/react/20/solid'; // Removed UserIcon\nimport { getSupabaseClient } from '../../lib/supabaseClient';\nimport { useRouter } from 'next/navigation';\nimport Image from 'next/image';\nimport Link from 'next/link'; // Import Link\nimport { getLocalAuthToken } from '../../hooks/useAuth'; // Import the function to get local JWT\n\n// Define a CSS keyframes animation for the avatar loading pulse\nconst avatarPulseAnimation = `\n@keyframes avatarPulse {\n  0% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n  50% {\n    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */\n  }\n  100% {\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\n  }\n}\n\n.avatar-pulse {\n  animation: avatarPulse 1.5s ease-in-out infinite;\n}\n\n.dark .avatar-pulse {\n  animation-name: avatarPulseDark;\n}\n\n@keyframes avatarPulseDark {\n  0% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n  50% {\n    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */\n  }\n  100% {\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\n  }\n}\n`;\n\ninterface SupabaseUser { // Renamed to avoid conflict with localUser\n  email?: string | null;\n  user_metadata?: {\n    name?: string;\n    full_name?: string; // From some OAuth providers\n    avatar_url?: string;\n    picture?: string; // Common field from OAuth providers for avatar\n  };\n}\n\n// Interface for the user data fetched from your backend /api/v1/users/me\ninterface LocalUser {\n  id: string;\n  email: string;\n  username: string;\n  full_name: string | null;\n  is_active: boolean;\n  is_superuser: boolean;\n  tier: string; // e.g., \"free\", \"basic_paid\"\n  monthly_conversion_limit: number;\n  max_file_size_bytes: number;\n  conversions_this_month: number;\n  last_conversion_reset: string | null;\n  avatar_url?: string; // Added avatar_url to LocalUser\n}\n\ninterface UserProfileMenuProps {\n  user: SupabaseUser | null; // Prop can now be SupabaseUser or null\n}\n\nconst UserProfileMenu: React.FC<UserProfileMenuProps> = ({ user: supabaseUser }) => {\n  const { t } = useTranslation('common');\n  const router = useRouter();\n  const supabase = getSupabaseClient();\n\n  const [localUser, setLocalUser] = useState<LocalUser | null>(null);\n  const [isLoadingLocalUser, setIsLoadingLocalUser] = useState(false);\n  const [localUserError, setLocalUserError] = useState<string | null>(null);\n  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark' | 'system'>('system');\n\n  // Add the avatar pulse animation styles to the document\n  useEffect(() => {\n    // Create a style element\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = avatarPulseAnimation;\n\n    // Add it to the document head\n    document.head.appendChild(styleElement);\n\n    // Clean up on unmount\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  // Initialize theme based on localStorage or system preference\n  useEffect(() => {\n    // Check if theme is stored in localStorage\n    const storedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system' | null;\n\n    // Update current theme state\n    if (storedTheme) {\n      setCurrentTheme(storedTheme as 'light' | 'dark' | 'system');\n    }\n\n    const applyTheme = () => {\n      if (storedTheme === 'dark') {\n        document.documentElement.classList.add('dark');\n      } else if (storedTheme === 'light') {\n        document.documentElement.classList.remove('dark');\n      } else if (storedTheme === 'system' || !storedTheme) {\n        // Use system preference if set to 'system' or not set\n        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n          document.documentElement.classList.add('dark');\n        } else {\n          document.documentElement.classList.remove('dark');\n        }\n\n        // If not set, default to 'system'\n        if (!storedTheme) {\n          localStorage.setItem('theme', 'system');\n          setCurrentTheme('system');\n        }\n      }\n    };\n\n    // Apply theme initially\n    applyTheme();\n\n    // Listen for system theme changes if theme is set to 'system'\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleSystemThemeChange = (e: MediaQueryListEvent) => {\n      if (localStorage.getItem('theme') === 'system') {\n        if (e.matches) {\n          document.documentElement.classList.add('dark');\n        } else {\n          document.documentElement.classList.remove('dark');\n        }\n      }\n    };\n\n    // Add listener for system theme changes\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n\n    // Clean up\n    return () => {\n      mediaQuery.removeEventListener('change', handleSystemThemeChange);\n    };\n  }, []);\n\n  // Fetch user data from backend\n  useEffect(() => {\n    const fetchLocalUserData = async () => {\n      const token = getLocalAuthToken();\n      if (!token) {\n        // No local token, likely means user is not fully authenticated with backend yet or session expired\n        // console.log('No local JWT found, skipping fetch of /users/me');\n        setIsLoadingLocalUser(false); // Make sure to set loading to false if we're not fetching\n        return;\n      }\n\n      setIsLoadingLocalUser(true); // Start loading\n      setLocalUserError(null);\n\n      try {\n        // Simulate a minimum loading time of 1.5 seconds for better UX and to ensure animation is visible\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 1500));\n\n        const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL\n          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/me`\n          : '/api/v1/users/me';\n\n        const fetchPromise = fetch(apiUrl, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n\n        // Wait for both the minimum loading time and the fetch to complete\n        const [response] = await Promise.all([fetchPromise, minLoadingTime]);\n\n        if (response.ok) {\n          const data: LocalUser = await response.json();\n          setLocalUser(data);\n        } else {\n          const errorData = await response.json();\n          console.error('Failed to fetch local user data:', response.status, errorData);\n          setLocalUserError(errorData.detail || `Error ${response.status}`);\n          // If unauthorized, perhaps clear local token?\n          if (response.status === 401 && typeof window !== 'undefined') {\n            localStorage.removeItem('local_jwt_token'); // Assuming 'local_jwt_token' is the key\n            // Optionally, trigger a re-evaluation of auth state if you have a global context\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching local user data:', error);\n        setLocalUserError(t('errorFetchingUserData', 'Failed to load user details.'));\n      } finally {\n        setIsLoadingLocalUser(false); // End loading\n      }\n    };\n\n    if (supabaseUser?.email) { // Fetch only if Supabase user exists\n      fetchLocalUserData();\n    } else {\n      setIsLoadingLocalUser(false); // Make sure loading is false if we don't have a Supabase user\n    }\n  }, [supabaseUser, t]);\n\n  const handleSignOut = async () => {\n    try {\n      await supabase.auth.signOut();\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('local_jwt_token'); // Ensure local JWT is cleared on sign out\n      }\n      router.push('/');\n      router.refresh(); // Or use a state management solution to update UI\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  // Determine display values based on available data\n  // console.log('UserProfileMenu: supabaseUser prop:', supabaseUser); // For debugging\n  // console.log('UserProfileMenu: localUser state:', localUser); // For debugging\n\n  let displayName = t('user');\n  let displayEmail: string | null | undefined = null;\n  let avatarUrl: string | null | undefined = null;\n\n  if (supabaseUser) {\n    displayName = supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name || supabaseUser.email || t('user');\n    displayEmail = supabaseUser.email;\n    avatarUrl = supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture;\n  }\n\n  if (localUserError) {\n    displayName = t('error', 'Error');\n    // Potentially clear email/avatar if error state means we don't trust any user data\n    displayEmail = null;\n    avatarUrl = null;\n  } else if (localUser) {\n    // Prioritize localUser data if available\n    displayName = localUser.full_name || localUser.username || (supabaseUser ? (supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name) : '') || localUser.email;\n    displayEmail = localUser.email;\n    // If localUser has avatar_url, use it. Otherwise, fallback to supabaseUser's avatar if localUser doesn't override it.\n    avatarUrl = localUser.avatar_url || (supabaseUser ? (supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture) : null);\n  }\n  // console.log('UserProfileMenu: final displayName:', displayName, 'final avatarUrl:', avatarUrl); // For debugging\n\n  return (\n    <div className=\"relative inline-block text-left group\">\n      {/* Avatar button with loading animation - completely replaced when loading */}\n      {isLoadingLocalUser ? (\n        <div className=\"flex items-center justify-center rounded-full focus:outline-none h-10 w-10 cursor-pointer\">\n          <span className=\"sr-only\">{t('loadingUserMenu', 'Loading user menu')}</span>\n          <div className=\"h-10 w-10 rounded-full avatar-pulse\"></div>\n        </div>\n      ) : !supabaseUser && !localUser ? (\n        <div className=\"flex items-center space-x-2\">\n          <Link\n            href=\"/login\"\n            className=\"px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-blue-400\"\n          >\n            {t('login', 'Login')}\n          </Link>\n          <button\n            onClick={() => router.push('/register')}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            {t('register', 'Register')}\n          </button>\n        </div>\n      ) : (\n        <div className=\"flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none transition-colors h-10 w-10 cursor-pointer dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600\">\n          <span className=\"sr-only\">{t('openUserMenu', 'Open user menu')}</span>\n          {avatarUrl ? (\n            <div className=\"relative h-10 w-10 rounded-full overflow-hidden\">\n              <Image\n                src={avatarUrl}\n                alt={displayName}\n                fill\n                sizes=\"40px\"\n                style={{ objectFit: 'cover' }}\n                priority\n              />\n            </div>\n          ) : (\n            <div className=\"h-10 w-10 rounded-full flex items-center justify-center bg-gray-200 dark:bg-gray-700\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className=\"text-gray-600 dark:text-gray-300\">\n                <path d=\"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z\"\n                  stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                <path d=\"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20\"\n                  stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n              </svg>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Dropdown menu - shown on hover with modern styling, only if user is logged in */}\n      {!isLoadingLocalUser && (supabaseUser || localUser) && (\n        <div className=\"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-150\"\n             style={{ marginTop: '0.5rem' }}>\n        {/* Invisible bridge to extend hover area */}\n        <div className=\"absolute -top-2 right-0 w-16 h-2 bg-transparent\"></div>\n\n        {/* User info section */}\n        <div className=\"px-4 py-3 border-b border-gray-100\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 mr-3\">\n              {isLoadingLocalUser ? (\n                <div className=\"h-8 w-8 rounded-full avatar-pulse\"></div>\n              ) : avatarUrl ? (\n                <div className=\"relative h-8 w-8 rounded-full overflow-hidden\">\n                  <Image\n                    src={avatarUrl}\n                    alt={displayName}\n                    fill\n                    sizes=\"32px\"\n                    style={{ objectFit: 'cover' }}\n                  />\n                </div>\n              ) : (\n                <div className=\"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className=\"text-gray-600 dark:text-gray-300\">\n                    <path d=\"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z\"\n                      stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                    <path d=\"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20\"\n                      stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                  </svg>\n                </div>\n              )}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\" title={displayName}>{displayName}</p>\n              {displayEmail && <p className=\"text-xs text-gray-500 truncate\" title={displayEmail}>{displayEmail}</p>}\n            </div>\n          </div>\n        </div>\n\n        {/* Menu items */}\n        <div className=\"py-1\">\n          <a\n            href=\"/account\"\n            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          >\n            <svg className=\"mr-3 h-5 w-5 text-gray-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n            {t('accountPreferences', 'Account Preferences')}\n          </a>\n\n          <a\n            href=\"/settings\"\n            className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          >\n            <Cog6ToothIcon\n              className=\"mr-3 h-5 w-5 text-gray-400\"\n              aria-hidden=\"true\"\n            />\n            {t('settings', 'Settings')}\n          </a>\n        </div>\n\n        {/* Logout button */}\n        <div className=\"py-1 border-t border-gray-100\">\n          <button\n            onClick={handleSignOut}\n            className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          >\n            <svg className=\"mr-3 h-5 w-5 text-gray-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n              <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"></path>\n              <polyline points=\"16 17 21 12 16 7\"></polyline>\n              <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"></line>\n            </svg>\n            {t('logout', 'Sign out')}\n          </button>\n        </div>\n\n        {/* Theme selector - moved to bottom */}\n        <div className=\"py-1 border-t border-gray-100\">\n          <div className=\"px-4 py-2\">\n            <p className=\"text-xs font-medium text-gray-500 mb-2\">{t('theme', 'Theme')}</p>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => {\n                  document.documentElement.classList.remove('dark');\n                  localStorage.setItem('theme', 'light');\n                  setCurrentTheme('light');\n                }}\n                className={`p-2 rounded-md bg-white border ${currentTheme === 'light' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'} hover:bg-gray-50`}\n                aria-label={t('lightTheme', 'Light theme')}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-4 w-4 ${currentTheme === 'light' ? 'text-blue-600' : 'text-gray-700'}`} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <circle cx=\"12\" cy=\"12\" r=\"5\"></circle>\n                  <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"3\"></line>\n                  <line x1=\"12\" y1=\"21\" x2=\"12\" y2=\"23\"></line>\n                  <line x1=\"4.22\" y1=\"4.22\" x2=\"5.64\" y2=\"5.64\"></line>\n                  <line x1=\"18.36\" y1=\"18.36\" x2=\"19.78\" y2=\"19.78\"></line>\n                  <line x1=\"1\" y1=\"12\" x2=\"3\" y2=\"12\"></line>\n                  <line x1=\"21\" y1=\"12\" x2=\"23\" y2=\"12\"></line>\n                  <line x1=\"4.22\" y1=\"19.78\" x2=\"5.64\" y2=\"18.36\"></line>\n                  <line x1=\"18.36\" y1=\"5.64\" x2=\"19.78\" y2=\"4.22\"></line>\n                </svg>\n              </button>\n              <button\n                onClick={() => {\n                  document.documentElement.classList.add('dark');\n                  localStorage.setItem('theme', 'dark');\n                  setCurrentTheme('dark');\n                }}\n                className={`p-2 rounded-md bg-gray-900 border ${currentTheme === 'dark' ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-50' : 'border-gray-700'} hover:bg-gray-800`}\n                aria-label={t('darkTheme', 'Dark theme')}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-4 w-4 ${currentTheme === 'dark' ? 'text-blue-400' : 'text-gray-200'}`} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"></path>\n                </svg>\n              </button>\n              <button\n                onClick={() => {\n                  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    document.documentElement.classList.add('dark');\n                  } else {\n                    document.documentElement.classList.remove('dark');\n                  }\n                  localStorage.setItem('theme', 'system');\n                  setCurrentTheme('system');\n                }}\n                className={`p-2 rounded-md bg-gray-100 border ${currentTheme === 'system' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'} hover:bg-gray-200`}\n                aria-label={t('systemTheme', 'System theme')}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-4 w-4 ${currentTheme === 'system' ? 'text-blue-600' : 'text-gray-700'}`} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"></rect>\n                  <line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"></line>\n                  <line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"></line>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n      )}\n    </div>\n  );\n};\n\nexport default UserProfileMenu;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA,yXAA2D,mBAAmB;AAC9E;AACA;AACA;AACA,8QAA8B,cAAc;AAC5C,qMAAyD,uCAAuC;AAThG;;;;;;;;;;AAWA,gEAAgE;AAChE,MAAM,uBAAuB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC9B,CAAC;AAgCD,MAAM,kBAAkD,CAAC,EAAE,MAAM,YAAY,EAAE;IAC7E,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;IAEjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAE9E,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,SAAS,GAAG;QAEzB,8BAA8B;QAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,sBAAsB;QACtB,OAAO;YACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,MAAM,cAAc,aAAa,OAAO,CAAC;QAEzC,6BAA6B;QAC7B,IAAI,aAAa;YACf,gBAAgB;QAClB;QAEA,MAAM,aAAa;YACjB,IAAI,gBAAgB,QAAQ;gBAC1B,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO,IAAI,gBAAgB,SAAS;gBAClC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C,OAAO,IAAI,gBAAgB,YAAY,CAAC,aAAa;gBACnD,sDAAsD;gBACtD,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,EAAE;oBAClF,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5C;gBAEA,kCAAkC;gBAClC,IAAI,CAAC,aAAa;oBAChB,aAAa,OAAO,CAAC,SAAS;oBAC9B,gBAAgB;gBAClB;YACF;QACF;QAEA,wBAAwB;QACxB;QAEA,8DAA8D;QAC9D,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,0BAA0B,CAAC;YAC/B,IAAI,aAAa,OAAO,CAAC,aAAa,UAAU;gBAC9C,IAAI,EAAE,OAAO,EAAE;oBACb,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5C;YACF;QACF;QAEA,wCAAwC;QACxC,WAAW,gBAAgB,CAAC,UAAU;QAEtC,WAAW;QACX,OAAO;YACL,WAAW,mBAAmB,CAAC,UAAU;QAC3C;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,MAAM,QAAQ,CAAA,GAAA,gHAAA,CAAA,oBAAiB,AAAD;YAC9B,IAAI,CAAC,OAAO;gBACV,mGAAmG;gBACnG,kEAAkE;gBAClE,sBAAsB,QAAQ,0DAA0D;gBACxF;YACF;YAEA,sBAAsB,OAAO,gBAAgB;YAC7C,kBAAkB;YAElB,IAAI;gBACF,kGAAkG;gBAClG,MAAM,iBAAiB,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAElE,MAAM,SAAS,uCACX,2DAAwC,gBAAgB,CAAC;gBAG7D,MAAM,eAAe,MAAM,QAAQ;oBACjC,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;gBAEA,mEAAmE;gBACnE,MAAM,CAAC,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAe;gBAEnE,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAkB,MAAM,SAAS,IAAI;oBAC3C,aAAa;gBACf,OAAO;oBACL,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,oCAAoC,SAAS,MAAM,EAAE;oBACnE,kBAAkB,UAAU,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,EAAE;oBAChE,8CAA8C;oBAC9C,uCAA8D;;oBAE5D,iFAAiF;oBACnF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,kBAAkB,EAAE,yBAAyB;YAC/C,SAAU;gBACR,sBAAsB,QAAQ,cAAc;YAC9C;QACF;QAEA,IAAI,cAAc,OAAO;YACvB;QACF,OAAO;YACL,sBAAsB,QAAQ,8DAA8D;QAC9F;IACF,GAAG;QAAC;QAAc;KAAE;IAEpB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,uCAAmC;;YAEnC;YACA,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO,IAAI,kDAAkD;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,mDAAmD;IACnD,qFAAqF;IACrF,gFAAgF;IAEhF,IAAI,cAAc,EAAE;IACpB,IAAI,eAA0C;IAC9C,IAAI,YAAuC;IAE3C,IAAI,cAAc;QAChB,cAAc,aAAa,aAAa,EAAE,QAAQ,aAAa,aAAa,EAAE,aAAa,aAAa,KAAK,IAAI,EAAE;QACnH,eAAe,aAAa,KAAK;QACjC,YAAY,aAAa,aAAa,EAAE,cAAc,aAAa,aAAa,EAAE;IACpF;IAEA,IAAI,gBAAgB;QAClB,cAAc,EAAE,SAAS;QACzB,mFAAmF;QACnF,eAAe;QACf,YAAY;IACd,OAAO,IAAI,WAAW;QACpB,yCAAyC;QACzC,cAAc,UAAU,SAAS,IAAI,UAAU,QAAQ,IAAI,CAAC,eAAgB,aAAa,aAAa,EAAE,QAAQ,aAAa,aAAa,EAAE,YAAa,EAAE,KAAK,UAAU,KAAK;QAC/K,eAAe,UAAU,KAAK;QAC9B,sHAAsH;QACtH,YAAY,UAAU,UAAU,IAAI,CAAC,eAAgB,aAAa,aAAa,EAAE,cAAc,aAAa,aAAa,EAAE,UAAW,IAAI;IAC5I;IACA,mHAAmH;IAEnH,qBACE,8OAAC;QAAI,WAAU;;YAEZ,mCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAW,EAAE,mBAAmB;;;;;;kCAChD,8OAAC;wBAAI,WAAU;;;;;;;;;;;uBAEf,CAAC,gBAAgB,CAAC,0BACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAET,EAAE,SAAS;;;;;;kCAEd,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCAET,EAAE,YAAY;;;;;;;;;;;qCAInB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAW,EAAE,gBAAgB;;;;;;oBAC5C,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK;4BACL,IAAI;4BACJ,OAAM;4BACN,OAAO;gCAAE,WAAW;4BAAQ;4BAC5B,QAAQ;;;;;;;;;;6CAIZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,MAAK;4BAAO,OAAM;4BAA6B,WAAU;;8CACvG,8OAAC;oCAAK,GAAE;oCACN,QAAO;oCAAe,aAAY;oCAAM,eAAc;oCAAQ,gBAAe;;;;;;8CAC/E,8OAAC;oCAAK,GAAE;oCACN,QAAO;oCAAe,aAAY;oCAAM,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;YAQxF,CAAC,sBAAsB,CAAC,gBAAgB,SAAS,mBAChD,8OAAC;gBAAI,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAS;;kCAElC,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,mCACC,8OAAC;wCAAI,WAAU;;;;;+CACb,0BACF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK;4CACL,KAAK;4CACL,IAAI;4CACJ,OAAM;4CACN,OAAO;gDAAE,WAAW;4CAAQ;;;;;;;;;;6DAIhC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,OAAM;4CAA6B,WAAU;;8DACvG,8OAAC;oDAAK,GAAE;oDACN,QAAO;oDAAe,aAAY;oDAAM,eAAc;oDAAQ,gBAAe;;;;;;8DAC/E,8OAAC;oDAAK,GAAE;oDACN,QAAO;oDAAe,aAAY;oDAAM,eAAc;oDAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;8CAKvF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;4CAA6C,OAAO;sDAAc;;;;;;wCAC9E,8BAAgB,8OAAC;4CAAE,WAAU;4CAAiC,OAAO;sDAAe;;;;;;;;;;;;;;;;;;;;;;;kCAM3F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAA6B,OAAM;wCAA6B,SAAQ;wCAAY,MAAK;wCAAO,QAAO;wCAAe,aAAY;wCAAI,eAAc;wCAAQ,gBAAe;;0DACxL,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAI,GAAE;;;;;;;;;;;;oCAE1B,EAAE,sBAAsB;;;;;;;0CAG3B,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC,uNAAA,CAAA,gBAAa;wCACZ,WAAU;wCACV,eAAY;;;;;;oCAEb,EAAE,YAAY;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;oCAA6B,OAAM;oCAA6B,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;sDACxL,8OAAC;4CAAK,GAAE;;;;;;sDACR,8OAAC;4CAAS,QAAO;;;;;;sDACjB,8OAAC;4CAAK,IAAG;4CAAK,IAAG;4CAAK,IAAG;4CAAI,IAAG;;;;;;;;;;;;gCAEjC,EAAE,UAAU;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA0C,EAAE,SAAS;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gDAC1C,aAAa,OAAO,CAAC,SAAS;gDAC9B,gBAAgB;4CAClB;4CACA,WAAW,CAAC,+BAA+B,EAAE,iBAAiB,UAAU,yCAAyC,kBAAkB,iBAAiB,CAAC;4CACrJ,cAAY,EAAE,cAAc;sDAE5B,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,WAAW,CAAC,QAAQ,EAAE,iBAAiB,UAAU,kBAAkB,iBAAiB;gDAAE,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;kEACvO,8OAAC;wDAAO,IAAG;wDAAK,IAAG;wDAAK,GAAE;;;;;;kEAC1B,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;;;;;;kEAChC,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;kEACjC,8OAAC;wDAAK,IAAG;wDAAO,IAAG;wDAAO,IAAG;wDAAO,IAAG;;;;;;kEACvC,8OAAC;wDAAK,IAAG;wDAAQ,IAAG;wDAAQ,IAAG;wDAAQ,IAAG;;;;;;kEAC1C,8OAAC;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEAC/B,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;kEACjC,8OAAC;wDAAK,IAAG;wDAAO,IAAG;wDAAQ,IAAG;wDAAO,IAAG;;;;;;kEACxC,8OAAC;wDAAK,IAAG;wDAAQ,IAAG;wDAAO,IAAG;wDAAQ,IAAG;;;;;;;;;;;;;;;;;sDAG7C,8OAAC;4CACC,SAAS;gDACP,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gDACvC,aAAa,OAAO,CAAC,SAAS;gDAC9B,gBAAgB;4CAClB;4CACA,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,SAAS,yDAAyD,kBAAkB,kBAAkB,CAAC;4CACxK,cAAY,EAAE,aAAa;sDAE3B,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,WAAW,CAAC,QAAQ,EAAE,iBAAiB,SAAS,kBAAkB,iBAAiB;gDAAE,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;0DACtO,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,SAAS;gDACP,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,EAAE;oDAClF,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;gDACzC,OAAO;oDACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gDAC5C;gDACA,aAAa,OAAO,CAAC,SAAS;gDAC9B,gBAAgB;4CAClB;4CACA,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,WAAW,yCAAyC,kBAAkB,kBAAkB,CAAC;4CAC1J,cAAY,EAAE,eAAe;sDAE7B,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,WAAW,CAAC,QAAQ,EAAE,iBAAiB,WAAW,kBAAkB,iBAAiB;gDAAE,SAAQ;gDAAY,MAAK;gDAAO,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;kEACxO,8OAAC;wDAAK,GAAE;wDAAI,GAAE;wDAAI,OAAM;wDAAK,QAAO;wDAAK,IAAG;wDAAI,IAAG;;;;;;kEACnD,8OAAC;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;kEAChC,8OAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;uCAEe", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/workspace/github/alltomarkdown_frontend/src/components/layout/ClientLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react'; // Added useEffect import\r\n\r\n// Define a CSS keyframes animation for the auth loading pulse\r\nconst authLoadingPulseAnimation = `\r\n@keyframes authLoadingPulse {\r\n  0% {\r\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\r\n  }\r\n  50% {\r\n    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */\r\n  }\r\n  100% {\r\n    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */\r\n  }\r\n}\r\n\r\n.auth-loading-pulse {\r\n  animation: authLoadingPulse 1.5s ease-in-out infinite;\r\n}\r\n\r\n.dark .auth-loading-pulse {\r\n  animation-name: authLoadingPulseDark;\r\n}\r\n\r\n@keyframes authLoadingPulseDark {\r\n  0% {\r\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\r\n  }\r\n  50% {\r\n    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */\r\n  }\r\n  100% {\r\n    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */\r\n  }\r\n}\r\n`;\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useTranslation } from 'react-i18next';\r\nimport I18nProvider from '../i18n/I18nProvider';\r\nimport LanguageSwitcher from '../i18n/LanguageSwitcher';\r\nimport UserProfileMenu from '../auth/UserProfileMenu';\r\nimport { useAuth } from '../../hooks/useAuth';\r\n\r\ninterface ClientLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nfunction ClientLayoutContent({ children }: { children: React.ReactNode }) {\r\n  const { t } = useTranslation('common');\r\n  const { user, isLoading } = useAuth();\r\n  const pathname = usePathname();\r\n\r\n  // Add the auth loading pulse animation styles to the document\r\n  useEffect(() => {\r\n    // Create a style element\r\n    const styleElement = document.createElement('style');\r\n    styleElement.innerHTML = authLoadingPulseAnimation;\r\n\r\n    // Add it to the document head\r\n    document.head.appendChild(styleElement);\r\n\r\n    // Clean up on unmount\r\n    return () => {\r\n      document.head.removeChild(styleElement);\r\n    };\r\n  }, []);\r\n\r\n  // For debugging purposes\r\n  useEffect(() => {\r\n    console.log('ClientLayout: isLoading:', isLoading);\r\n    console.log('ClientLayout: user object:', user);\r\n  }, [isLoading, user]);\r\n\r\n  const getLinkClassName = (href: string) => {\r\n    const isActive = pathname === href;\r\n    return `px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n      isActive ? 'bg-gray-200 text-blue-700' : 'text-gray-600 hover:bg-gray-100 hover:text-blue-600'\r\n    }`;\r\n  };\r\n\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className=\"antialiased\"\r\n      >\r\n        <header className=\"main-header shadow-md fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-white/90\">\r\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"flex justify-between h-16 items-center\">\r\n              <div className=\"flex-shrink-0 flex items-center\">\r\n                <Link href=\"/\" className=\"text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors\">\r\n                  {t('siteName')}\r\n                </Link>\r\n              </div>\r\n              <div className=\"flex items-center justify-between flex-1 pl-6 md:pl-10 lg:pl-16\"> {/* Responsive left padding */}\r\n                <nav className=\"hidden md:flex space-x-6 lg:space-x-8 items-baseline\"> {/* Responsive spacing between nav items */}\r\n                  <Link href=\"/\" className={getLinkClassName('/')}>\r\n                    {t('home')}\r\n                  </Link>\r\n                  <Link href=\"/convert\" className={getLinkClassName('/convert')}>\r\n                    {t('convert')}\r\n                  </Link>\r\n                  <Link href=\"/pricing\" className={getLinkClassName('/pricing')}>\r\n                    {t('pricing')}\r\n                  </Link>\r\n                </nav>\r\n\r\n                {/* Right side items: Language switcher and user/login */}\r\n                <div className=\"flex items-center space-x-3 md:space-x-4 lg:space-x-5\">\r\n                  {/* Language Switcher - now positioned to the left of user/login */}\r\n                  <LanguageSwitcher />\r\n\r\n                  {/* UserProfileMenu rendering logic - now handles all states: loading, logged in, logged out */}\r\n                  {isLoading ? (\r\n                    <div className=\"h-10 w-10 rounded-full auth-loading-pulse\"></div>\r\n                  ) : (\r\n                    <UserProfileMenu user={user} /> // Pass user (can be null) to UserProfileMenu\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n        <main className=\"pt-16\"> {/* Add padding-top to account for fixed header */}\r\n          {children}\r\n        </main>\r\n        <footer className=\"bg-white\">\r\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"border-t border-gray-200 py-8 text-center text-sm text-gray-500\">\r\n              <p>{t('footer', { year: new Date().getFullYear() })}</p>\r\n            </div>\r\n          </div>\r\n        </footer>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n\r\nconst ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {\r\n  return (\r\n    <I18nProvider>\r\n      <ClientLayoutContent>{children}</ClientLayoutContent>\r\n    </I18nProvider>\r\n  );\r\n};\r\n\r\nexport default ClientLayout;\r\n"], "names": [], "mappings": ";;;;AAEA,oVAA0C,yBAAyB;AAoCnE;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AA5CA;;;AAIA,8DAA8D;AAC9D,MAAM,4BAA4B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCnC,CAAC;;;;;;;;AAaD,SAAS,oBAAoB,EAAE,QAAQ,EAAiC;IACtE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,SAAS,GAAG;QAEzB,8BAA8B;QAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,sBAAsB;QACtB,OAAO;YACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,4BAA4B;QACxC,QAAQ,GAAG,CAAC,8BAA8B;IAC5C,GAAG;QAAC;QAAW;KAAK;IAEpB,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,aAAa;QAC9B,OAAO,CAAC,2DAA2D,EACjE,WAAW,8BAA8B,uDACzC;IACJ;IAEA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAU;;8BAEV,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACtB,EAAE;;;;;;;;;;;8CAGP,8OAAC;oCAAI,WAAU;;wCAAkE;sDAC/E,8OAAC;4CAAI,WAAU;;gDAAuD;8DACpE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAW,iBAAiB;8DACxC,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAW,iBAAiB;8DAC/C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAW,iBAAiB;8DAC/C,EAAE;;;;;;;;;;;;sDAKP,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC,uIAAA,CAAA,UAAgB;;;;;gDAGhB,0BACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC,sIAAA,CAAA,UAAe;oDAAC,MAAM;;;;;yDAAS,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOzF,8OAAC;oBAAK,WAAU;;wBAAQ;wBACrB;;;;;;;8BAEH,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAG,EAAE,UAAU;oCAAE,MAAM,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D;AAEA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,8OAAC,mIAAA,CAAA,UAAY;kBACX,cAAA,8OAAC;sBAAqB;;;;;;;;;;;AAG5B;uCAEe", "debugId": null}}]}