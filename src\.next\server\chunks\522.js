exports.id=522,exports.ids=[522],exports.modules={673:(e,t,r)=>{"use strict";r.d(t,{default:()=>k});var n=r(687),o=r(3210),a=r(5814),s=r.n(a),i=r(6189),l=r(6457),d=r(8262);let c=({children:e})=>{let[t]=(0,o.useState)((0,d.A)());return(0,o.useEffect)(()=>{let e=localStorage.getItem("language");e?t.changeLanguage(e):navigator.language.startsWith("zh")&&(t.changeLanguage("zh"),localStorage.setItem("language","zh"))},[t]),(0,n.jsx)(l.xC,{i18n:t,children:e})};var u=r(4279),m=r(143);let g=`
.language-dropdown-container:hover .language-dropdown {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}
`,h=()=>{let{t:e,i18n:t}=(0,l.Bd)("common"),r=(0,i.useRouter)();(0,o.useEffect)(()=>{let e=document.createElement("style");return e.innerHTML=g,document.head.appendChild(e),()=>{document.head.removeChild(e)}},[]);let a=e=>{t.changeLanguage(e),localStorage.setItem("language",e),r.refresh()},s=e("zh"===t.language?"chinese":"english");return(0,n.jsxs)("div",{className:"relative block text-left language-dropdown-container",children:[(0,n.jsx)("div",{children:(0,n.jsxs)("button",{id:"language-menu-button",type:"button",className:"inline-flex items-center gap-x-1 rounded-md bg-transparent px-2 py-1.5 text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2","aria-label":e("languageSwitcher"),"aria-haspopup":"true",children:[(0,n.jsx)(m.A,{className:"h-5 w-5","aria-hidden":"true"}),(0,n.jsx)("span",{className:"hidden sm:inline",children:s}),(0,n.jsx)(u.A,{className:"h-5 w-5","aria-hidden":"true"})]})}),(0,n.jsx)("div",{className:function(...e){return e.filter(Boolean).join(" ")}("language-dropdown opacity-0 scale-95 pointer-events-none","absolute right-0 z-10 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-blue-600 ring-opacity-5 focus:outline-none overflow-hidden transition-all duration-100 ease-out"),role:"menu","aria-orientation":"vertical","aria-labelledby":"language-menu-button",style:{top:"100%"},children:(0,n.jsxs)("div",{className:"py-1",children:[(0,n.jsx)("button",{onClick:()=>a("en"),className:"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors",role:"menuitem",children:"English"}),(0,n.jsx)("button",{onClick:()=>a("zh"),className:"hover:bg-blue-50 hover:text-blue-700 active:bg-blue-50 active:text-blue-700 text-gray-700 block w-full px-4 py-2 text-left text-sm transition-colors",role:"menuitem",children:"中文"})]})})]})};var p=r(7031),x=r(7163),y=r(474),b=r(8622);let v=`
@keyframes avatarPulse {
  0% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
  50% {
    background-color: rgba(229, 231, 235, 0.9); /* gray-200 with opacity */
  }
  100% {
    background-color: rgba(249, 250, 251, 0.8); /* gray-50 with opacity */
  }
}

.avatar-pulse {
  animation: avatarPulse 1.5s ease-in-out infinite;
}

.dark .avatar-pulse {
  animation-name: avatarPulseDark;
}

@keyframes avatarPulseDark {
  0% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
  50% {
    background-color: rgba(75, 85, 99, 0.7); /* gray-600 with opacity */
  }
  100% {
    background-color: rgba(55, 65, 81, 0.5); /* gray-700 with opacity */
  }
}
`,f=({user:e})=>{let{t}=(0,l.Bd)("common"),r=(0,i.useRouter)(),a=(0,x.A)(),[d,c]=(0,o.useState)(null),[u,m]=(0,o.useState)(!1),[g,h]=(0,o.useState)(null),[f,w]=(0,o.useState)("system");(0,o.useEffect)(()=>{let e=document.createElement("style");return e.innerHTML=v,document.head.appendChild(e),()=>{document.head.removeChild(e)}},[]),(0,o.useEffect)(()=>{let e=localStorage.getItem("theme");e&&w(e),"dark"===e?document.documentElement.classList.add("dark"):"light"===e?document.documentElement.classList.remove("dark"):("system"===e||!e)&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),e||(localStorage.setItem("theme","system"),w("system")));let t=window.matchMedia("(prefers-color-scheme: dark)"),r=e=>{"system"===localStorage.getItem("theme")&&(e.matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))};return t.addEventListener("change",r),()=>{t.removeEventListener("change",r)}},[]),(0,o.useEffect)(()=>{let r=async()=>{let e=(0,b.B)();if(!e)return void m(!1);m(!0),h(null);try{let t=new Promise(e=>setTimeout(e,1500)),r=fetch("http://localhost:8000/api/v1/users/me",{headers:{Authorization:`Bearer ${e}`}}),[n]=await Promise.all([r,t]);if(n.ok){let e=await n.json();c(e)}else{let e=await n.json();console.error("Failed to fetch local user data:",n.status,e),h(e.detail||`Error ${n.status}`),n.status}}catch(e){console.error("Error fetching local user data:",e),h(t("errorFetchingUserData","Failed to load user details."))}finally{m(!1)}};e?.email?r():m(!1)},[e,t]);let k=async()=>{try{await a.auth.signOut(),r.push("/"),r.refresh()}catch(e){console.error("Error signing out:",e)}},j=t("user"),A=null,C=null;return e&&(j=e.user_metadata?.name||e.user_metadata?.full_name||e.email||t("user"),A=e.email,C=e.user_metadata?.avatar_url||e.user_metadata?.picture),g?(j=t("error","Error"),A=null,C=null):d&&(j=d.full_name||d.username||(e?e.user_metadata?.name||e.user_metadata?.full_name:"")||d.email,A=d.email,C=d.avatar_url||(e?e.user_metadata?.avatar_url||e.user_metadata?.picture:null)),(0,n.jsxs)("div",{className:"relative inline-block text-left group",children:[u?(0,n.jsxs)("div",{className:"flex items-center justify-center rounded-full focus:outline-none h-10 w-10 cursor-pointer",children:[(0,n.jsx)("span",{className:"sr-only",children:t("loadingUserMenu","Loading user menu")}),(0,n.jsx)("div",{className:"h-10 w-10 rounded-full avatar-pulse"})]}):e||d?(0,n.jsxs)("div",{className:"flex items-center justify-center rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 focus:outline-none transition-colors h-10 w-10 cursor-pointer dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600",children:[(0,n.jsx)("span",{className:"sr-only",children:t("openUserMenu","Open user menu")}),C?(0,n.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:(0,n.jsx)(y.default,{src:C,alt:j,fill:!0,sizes:"40px",style:{objectFit:"cover"},priority:!0})}):(0,n.jsx)("div",{className:"h-10 w-10 rounded-full flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,n.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("path",{d:"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}):(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(s(),{href:"/login",className:"px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:bg-gray-100 hover:text-blue-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-blue-400",children:t("login","Login")}),(0,n.jsx)("button",{onClick:()=>r.push("/register"),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:t("register","Register")})]}),!u&&(e||d)&&(0,n.jsxs)("div",{className:"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-150",style:{marginTop:"0.5rem"},children:[(0,n.jsx)("div",{className:"absolute -top-2 right-0 w-16 h-2 bg-transparent"}),(0,n.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 mr-3",children:u?(0,n.jsx)("div",{className:"h-8 w-8 rounded-full avatar-pulse"}):C?(0,n.jsx)("div",{className:"relative h-8 w-8 rounded-full overflow-hidden",children:(0,n.jsx)(y.default,{src:C,alt:j,fill:!0,sizes:"32px",style:{objectFit:"cover"}})}):(0,n.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(0,n.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("path",{d:"M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M6 20C6 17.2386 8.68629 15 12 15C15.3137 15 18 17.2386 18 20",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",title:j,children:j}),A&&(0,n.jsx)("p",{className:"text-xs text-gray-500 truncate",title:A,children:A})]})]})}),(0,n.jsxs)("div",{className:"py-1",children:[(0,n.jsxs)("a",{href:"/account",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,n.jsxs)("svg",{className:"mr-3 h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),(0,n.jsx)("circle",{cx:"12",cy:"7",r:"4"})]}),t("accountPreferences","Account Preferences")]}),(0,n.jsxs)("a",{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,n.jsx)(p.A,{className:"mr-3 h-5 w-5 text-gray-400","aria-hidden":"true"}),t("settings","Settings")]})]}),(0,n.jsx)("div",{className:"py-1 border-t border-gray-100",children:(0,n.jsxs)("button",{onClick:k,className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,n.jsxs)("svg",{className:"mr-3 h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),(0,n.jsx)("polyline",{points:"16 17 21 12 16 7"}),(0,n.jsx)("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),t("logout","Sign out")]})}),(0,n.jsx)("div",{className:"py-1 border-t border-gray-100",children:(0,n.jsxs)("div",{className:"px-4 py-2",children:[(0,n.jsx)("p",{className:"text-xs font-medium text-gray-500 mb-2",children:t("theme","Theme")}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{onClick:()=>{document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light"),w("light")},className:`p-2 rounded-md bg-white border ${"light"===f?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"} hover:bg-gray-50`,"aria-label":t("lightTheme","Light theme"),children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 ${"light"===f?"text-blue-600":"text-gray-700"}`,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"5"}),(0,n.jsx)("line",{x1:"12",y1:"1",x2:"12",y2:"3"}),(0,n.jsx)("line",{x1:"12",y1:"21",x2:"12",y2:"23"}),(0,n.jsx)("line",{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}),(0,n.jsx)("line",{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}),(0,n.jsx)("line",{x1:"1",y1:"12",x2:"3",y2:"12"}),(0,n.jsx)("line",{x1:"21",y1:"12",x2:"23",y2:"12"}),(0,n.jsx)("line",{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}),(0,n.jsx)("line",{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"})]})}),(0,n.jsx)("button",{onClick:()=>{document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark"),w("dark")},className:`p-2 rounded-md bg-gray-900 border ${"dark"===f?"border-blue-500 ring-2 ring-blue-500 ring-opacity-50":"border-gray-700"} hover:bg-gray-800`,"aria-label":t("darkTheme","Dark theme"),children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 ${"dark"===f?"text-blue-400":"text-gray-200"}`,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,n.jsx)("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"})})}),(0,n.jsx)("button",{onClick:()=>{window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("theme","system"),w("system")},className:`p-2 rounded-md bg-gray-100 border ${"system"===f?"border-blue-500 ring-2 ring-blue-200":"border-gray-200"} hover:bg-gray-200`,"aria-label":t("systemTheme","System theme"),children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-4 w-4 ${"system"===f?"text-blue-600":"text-gray-700"}`,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,n.jsx)("rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",ry:"2"}),(0,n.jsx)("line",{x1:"8",y1:"21",x2:"16",y2:"21"}),(0,n.jsx)("line",{x1:"12",y1:"17",x2:"12",y2:"21"})]})})]})]})})]})]})};function w({children:e}){let{t}=(0,l.Bd)("common"),{user:r,isLoading:o}=(0,b.A)(),a=(0,i.usePathname)(),d=e=>`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a===e?"bg-gray-200 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-blue-600"}`;return(0,n.jsx)("html",{lang:"en",children:(0,n.jsxs)("body",{className:"antialiased",children:[(0,n.jsx)("header",{className:"main-header shadow-md fixed top-0 left-0 right-0 z-50 backdrop-blur-sm bg-white/90",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16 items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,n.jsx)(s(),{href:"/",className:"text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors",children:t("siteName")})}),(0,n.jsxs)("div",{className:"flex items-center justify-between flex-1 pl-6 md:pl-10 lg:pl-16",children:[" ",(0,n.jsxs)("nav",{className:"hidden md:flex space-x-6 lg:space-x-8 items-baseline",children:[" ",(0,n.jsx)(s(),{href:"/",className:d("/"),children:t("home")}),(0,n.jsx)(s(),{href:"/convert",className:d("/convert"),children:t("convert")}),(0,n.jsx)(s(),{href:"/pricing",className:d("/pricing"),children:t("pricing")})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 md:space-x-4 lg:space-x-5",children:[(0,n.jsx)(h,{}),o?(0,n.jsx)("div",{className:"h-10 w-10 rounded-full auth-loading-pulse"}):(0,n.jsx)(f,{user:r})]})]})]})})}),(0,n.jsxs)("main",{className:"pt-16",children:[" ",e]}),(0,n.jsx)("footer",{className:"bg-white",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsx)("div",{className:"border-t border-gray-200 py-8 text-center text-sm text-gray-500",children:(0,n.jsx)("p",{children:t("footer",{year:new Date().getFullYear()})})})})})]})})}let k=({children:e})=>(0,n.jsx)(c,{children:(0,n.jsx)(w,{children:e})})},2017:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},2704:()=>{},5569:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7163:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1543);let o=null,a=()=>{if(o)return o;let e="https://rpzceoedurujspnnyvkm.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJwemNlb2VkdXJ1anNwbm55dmttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1OTkwNDgsImV4cCI6MjA2MjE3NTA0OH0._zuv48OJiram1Q_QXndbl6exkL3P8qrgI_MfybQzCEo";if(!e)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_URL. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");if(!t)throw console.error("Error: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Check your .env.local file and Next.js configuration."),Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");return o=(0,n.UU)(e,t,{auth:{debug:!0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},global:{fetch:async(e,t)=>{console.log(`[Supabase Debug] Fetch request to: ${e}`),console.log("[Supabase Debug] Fetch options:",t);try{let r=await fetch(e,t);return console.log(`[Supabase Debug] Response status: ${r.status}`),r}catch(e){throw console.error("[Supabase Debug] Fetch error:",e),e}}}}),console.log("[Supabase Debug] Client initialized with URL:",e),o}},7448:(e,t,r)=>{Promise.resolve().then(r.bind(r,673))},7720:(e,t,r)=>{Promise.resolve().then(r.bind(r,8271))},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>a});var n=r(7413);r(2704);var o=r(8271);let a={title:"All to Markdown - Convert Any Document to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Make your content truly understandable by AI models like ChatGPT and Claude.",keywords:"markdown converter, ai friendly format, chatgpt friendly, pdf to markdown, word to markdown, excel to markdown, html to markdown, image to markdown, website to markdown, url to markdown, doc to markdown, docx to markdown, xlsx to markdown, document conversion, ai readable",openGraph:{title:"All to Markdown - Convert Any Document to AI-Friendly Markdown",description:"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis.",url:"https://alltomarkdown.com",siteName:"All to Markdown",type:"website"},twitter:{card:"summary_large_image",title:"All to Markdown - Convert Any Document to AI-Friendly Markdown",description:"Transform documents into AI-friendly Markdown format. Help AI models truly understand your content for better responses and analysis."}};function s({children:e}){return(0,n.jsx)(o.default,{children:e})}},8262:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(6755),o=r(6457);let a={common:{siteName:"All to Markdown",home:"Home",convert:"Convert",pricing:"Pricing",login:"Login",logout:"Logout",profile:"Profile",subscription:"Subscription",user:"User",footer:"\xa9 {{year}} All to Markdown. All rights reserved.",languageSwitcher:"Language",english:"English",chinese:"中文",userGroupPlaceholder:"N/A",paymentStatusPlaceholder:"N/A",openUserMenu:"Open user menu",userGroup:"User Group",paymentStatus:"Payment Status"},home:{title:"Convert Documents to AI-Friendly Markdown",description:"Transform PDF, Word, Excel, HTML, images, websites, and URLs into clean, AI-friendly Markdown. Help AI models truly understand your content for better responses and analysis.",startConverting:"Start Converting",viewPricing:"View Pricing",aiTitle:"Why Markdown is Perfect for AI",aiDescription:"Markdown provides a clean, structured format that AI models can easily understand and process, leading to better responses and more accurate analysis.",clearStructure:"Clear Structure",clearStructureDesc:"Markdown's simple structure helps AI models understand document organization, headings, lists, and emphasis, leading to better comprehension.",enhancedResponses:"Enhanced AI Responses",enhancedResponsesDesc:"When feeding Markdown to AI like ChatGPT or Claude, you'll get more accurate responses because the AI can better understand context and relationships in your content.",noFormatting:"No Formatting Noise",noFormattingDesc:"Unlike PDFs or Word documents, Markdown removes complex formatting that can confuse AI models, focusing on content and meaning rather than appearance.",convertAnyFormat:"Convert any document format to clean Markdown",convertDesc:"Our powerful conversion engine supports a wide range of document formats. We handle complex formatting, tables, images, and ensure your Markdown output is clean and ready to use.",supportedFormats:"Supported File Formats",easyUpload:"Easy File Upload",easyUploadDesc:"Drag and drop your files or use the file browser. Support for PDF, Word, Excel, HTML, images, websites, URLs and more.",advancedOptions:"Advanced Options",advancedOptionsDesc:"Customize your Markdown output with options for different Markdown flavors, image handling, and more.",batchProcessing:"Batch Processing",batchProcessingDesc:"Convert multiple files at once and download them individually or as a zip archive.",fastConversion:"Fast Conversion",fastConversionDesc:"Our optimized conversion engine processes your documents quickly, saving you time and effort."},convert:{title:"Convert to AI-Friendly Markdown",description:"Upload your documents and convert them to clean, AI-friendly Markdown. Help AI models like ChatGPT and Claude truly understand your content for better responses.",aiTip:"AI-Friendly Format: Markdown is the preferred format for AI models like ChatGPT and Claude. Converting your documents to Markdown helps AI better understand your content structure, leading to more accurate responses and analysis.",startConversion:"Start Conversion",options:"Conversion Options",markdownFlavor:"Markdown Flavor",markdownFlavorDesc:"Select the Markdown specification to follow",aiOptimized:"AI-Optimized Format",aiOptimizedDesc:"Optimize output for AI models like ChatGPT and Claude",advancedOptions:"Advanced Options",imageHandling:"Image Handling",imageHandlingDesc:"How to handle images in the converted Markdown",enableImageDescription:"Generate descriptive text for images",enableImageDescriptionDesc:"Generate descriptions for image content. You can choose to keep the original image and use the description as alt text, or replace the image entirely with the descriptive text, to help AI large models better understand your document.",imageDescriptionAttachmentMode:"Description Attachment Method:",attachmentModeKeepImage:"Keep Image",attachmentModeReplaceImage:"Replace Image",tableHandling:"Table Handling",tableHandlingDesc:"Table formatting style in the converted Markdown",successMessage:"Your file has been successfully converted to Markdown!",successMessagePlural:"{{count}} files have been successfully converted to Markdown!",aiSuccessTip:"Your content is now in an AI-friendly format. Copy and paste it into ChatGPT, Claude, or other AI tools for better understanding and responses.",download:"Download .md",downloadAll:"Download All (.zip)"},pricing:{title:"Pricing",subtitle:"Pricing plans for all needs",description:"Choose the perfect plan for your document conversion needs. All plans include our core conversion features.",free:"Free",pro:"Pro",enterprise:"Enterprise",mostPopular:"Most popular",monthly:"/month",yearly:"/year",getStarted:"Get started",subscribe:"Subscribe"},auth:{loginTitle:"Login",loginDescription:"Login to your account",emailLabel:"Email",passwordLabel:"Password",loginButton:"Login",forgotPassword:"Forgot password?",noAccount:"Don't have an account?",signUpLink:"Sign up",registerTitle:"Sign Up",registerDescription:"Create a new account",confirmPasswordLabel:"Confirm Password",registerButton:"Sign Up",hasAccount:"Already have an account?",signInLink:"Sign in",orContinueWith:"Or continue with",github:"GitHub",google:"Google",magicLinkSent:"Magic link sent!",checkYourEmail:"Check your email for the magic link to login."}},s={common:{siteName:"All to Markdown",home:"首页",convert:"转换",pricing:"价格",login:"登录",logout:"退出登录",profile:"个人资料",subscription:"订阅状态",user:"用户",footer:"\xa9 {{year}} All to Markdown. 保留所有权利。",languageSwitcher:"语言",english:"English",chinese:"中文",userGroupPlaceholder:"暂无",paymentStatusPlaceholder:"暂无",openUserMenu:"打开用户菜单",userGroup:"用户组",paymentStatus:"支付状态",register:"注册"},home:{title:"将文档转换为AI友好的Markdown格式",description:"将PDF、Word、Excel、HTML、图片、网站和URL转换为清晰、AI友好的Markdown格式。帮助AI模型真正理解您的内容，获得更好的响应和分析。",startConverting:"开始转换",viewPricing:"查看价格",aiTitle:"为什么Markdown对AI来说是完美的",aiDescription:"Markdown提供了一种干净、结构化的格式，AI模型可以轻松理解和处理，从而带来更好的响应和更准确的分析。",clearStructure:"清晰的结构",clearStructureDesc:"Markdown的简单结构帮助AI模型理解文档组织、标题、列表和强调，从而更好地理解内容。",enhancedResponses:"增强的AI响应",enhancedResponsesDesc:"当将Markdown输入到ChatGPT或Claude等AI时，您将获得更准确的响应，因为AI可以更好地理解内容中的上下文和关系。",noFormatting:"没有格式噪音",noFormattingDesc:"与PDF或Word文档不同，Markdown去除了可能混淆AI模型的复杂格式，专注于内容和含义而非外观。",convertAnyFormat:"将任何文档格式转换为清晰的Markdown",convertDesc:"我们强大的转换引擎支持各种文档格式。我们处理复杂的格式、表格、图像，并确保您的Markdown输出干净且随时可用。",supportedFormats:"支持的文件格式",easyUpload:"轻松上传文件",easyUploadDesc:"拖放文件或使用文件浏览器。支持PDF、Word、Excel、HTML、图片、网站、URL等多种格式。",advancedOptions:"高级选项",advancedOptionsDesc:"使用不同的Markdown风格、图像处理等选项自定义您的Markdown输出。",batchProcessing:"批量处理",batchProcessingDesc:"一次转换多个文件，并单独下载或作为zip存档下载。",fastConversion:"快速转换",fastConversionDesc:"我们优化的转换引擎快速处理您的文档，节省您的时间和精力。"},convert:{title:"转换为AI友好的Markdown",description:"上传您的文档并将其转换为清晰、AI友好的Markdown。帮助ChatGPT和Claude等AI模型真正理解您的内容，获得更好的响应。",aiTip:"AI友好格式：Markdown是ChatGPT和Claude等AI模型的首选格式。将文档转换为Markdown有助于AI更好地理解您的内容结构，从而获得更准确的响应和分析。",startConversion:"开始转换",options:"转换选项",markdownFlavor:"Markdown风格",markdownFlavorDesc:"选择要遵循的Markdown规范",aiOptimized:"AI优化格式",aiOptimizedDesc:"为ChatGPT和Claude等AI模型优化输出",advancedOptions:"高级选项",imageHandling:"图像处理",imageHandlingDesc:"如何处理转换后的Markdown中的图像",enableImageDescription:"为图片生成描述性文字",enableImageDescriptionDesc:"为图片内容生成描述，您可以选择保留原图片并将描述作为图片的 alt 文本，或用描述文本直接替换图片，以方便AI大模型更好的理解您的文档。",imageDescriptionAttachmentMode:"描述文字附加方式:",attachmentModeKeepImage:"保留图片",attachmentModeReplaceImage:"替换图片",tableHandling:"表格处理",tableHandlingDesc:"转换后的Markdown中的表格格式样式",successMessage:"您的文件已成功转换为Markdown！",successMessagePlural:"{{count}}个文件已成功转换为Markdown！",aiSuccessTip:"您的内容现在是AI友好的格式。将其复制并粘贴到ChatGPT、Claude或其他AI工具中，以获得更好的理解和响应。",download:"下载.md",downloadAll:"下载全部(.zip)"},pricing:{title:"价格",subtitle:"满足所有需求的价格计划",description:"选择适合您文档转换需求的完美计划。所有计划都包括我们的核心转换功能。",free:"免费版",pro:"专业版",enterprise:"企业版",mostPopular:"最受欢迎",monthly:"/月",yearly:"/年",getStarted:"开始使用",subscribe:"订阅"},auth:{loginTitle:"登录",loginDescription:"登录您的账户",emailLabel:"邮箱",passwordLabel:"密码",loginButton:"登录",forgotPassword:"忘记密码？",noAccount:"还没有账户？",signUpLink:"注册",registerTitle:"注册",registerDescription:"创建一个新账户",confirmPasswordLabel:"确认密码",registerButton:"注册",hasAccount:"已有账户？",signInLink:"登录",orContinueWith:"或继续使用",github:"GitHub",google:"Google",magicLinkSent:"魔法链接已发送！",checkYourEmail:"请检查您的邮箱以获取魔法链接进行登录。"}},i=(e="en",t=["common","auth"])=>{let r=(0,n.Q_)();return r.use(o.r9).init({lng:e,ns:t,fallbackLng:"en",interpolation:{escapeValue:!1},resources:{en:{...a},zh:{...s}}}),r}},8271:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\workspace\\\\github\\\\alltomarkdown_frontend\\\\src\\\\components\\\\layout\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\workspace\\github\\alltomarkdown_frontend\\src\\components\\layout\\ClientLayout.tsx","default")},8622:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,B:()=>a});var n=r(3210);function o(){let[e,t]=(0,n.useState)({user:null,session:null,isLoading:!0});return e}function a(){return null}r(7163)}};