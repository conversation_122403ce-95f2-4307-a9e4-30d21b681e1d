<svg width="100" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg" font-family="Arial, sans-serif">

  <!-- Original Long Document -->
  <g id="originalLongDoc" transform="translate(0, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Long Document</text>

    <!-- Many lines of text to show length -->
    <line x1="20" y1="35" x2="80" y2="35" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="40" x2="75" y2="40" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="45" x2="80" y2="45" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="50" x2="70" y2="50" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="55" x2="80" y2="55" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="60" x2="75" y2="60" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="65" x2="80" y2="65" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="70" x2="65" y2="70" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="75" x2="80" y2="75" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="80" x2="70" y2="80" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="85" x2="80" y2="85" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="90" x2="75" y2="90" stroke="#757575" stroke-width="1.5"/>
    <line x1="20" y1="95" x2="60" y2="95" stroke="#757575" stroke-width="1.5"/>

    <animateTransform attributeName="transform" type="translate"
                      values="0,0; -100,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="1;0"
             begin="0s" dur="2s" fill="freeze" />
  </g>

  <!-- Summarized Short Document -->
  <g id="summarizedShortDoc" opacity="0" transform="translate(100, 0)">
    <rect x="10" y="10" width="80" height="100" fill="#E0E0E0" rx="2" ry="2"/>
    <text x="50" y="25" font-size="7" fill="#424242" text-anchor="middle">Summary</text>

    <!-- Summary header -->
    <rect x="20" y="35" width="50" height="6" fill="#4CAF50"/>
    <text x="45" y="41" font-size="5" fill="#FFFFFF" text-anchor="middle" font-weight="bold">Summary</text>

    <!-- Key points -->
    <circle cx="25" cy="55" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="55" x2="75" y2="55" stroke="#4CAF50" stroke-width="2"/>

    <circle cx="25" cy="65" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="65" x2="70" y2="65" stroke="#4CAF50" stroke-width="2"/>

    <circle cx="25" cy="75" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="75" x2="80" y2="75" stroke="#4CAF50" stroke-width="2"/>

    <circle cx="25" cy="85" r="1.5" fill="#4CAF50"/>
    <line x1="30" y1="85" x2="65" y2="85" stroke="#4CAF50" stroke-width="2"/>

    <animateTransform attributeName="transform" type="translate"
                      values="100,0; 0,0"
                      begin="0s" dur="2s" fill="freeze" />
    <animate attributeName="opacity"
             values="0;1"
             begin="0s" dur="2s" fill="freeze" />
  </g>
</svg>
